services:

  app:
    build:
      context: .
      target: dev
      dockerfile: ./Dockerfile
      args:
        INSTALL_DEV: 'true'
      ssh:
        - default
    container_name: rpm_registration_api
    restart: unless-stopped
    tty: true
    env_file: .env
    ports:
      - "10000:8000"
    entrypoint: /app/entrypoint.sh
    volumes:
      - .:/app/
    networks:
      - participant-network

  db:
    image: postgres:14
    container_name: rpm_registration_api_db
    restart: unless-stopped
    tty: true
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_DB=rpm_single
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - rpm_registration_db:/var/lib/postgresql
    networks:
      - participant-network

  localstack:
    container_name: localstack
    build:
      context: .
      dockerfile: localstack/Dockerfile.localstack
    ports:
      - "4566:4566"  # Edge port
    env_file: localstack/.env
    environment:
      - SERVICES=sqs,sns,ses,logs,lambda,ecr,s3
      - LOCALSTACK_HOST=localhost.localstack.cloud:4566
      #- DEBUG=1  # Optional: to enable debug mode
      - SMTP_HOST=localemail:1080
      - SMTP_USER=<EMAIL>
      - SMTP_PASS=test
      - LAMBDA_IGNORE_ARCHITECTURE=1
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./localstack/local-aws-init.sh:/etc/localstack/init/ready.d/local-aws-init.sh
      - "${SSH_AUTH_SOCK}:${SSH_AUTH_SOCK}"
    networks:
      - participant-network

  localemail:
    image: maildev/maildev
    container_name: localemail
    ports:
      - "1080:1080"
      - "1025:1025"
    environment:
      - MAILDEV_SMTP_PORT=1080
      - MAILDEV_WEB_PORT=1025
      - MAILDEV_INCOMING_USER=<EMAIL>
      - MAILDEV_INCOMING_PASS=test
    networks:
      - participant-network

volumes:
  rpm_registration_db:
    driver: local
  localstack-data:
    driver: local

networks:
  participant-network:
    name: participant-network
    external: true
