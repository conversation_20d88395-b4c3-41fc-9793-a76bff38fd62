#!/bin/bash

RED='\033[0;31m'
GREEN='\033[0;32m'

while getopts "i" f
do
  case "$f" in
    i) echo -e "${GREEN}Installing test dependencies."; poetry install ;;
    *) echo "usage: $0 [-i]" >&2
       exit 1 ;;
  esac
done

exit_status=0

function mytest {
    if "$@"; then
        echo -e "${GREEN}$* succeed!"
    else
        echo -e "${RED}$* failed!"
        exit_status=1
    fi
}


echo -e "${GREEN}Running unittests."
mytest uv run pytest

exit $exit_status
