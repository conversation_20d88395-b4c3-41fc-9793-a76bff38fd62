# Base stage
FROM ghcr.io/astral-sh/uv:python3.12-alpine AS base

WORKDIR /app

ENV UV_PROJECT_ENVIRONMENT=/usr/local

# Enable bytecode compilation
ENV UV_COMPILE_BYTECODE=1

# Copy from the cache instead of linking since it's a mounted volume
ENV UV_LINK_MODE=copy

# https://docs.python.org/3/using/cmdline.html#envvar-PYTHONDONTWRITEBYTECODE
# Prevents Python from writing .pyc files to disk
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

ARG DD_GIT_REPOSITORY_URL
ARG DD_GIT_COMMIT_SHA
ARG INSTALL_DEV=false
ENV INSTALL_DEV=${INSTALL_DEV}
ENV DD_GIT_REPOSITORY_URL=${DD_GIT_REPOSITORY_URL}
ENV DD_GIT_COMMIT_SHA=${DD_GIT_COMMIT_SHA}
ENV DD_PROFILING_ENABLED=true


# Common dependencies
RUN apk update && apk --no-cache add \
    bash \
    build-base \
    cargo \
    curl \
    git \
    libffi-dev \
    musl-dev \
    openssh-client \
    openssl-dev \
    python3-dev \
    rust \
    && apk cache clean

# SSH configuration for private repositories
RUN mkdir -p -m 0700 ~/.ssh \
    && ssh-keyscan github.com >> ~/.ssh/known_hosts

# Install the project's dependencies using the lockfile and settings
RUN --mount=type=ssh \
    --mount=type=cache,target=/root/.cache/uv \
    --mount=type=bind,source=uv.lock,target=uv.lock \
    --mount=type=bind,source=pyproject.toml,target=pyproject.toml \
    uv sync --frozen --no-install-project --no-dev

ADD . /app
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen --no-dev

ENV PATH="/app/.venv/bin:$PATH"

# Development stage
FROM base AS dev

# Additional dependencies for development
RUN apk --no-cache add \
    build-base \
    openssh-server \
    && apk cache clean

# SSH server configuration
RUN mkdir /var/run/sshd \
    && echo 'root:root' | chpasswd \
    && sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config \
    && sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config \
    && echo 'PasswordAuthentication yes' >> /etc/ssh/sshd_config

ENV TESTING=0

# SSH configuration for runtime
RUN mkdir -p -m 0700 ~/.ssh \
    && ssh-keyscan github.com >> ~/.ssh/known_hosts

# Copy entrypoint script and ensure it's executable
COPY entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

EXPOSE 8000 22 5678 3005

# Pytest stage for running tests
FROM base AS test

# Set the environment variable for testing
ENV TESTING=1

# Install test dependencies
RUN uv sync --frozen --extra test

# Run pytest
CMD ["uv", "run", "pytest", "--disable-warnings"]

# Production stage
FROM base AS prod

# Prevents Python from writing .pyc files to disk
ENV TESTING 0
ENV ENV prod
ENV DEBUG 0

COPY entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

EXPOSE 8000
