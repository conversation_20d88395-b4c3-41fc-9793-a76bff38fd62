name: Deploy

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      image_tag:
        required: true
        type: string
      k8s_env:
        required: true
        type: string
    secrets:
      CI_USERNAME:
        required: true
      CI_PUSH_TOKEN:
        required: true
      CI_USER_EMAIL:
        required: true

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    steps:
      - name: Set environment and extract version
        id: set-env
        run: |
          # Determine the environment name
          if [ "${{ inputs.environment }}" == "development" ]; then
            echo "env_name=dev" >> $GITHUB_OUTPUT

            # Extract the version for development
            VERSION=$(echo "${{ inputs.image_tag }}" | cut -d"-" -f2)
          else
            echo "env_name=${{ inputs.environment }}" >> $GITHUB_OUTPUT

            # Extract the version for production
            VERSION=$(echo "${{ inputs.image_tag }}" | cut -d"-" -f1)
          fi

          # Determine the app name based on the version
          MAJOR_VERSION=$(echo $VERSION | cut -d"." -f1)
          if [ "$MAJOR_VERSION" == "2" ]; then
            echo "app_name=${{ vars.APP_NAME_v2 }}" >> $GITHUB_OUTPUT
          else
            echo "app_name=${{ vars.APP_NAME }}" >> $GITHUB_OUTPUT
          fi

          echo "version=$VERSION" >> $GITHUB_OUTPUT

      - name: Checkout infra repo
        run: |
          git clone https://${{ secrets.CI_USERNAME }}:${{ secrets.CI_PUSH_TOKEN }}@gitlab.com/ciba-health/infra.git
          cd infra/k8s/environments/${{ inputs.k8s_env }}
          kustomize edit set image ${{ vars.CLUSTER_APP }}=${{ vars.GLOBAL_OLD_ECR_REGISTRY }}/${{ vars.APP_NAME }}:${{ inputs.image_tag }}
          cat kustomization.yaml
          git config --global user.email "${{ secrets.CI_USER_EMAIL }}"
          git config --global user.name "${{ secrets.CI_USERNAME }}"
          git commit -am '[CI] ${{ vars.CLUSTER_APP }} ${{ steps.set-env.outputs.env_name }} ${{ inputs.image_tag }} update'
          git push origin main

      - name: Set PR or Branch URL
        id: set-pr-branch-url
        run: |
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            echo "PR_URL=${{ github.event.pull_request.html_url }}" >> $GITHUB_ENV
          else
            BRANCH_NAME=$(echo "${GITHUB_REF}" | sed "s/refs\/heads\///")
            echo "PR_URL=https://github.com/${GITHUB_REPOSITORY}/tree/${BRANCH_NAME}" >> $GITHUB_ENV
          fi
