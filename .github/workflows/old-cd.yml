name: CI/CD Pipeline

on:
  release:
    types: [ published ]

permissions:
  contents: read
  id-token: write
  pull-requests: write

jobs:

  build_and_push:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.get_version.outputs.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up UV
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - name: Install UV
        run: pip install uv
      - name: Get version using script
        id: get_version
        run: |
          version=$(grep -m 1 'version = ' pyproject.toml | awk -F' = ' '{print $2}' | tr -d '"')
          echo "version=$version" >> $GITHUB_ENV
          echo "version=$version" >> $GITHUB_OUTPUT

      - uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.OLD_AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.OLD_AWS_SECRET_KEY }}
          aws-region: us-east-2


      - name: Log in to AWS ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Adding SSH key
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.GLOBAL_SSH_KEY }}


      - name: Build Prod Docker Image
        if: github.event_name == 'release'
        run: |
          DOCKER_BUILDKIT=1 docker build --ssh default \
            --build-arg DD_GIT_REPOSITORY_URL=${{ github.server_url }}/${{ github.repository }} \
            --build-arg DD_GIT_COMMIT_SHA=${{ github.sha }} \
            -f Dockerfile \
            --build-arg INSTALL_DEV=false \
            -t ${{ vars.GLOBAL_OLD_ECR_REGISTRY }}/${{ vars.APP_NAME }}:${{ env.version }}-${{ github.sha }} .
          docker tag ${{ vars.GLOBAL_OLD_ECR_REGISTRY }}/${{ vars.APP_NAME }}:${{ env.version }}-${{ github.sha }} \
          ${{ vars.GLOBAL_OLD_ECR_REGISTRY }}/${{ vars.APP_NAME }}:latest

      - name: Tag and Push Prod Image
        if: github.event_name == 'release'
        run: |
          docker push ${{ vars.GLOBAL_OLD_ECR_REGISTRY }}/${{ vars.APP_NAME }}:${{ env.version }}-${{ github.sha }}
          docker push ${{ vars.GLOBAL_OLD_ECR_REGISTRY }}/${{ vars.APP_NAME }}:latest


  deploy_prod:
    name: Deploy to Prod
    needs: [ build_and_push]
    if: github.event_name == 'release'
    uses: ./.github/workflows/deploy.yaml
    with:
      environment: production
      image_tag: ${{ needs.build_and_push.outputs.version }}-${{ github.sha }}
      k8s_env: prod
    secrets:
      CI_USERNAME: ${{ secrets.CI_USERNAME }}
      CI_PUSH_TOKEN: ${{ secrets.CI_PUSH_TOKEN }}
      CI_USER_EMAIL: ${{ secrets.CI_USER_EMAIL }}
