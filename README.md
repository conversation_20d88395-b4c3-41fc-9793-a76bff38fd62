# RPM registration service

## Development Environment Setup

### Option 1: Dev Container (Recommended)

This project includes a dev container configuration that provides a consistent development environment with all dependencies pre-configured.

#### Prerequisites

1. Install [Docker](https://docs.docker.com/desktop/install/mac-install/)
1. Install [VS Code](https://code.visualstudio.com/)
1. Install the [Remote - Containers](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers) extension in VS Code
1. Setup your [GitHub SSH key](https://docs.github.com/en/authentication/connecting-to-github-with-ssh/generating-a-new-ssh-key-and-adding-it-to-the-ssh-agent)

#### Getting Started with Dev Container

1. Open the project folder in VS Code
1. Click on the green button in the bottom-left corner of VS Code and select "Reopen in Container"
1. Wait for the container to build and start
1. The application will automatically start and be available at [http://localhost:8001](http://localhost:8001)

The dev container includes:

- Python 3.12 with uv package manager
- PostgreSQL database in a separate container
- LocalStack for AWS service emulation
- VS Code extensions pre-configured for Python development
- AWS CLI with SSO support (reuses your host machine's AWS credentials)
- Debugging support

For more details, see the [Dev Container README](.devcontainer/README.md).

### Option 2: Manual Setup (MacOS)

#### Python

Follow [this guide](https://medium.com/marvelous-mlops/the-rightway-to-install-python-on-a-mac-f3146d9d9a32)

#### UV

Follow [this guide](https://docs.astral.sh/uv/)

#### Docker

Follow [this guide](https://docs.docker.com/desktop/install/mac-install/)

#### Setup your GitHub SSH key

Follow [this guide](https://docs.github.com/en/authentication/connecting-to-github-with-ssh/generating-a-new-ssh-key-and-adding-it-to-the-ssh-agent)

## Running the Application

### Using Dev Container

Once inside the dev container, you can run the application with:

```bash
uv run --no-sync uvicorn app.main:app --host=0.0.0.0 --port=8000 --reload
```

Or use the VS Code task "Start FastAPI with Debugger" for debugging.

### Using Docker Compose

```bash
docker compose -f docker-compose.local.yaml up -d
```

## Debugging

### VS Code Dev Container

1. Use the "Python: Remote Attach" launch configuration
1. Start the application with the "Start FastAPI with Debugger" task
1. Set breakpoints and debug as usual

### Built Image

#### VSCode Web

Access the web-based VS Code at [http://localhost:3005](http://localhost:3005)

Access password is available in [entrypoint.sh](entrypoint.sh)

#### JetBrains Gateway

Use [JetBrains Gateway](https://www.jetbrains.com/remote-development/gateway/) for remote development

## Testing

Run unit tests with command:

```shell
uv run pytest
```

## Pre-commit Hooks

This project uses pre-commit hooks to enforce code quality standards. To install the pre-commit hooks, run:

```shell
uv run pre-commit install
```

After installation, the hooks will automatically check your code before each commit.

To manually run all pre-commit checks on your files (recommended before pushing changes or creating a PR), use:

```shell
uv run pre-commit run --all-files
```

> **Note:** Running pre-commit locally helps catch formatting and linting issues early, preventing failures in the CI pipeline.

## Updating Internal Packages

If you make changes that depend on new methods, fixes, or updates in our internal packages (for example, `ciba-iot-etl`), **you must update the lock file** to ensure your application uses the latest version of the package.

This keeps the dependencies consistent and avoids issues from outdated code.

**To upgrade the package version in your lock file, run:**

```shell
uv lock --upgrade-package ciba-iot-etl
```

> - **Note:** This is required whenever you need newly added or changed functionality from `ciba-iot-etl` or other internal packages.
> - **Note:** If you run into package vulnerability issues, follow the same steps described above to fix them.

## Summary of Development Workflow

1. Set up your environment (see above)
1. Make code changes
1. **If your changes depend on updates in internal packages, update the lock file** (see [Updating Internal Packages](#updating-internal-packages))
1. Run the application or tests
1. Run pre-commit hooks locally to validate your changes (see [Pre-commit Hooks](#pre-commit-hooks))
1. Commit and push your changes
