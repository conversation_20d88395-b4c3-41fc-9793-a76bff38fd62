version: '3.8'
services:
  app:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
      args:
        INSTALL_DEV: 'true'
    container_name: ciba_rpm_api_dev
    restart: unless-stopped
    tty: true
    volumes:
      - ..:/app:cached
      - ~/.ssh:/root/.ssh:ro
      - ~/.aws:/root/.aws:ro
    env_file:
      - devcontainer.env
    environment:
      PYTHONPATH: /app
    ports:
      - "8001:8000"
      - "5678:5678"  # For debugpy
    command: sleep infinity
    depends_on:
      - db
    networks:
      - rpm-network

  db:
    image: postgres:14
    container_name: rpm_registration_api_db_dev
    restart: unless-stopped
    tty: true
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - rpm_registration_db_dev:/var/lib/postgresql/data
    networks:
      - rpm-network

  # localstack:
  #   image: localstack/localstack
  #   container_name: rpm_localstack_dev
  #   ports:
  #     - "4566:4566"  # Edge port
  #     - "4571:4571"  # Old edge port
  #   environment:
  #     - SERVICES=sqs
  #     - DEFAULT_REGION=us-east-2
  #     - DEBUG=1
  #   volumes:
  #     - localstack-data-dev:/var/lib/localstack
  #   networks:
  #     - rpm-network

volumes:
  rpm_registration_db_dev:
    driver: local
  # localstack-data-dev:
  #   driver: local

networks:
  rpm-network:
    driver: bridge
