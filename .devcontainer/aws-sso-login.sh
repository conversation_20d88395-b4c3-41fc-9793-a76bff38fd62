#!/bin/bash

# This script helps with AWS SSO login from within the dev container
# It will open a browser on the host machine for authentication

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check if profile is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <profile_name>"
    echo "Example: $0 dev"

    # List available profiles
    echo -e "\nAvailable profiles:"
    aws configure list-profiles
    exit 1
fi

PROFILE=$1

echo "Starting AWS SSO login for profile: $PROFILE"
echo "This will open a browser window on your host machine."
echo "Please complete the authentication process in the browser."

# Start the SSO login process
aws sso login --profile $PROFILE

# Check if login was successful
if [ $? -eq 0 ]; then
    echo "AWS SSO login successful for profile: $PROFILE"
    echo "Your AWS credentials are now available in the container."

    # Display identity information
    echo -e "\nCurrent identity:"
    aws sts get-caller-identity --profile $PROFILE
else
    echo "AWS SSO login failed for profile: $PROFILE"
    exit 1
fi
