#!/bin/bash

# Fix SSH configuration
# if [ -f /app/.devcontainer/fix-ssh.sh ]; then
#   echo "Running SSH fix script..."
#   /app/.devcontainer/fix-ssh.sh
# fi

# Sync dependencies first
echo "Syncing dependencies..."
uv sync

# Load environment variables from .env file if it exists
if [ -f /app/.devcontainer/devcontainer.env ]; then
  echo "Loading environment variables from .env file..."
  set -a
  source /app/.devcontainer/devcontainer.env
  set +a
fi

# Set default database connection parameters if not provided in .env
DB_HOST=${POSTGRES_HOST:-db}
DB_USER=${POSTGRES_USER:-postgres}
DB_PASSWORD=${POSTGRES_PASSWORD}
DB_NAME=${POSTGRES_DB:-postgres}

# Wait for the database to be ready
echo "Waiting for database to be ready..."
until PGPASSWORD="${DB_PASSWORD}" psql -h "${DB_HOST}" -U "${DB_USER}" -d "${DB_NAME}" -c '\q'; do
  echo "Database not ready yet. Waiting..."
  sleep 2
done
echo "Database is ready!"

# Start the application in the background
echo "Starting the application..."
uv run --no-sync uvicorn app.main:app --host=0.0.0.0 --port=8000 --reload &

# Keep the container running
echo "Application is running at http://localhost:8000"
echo "You can now use the application!"

# Return to the shell
exec "$SHELL"
