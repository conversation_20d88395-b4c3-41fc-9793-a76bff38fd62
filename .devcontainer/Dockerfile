FROM ghcr.io/astral-sh/uv:python3.12-bookworm-slim

# Enable bytecode compilation
ENV UV_COMPILE_BYTECODE=1

# Copy from the cache instead of linking since it's a mounted volume
ENV UV_LINK_MODE=copy

# https://docs.python.org/3/using/cmdline.html#envvar-PYTHONDONTWRITEBYTECODE
# Prevents Python from writing .pyc files to disk
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

ARG INSTALL_DEV=false
ENV INSTALL_DEV=${INSTALL_DEV}

WORKDIR /app

# Common dependencies
RUN apt-get update \
    && apt-get install --no-install-recommends -y \
        curl \
        openssh-client \
        git \
        libpq-dev  \
        gcc \
        build-essential \
        libmagic1 \
        libcurl4-openssl-dev \
        vim \
        less \
        procps \
        htop \
        sudo \
        postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# SSH configuration for private repositories
RUN mkdir -p -m 0700 ~/.ssh \
    && ssh-keyscan github.com >> ~/.ssh/known_hosts

# Install debugpy for VS Code debugging
RUN pip install debugpy

# Install AWS CLI v2
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-aarch64.zip" -o "awscliv2.zip" \
    && apt-get update \
    && apt-get install -y unzip \
    && unzip awscliv2.zip \
    && ./aws/install \
    && rm -rf awscliv2.zip ./aws \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy entrypoint script and ensure it's executable
COPY entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

EXPOSE 8000 5678


CMD ["sleep", "infinity"]
