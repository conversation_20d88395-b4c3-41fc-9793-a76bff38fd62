# Dev Container for Ciba Remote Monitoring

This directory contains configuration files for setting up a development container environment for the Ciba Remote Monitoring project.

## Features

- Python 3.12 development environment with uv package manager
- PostgreSQL database in a separate container
- LocalStack for AWS service emulation
- VS Code extensions pre-configured for Python development
- Debugging support with debugpy
- Git and GitHub CLI integration
- AWS CLI with SSO support

## Getting Started

1. Install [Docker](https://www.docker.com/products/docker-desktop) and [VS Code](https://code.visualstudio.com/)
1. Install the [Remote - Containers](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers) extension in VS Code
1. Open the project folder in VS Code
1. Click on the green button in the bottom-left corner of VS Code and select "Reopen in Container"
1. Wait for the container to build and start

## Container Structure

- **app**: The main development container with Python 3.12 and all dependencies
- **db**: PostgreSQL 14 database container
- **localstack**: LocalStack container for AWS service emulation

## Database Connection

The PostgreSQL database is accessible at:

- Host: `db`
- Port: `5432`
- Database: `postgres`
- Username: `postgres`
- Password: `postgres`

From your host machine, you can connect to the database at `localhost:5433`.

## Running the Application

Once inside the dev container, you can run the application with:

```bash
uv run --no-sync uvicorn app.main:app --host=0.0.0.0 --port=8000 --reload
```

Or use the entrypoint script:

```bash
./entrypoint.sh
```

## Debugging

The container is configured with debugpy for VS Code debugging. A launch configuration is included in the VS Code settings.

## AWS SSO Integration

The dev container is configured to use AWS SSO credentials from your host machine. Your `~/.aws` directory is mounted into the container, allowing you to use your existing AWS profiles.

To log in with AWS SSO from within the container:

1. Run the provided helper script:

   ```bash
   ./.devcontainer/aws-sso-login.sh <profile_name>
   ```

1. A browser window will open on your host machine for authentication

1. Complete the authentication process in the browser

1. Your AWS credentials will be available in the container

## Customization

You can customize the dev container by modifying the following files:

- `devcontainer.json`: VS Code settings and extensions
- `docker-compose.yml`: Container configuration
- `Dockerfile`: Development environment setup
