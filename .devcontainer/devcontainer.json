{"name": "Ciba RPM", "dockerComposeFile": "docker-compose.yml", "service": "app", "workspaceFolder": "/app", "customizations": {"vscode": {"extensions": ["ms-python.python", "ms-python.vscode-pylance", "ms-python.black-formatter", "ms-python.flake8", "ms-python.isort", "ms-azuretools.vscode-docker", "mtxr.sqltools", "mtxr.sqltools-driver-pg", "njpwerner.autodocstring", "streetsidesoftware.code-spell-checker", "ryanluker.vscode-coverage-gutters", "ms-python.debugpy"], "settings": {"python.defaultInterpreterPath": "/usr/local/bin/python", "python.linting.enabled": true, "python.linting.pylintEnabled": true, "python.formatting.provider": "black", "python.formatting.blackPath": "/usr/local/bin/black", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true}, "python.linting.flake8Enabled": true, "python.linting.mypyEnabled": true, "python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "python.testing.nosetestsEnabled": false, "python.testing.pytestArgs": ["tests"], "sqltools.connections": [{"name": "Remote Monitoring DB", "driver": "PostgreSQL", "server": "db", "port": 5432, "database": "postgres", "username": "postgres", "password": "postgres"}]}}}, "forwardPorts": [8000, 5432], "postCreateCommand": "uv sync", "postStartCommand": "./.devcontainer/startup.sh", "remoteUser": "root", "features": {"ghcr.io/devcontainers/features/git:1": {}, "ghcr.io/devcontainers/features/github-cli:1": {}}}