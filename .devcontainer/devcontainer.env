# Database Configuration - Default values for local development
POSTGRES_HOST=
POSTGRES_DB=
POSTGRES_USER=rpmUser
POSTGRES_PASSWORD=''
POSTGRES_PORT=5432

# AWS Configuration
AWS_REGION=us-east-2
SQS_ENDPOINT_URL=https://sqs.us-east-2.amazonaws.com
SQS_REGION=us-east-2
AWS_SSO_SESSION=
AWS_CONFIG_FILE=/root/.aws/config
AWS_PROFILE=
SQS_ENDPOINT_URL="https://sqs.us-east-2.amazonaws.com"
SQS_REGION=us-east-2
SQS_DATA_NOTIFICATION_QUEUE=""

# Application Configuration
DEBUG=0
IS_LOCAL=
IS_NEW_ENV=0
IS_LAMBDA=0
API_KEYS=
METRICS_USERNAME=admin
METRICS_PASSWORD=admin
ENV=dev


# Withings Configuration
WITHINGS_CLIENT_ID=
WITHINGS_CUSTOMER_SECRET=
WITHINGS_CALLBACK_URI=https://bream-fair-cleanly.ngrok-free.app/withings/get_token
WITHINGS_REDIRECT_URI=https://bream-fair-cleanly.ngrok-free.app/withings/get_token


# Fitbit Configuration
FITBIT_CLIENT_ID=
FITBIT_CLIENT_SECRET=
FITBIT_REDIRECT_URI=https://bream-fair-cleanly.ngrok-free.app/fitbit/get_token
