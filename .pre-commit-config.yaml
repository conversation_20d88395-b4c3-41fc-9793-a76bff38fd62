repos:
- repo: https://github.com/astral-sh/ruff-pre-commit
  rev: v0.11.8
  hooks:
    - id: ruff
      args:
        - --fix
    - id: ruff-format
- repo: https://github.com/executablebooks/mdformat
  hooks:
    - additional_dependencies:
      - mdformat-ruff
      id: mdformat
  rev: 0.7.22
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v5.0.0
  hooks:
    - id: trailing-whitespace
    - id: requirements-txt-fixer
    - id: name-tests-test
      exclude: ^tests/.*common\.py$
    - id: end-of-file-fixer
    - id: detect-aws-credentials
      args: [--allow-missing-credentials]
    - id: detect-private-key
    - id: check-yaml
    - id: check-added-large-files

- repo: https://github.com/gitleaks/gitleaks
  rev: v8.25.1
  hooks:
    - id: gitleaks

- repo: https://github.com/commitizen-tools/commitizen
  rev: v4.6.0
  hooks:
    - id: commitizen

- repo: https://github.com/astral-sh/uv-pre-commit
  rev: 0.7.2
  hooks:
    - id: pip-compile
      args:
        - requirements.in
        - -o
        - requirements.txt

- repo: https://github.com/owenlamont/uv-secure
  rev: 0.9.1
  hooks:
    - id: uv-secure

- repo: https://github.com/ninoseki/uv-sort
  rev: "v0.5.1"
  hooks:
    - id: uv-sort
