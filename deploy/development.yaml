service_name: remote-monitoring-api
metadata:
  team: data-lake
  source: python
type: microservice
internal: false
stage: dev
deploy:
  command:
    - /app/entrypoint.sh
securityContext: null
image:
  pullPolicy: IfNotPresent
networking:
  backendPort: 8000
resources:
  requests:
    memory: "512Mi"
    cpu: "250m"
  limits:
    memory: "1024Mi"
    cpu: "500m"
environment:
  - name: "IS_NEW_ENV"
    value: "1"
ssm_params:
  - name: POSTGRES_HOST
    from: /rpm-single/POSTGRES_HOST
  - name: POSTGRES_DB
    from: /rpm-single/POSTGRES_DB
  - name: POSTGRES_USER
    from: /rpm-single/POSTGRES_USER
  - name: POSTGRES_PASSWORD
    from: /rpm-single/POSTGRES_PASSWORD
  - name: ENV
    from: /rpm-single/ENV
  - name: WITHINGS_CLIENT_ID
    from: /rpm-single/WITHINGS_CLIENT_ID
  - name: WITHINGS_CUSTOMER_SECRET
    from: /rpm-single/WITHINGS_CUSTOMER_SECRET
  - name: WITHINGS_CALLBACK_URI
    from: /rpm-single/WITHINGS_CALLBACK_URI
  - name: WITHINGS_NOTIFICATION_CALLBACK_URI
    from: /rpm-single/WITHINGS_NOTIFICATION_CALLBACK_URI
  - name: WITHINGS_NOTIFICATION_QUEUE
    from: /rpm-single/WITHINGS_NOTIFICATION_QUEUE
  - name: SQS_ENDPOINT_URL
    from: /rpm-single/SQS_ENDPOINT_URL
  - name: API_KEYS
    from: /rpm-single/API_KEYS
  - name: SENTRY_DSN
    from: /rpm-single/SENTRY_DSN
  - name: FITBIT_CLIENT_ID
    from: /rpm-single/FITBIT_CLIENT_ID
  - name: FITBIT_CUSTOMER_SECRET
    from: /rpm-single/FITBIT_CUSTOMER_SECRET
  - name: FITBIT_REDIRECT_URI
    from: /rpm-single/FITBIT_REDIRECT_URI
  - name: FITBIT_VERIFY
    from: /rpm-single/FITBIT_VERIFY
  - name: DEXCOM_CLIENT_ID
    from: /rpm-single/DEXCOM_CLIENT_ID
  - name: DEXCOM_CUSTOMER_SECRET
    from: /rpm-single/DEXCOM_CUSTOMER_SECRET
  - name: DEXCOM_REDIRECT_URI
    from: /rpm-single/DEXCOM_REDIRECT_URI
  - name: WITHINGS_REDIRECT_URI
    from: /rpm-single/WITHINGS_REDIRECT_URI
  - name: SQS_DATA_NOTIFICATION_QUEUE
    from: /rpm-single/SQS_DATA_NOTIFICATION_QUEUE
  - name: RPM_SINGLE_SNS_TOPIC_ARN
    from: /rpm-single/SNS_TOPIC_ARN
  - name: DEXCOM_CLIENT_SECRET
    from: /rpm-single/DEXCOM_CLIENT_SECRET
  - name: FITBIT_CLIENT_SECRET
    from: /rpm-single/FITBIT_CLIENT_SECRET
  - name: TRANSTEK_API_KEY
    from: /rpm-single/TRANSTEK_API_KEY
  - name: TRANSTEK_CIBA_KEY
    from: /rpm-single/TRANSTEK_CIBA_KEY
