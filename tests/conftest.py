"""
Pytest configuration and shared fixtures for token handling tests.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from uuid import uuid4
import pendulum

from ciba_iot_etl.models.db.fitbit import Fitbit
from ciba_iot_etl.models.db.withings import Withings
from ciba_iot_etl.models.db.member import Member
from tests.unit.test_utils_test import (
    create_mock_member,
    create_mock_withings_device,
    create_mock_withings_loader,
    create_mock_platform,
    DummyQuery,
    MockMemberState,
)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_member():
    """Create a mock Member instance."""
    member = Mock(spec=Member)
    member.id = uuid4()
    member.email = "<EMAIL>"
    return member


@pytest.fixture
def mock_fitbit_connection():
    """Create a mock Fitbit connection with realistic data."""
    connection = Mock(spec=Fitbit)
    connection.id = uuid4()
    connection.member_id = uuid4()
    connection.access_token = "mock_access_token"
    connection.refresh_token = "mock_refresh_token"
    connection.old_refresh_token = "mock_old_refresh_token"
    connection.expires_in = 3600
    connection.healthy = True
    connection.created_at = pendulum.now()
    connection.updated_at = pendulum.now()
    connection.access_token_expires_at = pendulum.now().add(hours=1)
    connection.old_refresh_token_expires_at = pendulum.now().add(hours=8)

    # Mock methods
    connection.is_access_token_expired.return_value = False
    connection.is_old_refresh_token_expired.return_value = False
    connection.save = AsyncMock()

    return connection


@pytest.fixture
def mock_withings_connection():
    """Create a mock Withings connection with realistic data."""
    connection = Mock(spec=Withings)
    connection.id = uuid4()
    connection.member_id = uuid4()
    connection.user_id = 12345
    connection.access_token = "mock_access_token"
    connection.refresh_token = "mock_refresh_token"
    connection.old_refresh_token = "mock_old_refresh_token"
    connection.expires_in = 10800  # 3 hours for Withings
    connection.healthy = True
    connection.created_at = pendulum.now()
    connection.updated_at = pendulum.now()
    connection.access_token_expires_at = pendulum.now().add(hours=3)
    connection.old_refresh_token_expires_at = pendulum.now().add(hours=8)

    # Mock methods
    connection.is_access_token_expired.return_value = False
    connection.is_old_refresh_token_expired.return_value = False
    connection.save = AsyncMock()

    return connection


@pytest.fixture
def mock_fitbit_token_success():
    """Create a mock successful Fitbit token response."""
    token = Mock()
    token.error = None
    token.access_token = "new_access_token"
    token.refresh_token = "new_refresh_token"
    token.expires_in = 3600
    token.user_id = "FITBIT_USER_123"
    token.scope = ["activity", "heartrate", "weight"]
    return token


@pytest.fixture
def mock_fitbit_token_error():
    """Create a mock failed Fitbit token response."""
    token = Mock()
    token.error = "invalid_grant"
    token.access_token = None
    token.refresh_token = None
    token.expires_in = None
    return token


@pytest.fixture
def mock_withings_token_success():
    """Create a mock successful Withings token response."""
    return {
        "status": 0,
        "body": {
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
            "expires_in": 10800,
            "userid": 12345,
            "scope": "user.info,user.metrics",
        },
    }


@pytest.fixture
def mock_withings_token_error():
    """Create a mock failed Withings token response."""
    return {"status": 401, "error": "invalid_grant"}


@pytest.fixture
def mock_withings_api_success():
    """Create a mock successful Withings API response."""
    return {
        "status": 0,
        "body": {
            "devices": [
                {
                    "deviceid": "device123",
                    "type": 1,
                    "model": "Body Scale",
                    "timezone": "Europe/Paris",
                }
            ]
        },
    }


@pytest.fixture
def mock_withings_api_error():
    """Create a mock failed Withings API response."""
    return {"status": 401, "error": "Unauthorized"}


class MockAsyncContext:
    """Mock async context manager for database operations."""

    def __init__(self, return_value=None):
        self.return_value = return_value

    async def __aenter__(self):
        return self.return_value

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Optionally, you can add logic to handle exceptions here
        pass


@pytest.fixture
def mock_database_operations():
    """Mock common database operations."""
    mocks = {
        "fitbit_get": AsyncMock(),
        "fitbit_update_tokens": AsyncMock(),
        "fitbit_filter_update": AsyncMock(),
        "withings_get": AsyncMock(),
        "withings_update_tokens": AsyncMock(),
        "withings_filter_update": AsyncMock(),
        "withings_expire_old_token": AsyncMock(return_value=True),
    }
    return mocks


@pytest.fixture
def mock_logging():
    """Mock logging for tests."""
    with pytest.mock.patch(
        "app.services.token_error_handler.logger"
    ) as mock_logger:
        yield mock_logger


# Additional shared fixtures using test_utils


@pytest.fixture
def dummy_query():
    """Create a DummyQuery instance for testing."""
    return DummyQuery()


@pytest.fixture
def dummy_query_with_result():
    """Create a DummyQuery instance with a mock result."""

    def _create_query(result):
        return DummyQuery(result)

    return _create_query


@pytest.fixture
def mock_member_enhanced():
    """Create an enhanced mock Member using test_utils."""
    return create_mock_member()


@pytest.fixture
def mock_withings_device():
    """Create a mock Withings device using test_utils."""
    return create_mock_withings_device()


@pytest.fixture
def mock_withings_loader():
    """Create a mock WithingsLoader using test_utils."""
    return create_mock_withings_loader()


@pytest.fixture
def mock_platform():
    """Create a mock Platform using test_utils."""
    return create_mock_platform()


@pytest.fixture
def mock_member_state():
    """Create a mock MemberState using test_utils."""
    return MockMemberState()


@pytest.fixture
def mock_queue():
    """Create a mock queue for testing."""
    return AsyncMock()


@pytest.fixture
def sample_error_scenarios():
    """Provide sample error scenarios for testing."""
    return {
        "invalid_grant": {
            "message": "invalid_grant",
            "requires_reauth": True,
            "should_fallback": True,
        },
        "expired_token": {
            "message": "Token expired",
            "requires_reauth": True,
            "should_fallback": False,
        },
        "network_error": {
            "message": "Network timeout",
            "requires_reauth": False,
            "should_fallback": False,
        },
        "api_error": {
            "message": "Rate limit exceeded",
            "requires_reauth": False,
            "should_fallback": False,
        },
        "validation_error": {
            "message": "Invalid response format",
            "requires_reauth": False,
            "should_fallback": False,
        },
        "unknown_error": {
            "message": "Something went wrong",
            "requires_reauth": False,
            "should_fallback": False,
        },
    }


@pytest.fixture
def token_lifecycle_scenarios():
    """Provide token lifecycle test scenarios."""
    return {
        "successful_refresh": {
            "primary_success": True,
            "fallback_needed": False,
            "expected_calls": 1,
        },
        "successful_fallback": {
            "primary_success": False,
            "primary_error": "invalid_grant",
            "fallback_success": True,
            "fallback_needed": True,
            "expected_calls": 2,
        },
        "failed_no_fallback": {
            "primary_success": False,
            "primary_error": "network_error",
            "fallback_needed": False,
            "expected_calls": 1,
        },
        "failed_with_fallback": {
            "primary_success": False,
            "primary_error": "invalid_grant",
            "fallback_success": False,
            "fallback_error": "invalid_grant",
            "fallback_needed": True,
            "expected_calls": 2,
        },
    }


# Test markers for categorizing tests
def pytest_configure(config):
    """Configure custom pytest markers."""
    config.addinivalue_line("markers", "unit: mark test as a unit test")
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line("markers", "e2e: mark test as an end-to-end test")
    config.addinivalue_line(
        "markers", "error_handling: mark test as error handling test"
    )
    config.addinivalue_line(
        "markers", "token_lifecycle: mark test as token lifecycle test"
    )
    config.addinivalue_line(
        "markers", "fallback: mark test as fallback logic test"
    )
    config.addinivalue_line(
        "markers", "fitbit: mark test as Fitbit-specific test"
    )
    config.addinivalue_line(
        "markers", "withings: mark test as Withings-specific test"
    )
