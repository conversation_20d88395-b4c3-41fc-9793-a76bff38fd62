"""
Shared test utilities to reduce code duplication across test files.

This module provides common mock classes, helper functions, and reusable components
that are used across multiple test files.
"""

from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
from typing import Any, Optional

import pendulum
from ciba_iot_etl.models.pydantic.common import Platform


class DummyQuery:
    """
    A reusable mock query class that simulates async database query operations.

    This replaces the multiple DummyQuery classes found across different test files.
    """

    def __init__(self, result: Any = None):
        self.result = result

    async def get_or_none(self) -> Any:
        """Return the dummy result asynchronously."""
        return self.result

    async def first(self) -> Any:
        """Return the first result asynchronously."""
        return self.result

    async def filter(self, *args, **kwargs) -> Any:
        """Return the filtered result asynchronously."""
        return self.result

    async def all(self) -> Any:
        """Return all results asynchronously."""
        return self.result if isinstance(self.result, list) else [self.result]


class AsyncChainMock:
    """
    A mock class that supports method chaining for async database operations.

    This is useful for mocking complex query chains like:
    Model.filter().prefetch_related().first()
    """

    def __init__(self, return_value: Any = None):
        self.return_value = return_value
        self._filter_kwargs = None
        self._filter_args = None
        self._prefetch_args = None

    def filter(self, *args, **kwargs):
        self._filter_args = args
        self._filter_kwargs = kwargs
        return self

    def first(self):
        return self

    def prefetch_related(self, *args):
        self._prefetch_args = args
        return self

    def order_by(self, *args):
        return self

    def limit(self, count: int):
        return self

    def __await__(self):
        async def _return():
            return self.return_value

        return _return().__await__()


class MockMemberState:
    """
    A reusable mock MemberState class used across multiple test files.
    """

    def __init__(
        self,
        sync_start_date: Optional[datetime] = None,
        redirect_uri: str = "https://test.com",
    ):
        self.sync_start_date = sync_start_date or pendulum.now().subtract(
            days=30
        )
        self.redirect_uri = redirect_uri
        self.state = 0

    async def save(self):
        """Mock save method."""
        return ""


def create_mock_member(
    member_id: Optional[str] = None,
    email: str = "<EMAIL>",
    withings_id: Optional[str] = None,
    platforms: Optional[list] = None,
) -> AsyncMock:
    """
    Create a standardized mock Member object with common attributes and methods.

    Args:
        member_id: Optional member ID, generates UUID if not provided
        email: Member email address
        withings_id: Optional Withings ID for the member
        platforms: List of platforms associated with the member

    Returns:
        AsyncMock configured as a Member object
    """
    member = AsyncMock()
    member.id = member_id or str(uuid4())
    member.email = email
    member.created_at = datetime.now()

    # Configure common async methods
    member.get_withings_id = AsyncMock(return_value=withings_id)
    member.get_platforms = AsyncMock(return_value=platforms or [])
    member.get_withings_refresh_token = AsyncMock(return_value="refresh_token")

    return member


def create_mock_withings_device(
    device_id: Optional[str] = None,
    user_id: str = "user_id",
    access_token: str = "mock_access_token",
    healthy: bool = True,
    expires_in: int = 3600,
) -> MagicMock:
    """
    Create a standardized mock Withings device object.

    Args:
        device_id: Optional device ID, generates UUID if not provided
        user_id: Withings user ID
        access_token: Access token for the device
        healthy: Whether the device is healthy/connected
        expires_in: Token expiration time in seconds

    Returns:
        MagicMock configured as a Withings device
    """
    device = MagicMock()
    device.id = device_id or str(uuid4())
    device.user_id = user_id
    device.access_token = access_token
    device.healthy = healthy
    device.expires_in = expires_in
    device.member_id = str(uuid4())
    device.updated_at = datetime.now()

    # Add async methods
    device.save = AsyncMock()
    device.delete = AsyncMock()

    return device


def create_mock_withings_loader(
    get_token_response: Optional[dict] = None,
    create_subscription_response: Optional[dict] = None,
    delete_subscription_response: Optional[dict] = None,
) -> AsyncMock:
    """
    Create a standardized mock WithingsLoader object.

    Args:
        get_token_response: Response for get_token method
        create_subscription_response: Response for create_subscription method
        delete_subscription_response: Response for delete_subscription method

    Returns:
        AsyncMock configured as a WithingsLoader
    """
    loader = AsyncMock()

    # Default responses
    default_token_response = {
        "body": {
            "access_token": "token",
            "refresh_token": "refresh",
            "userid": "user_id",
            "expires_in": 3600,
        }
    }

    loader.get_token.return_value = (
        get_token_response or default_token_response
    )
    loader.create_subscription.return_value = create_subscription_response or {
        "status": 0
    }
    loader.delete_subscription.return_value = delete_subscription_response or {
        "status": 0
    }
    loader.refresh_token.return_value = {
        "body": {
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
        }
    }

    return loader


def create_mock_platform(
    platform_id: str = "mock_platform_id", platform_type: str = "patient"
) -> Platform:
    """
    Create a mock Platform object.

    Args:
        platform_id: Platform ID
        platform_type: Type of platform (patient, participant, etc.)

    Returns:
        Platform object
    """
    return Platform(id=platform_id, type=platform_type)


# Common patch paths used across multiple test files
COMMON_PATCHES = {
    "member_get_by_platform": "ciba_iot_etl.models.db.member.Member.get_by_platform",
    "member_state_get_by_state": "ciba_iot_etl.models.db.member_state.MemberState.get_member_by_state",
    "member_state_get_redirect_uri": "ciba_iot_etl.models.db.member_state.MemberState.get_redirect_uri",
    "member_state_filter": "ciba_iot_etl.models.db.member_state.MemberState.filter",
    "withings_filter": "app.services.withings.Withings.filter",
    "withings_get_or_none": "app.services.withings.Withings.get_or_none",
    "withings_delete": "ciba_iot_etl.models.db.withings.Withings.delete_withings",
    "withings_get": "ciba_iot_etl.models.db.withings.Withings.get",
    "withings_update_tokens": "ciba_iot_etl.models.db.withings.Withings.update_tokens",
    "queue_get_queue": "app.services.queue.get_queue",
    "process_member_state": "app.services.common.process_member_state",
    # Manager router specific patches
    "withings_module": "app.routers.manager.Withings",
    "fitbit_module": "app.routers.manager.Fitbit",
    "member_get": "app.routers.manager.Member.get",
    "member_platform_model": "app.routers.manager.MemberPlatform",
    "get_user_data": "app.routers.manager.get_user_data",
    "withings_refresh": "app.routers.manager.WithingsLoader.refresh_token",
    "withings_api_call": "app.routers.manager.WithingsLoader._call_api_with_rate_limit_handling",
    "fitbit_refresh": "app.routers.manager.FitbitLoader.refresh_token",
    "fitbit_all": "app.routers.manager.Fitbit.all",
    "fitbit_call_api": "app.routers.manager.FitbitLoader._call_api_with_rate_limit_handling",
}


async def mock_async_property(value: Any) -> Any:
    """
    Helper function to mock async properties.

    Args:
        value: The value to return

    Returns:
        The provided value
    """
    return value


def create_mock_device_model() -> tuple[MagicMock, dict]:
    """
    Create a standardized mock device model for testing.

    Returns:
        Tuple of (mock_device_model, actions_dict)
    """
    mock_device_model = MagicMock()
    mock_all_result = AsyncMock()
    mock_filter = MagicMock()

    # Set up the filter chain properly: filter() returns an object with first() method
    mock_filter.first = AsyncMock()
    mock_device_model.all = AsyncMock(return_value=mock_all_result)
    mock_device_model.filter.return_value = mock_filter

    actions = {
        "all": mock_all_result,
        "filter": mock_filter,
    }

    return mock_device_model, actions


def create_mock_member_model() -> tuple[MagicMock, dict]:
    """
    Create a standardized mock member model for testing.

    Returns:
        Tuple of (mock_member_model, actions_dict)
    """
    mock_member_model = MagicMock()
    mock_filter = MagicMock()
    mock_all = MagicMock()
    mock_member_model.filter.return_value = mock_filter
    mock_member_model.all.return_value = mock_all

    actions = {
        "all": mock_all,
        "filter": mock_filter,
    }

    return mock_member_model, actions


def create_mock_member_platform_model() -> tuple[MagicMock, dict]:
    """
    Create a standardized mock member platform model for testing.

    Returns:
        Tuple of (mock_member_platform_model, actions_dict)
    """
    member_platform_mock = MagicMock()
    member_platform_filter_mock = MagicMock()
    member_platform_mock.filter.return_value = member_platform_filter_mock

    actions = {"filter": member_platform_filter_mock}

    return member_platform_mock, actions


def create_measure_mock(name: str) -> MagicMock:
    """
    Create a standardized mock measure model for testing.

    Args:
        name: The name of the measure model

    Returns:
        MagicMock configured as a measure model
    """
    measure_model_mock = MagicMock()
    measure_model_mock.__name__ = name
    order_by_mock = MagicMock()
    filter_mock = MagicMock()
    filter_mock.order_by.return_value = order_by_mock
    measure_model_mock.filter.return_value = filter_mock
    order_by_mock.limit = AsyncMock(return_value=[])

    return measure_model_mock


def create_test_member(
    member_id: str = "6b466245-c321-408f-9a9c-e85ff4a46ba2",
    email: str = "<EMAIL>",
) -> MagicMock:
    """
    Create a standardized test member object.

    Args:
        member_id: The member ID
        email: The member email

    Returns:
        MagicMock configured as a test member
    """
    test_member = MagicMock()
    test_member.id = (
        member_id if isinstance(member_id, str) else str(member_id)
    )
    test_member.email = email
    return test_member


def create_test_device_data(device_id: str = "device_id") -> MagicMock:
    """
    Create a standardized test device data object.

    Args:
        device_id: The device ID

    Returns:
        MagicMock configured as test device data
    """
    test_device_data = MagicMock()
    test_device_data.id = device_id
    return test_device_data


def create_test_device_with_member(
    device_id: str = "test_device_id",
    member: Optional[Any] = None,
    access_token: str = "test_access_token",
    refresh_token: str = "test_refresh_token",
    is_expired: bool = False,
) -> MagicMock:
    """
    Create a standardized test device with member for testing.

    Args:
        device_id: The device ID
        member: The member object (will use mock_async_property)
        access_token: The access token
        refresh_token: The refresh token
        is_expired: Whether the access token is expired

    Returns:
        MagicMock configured as a test device
    """
    test_device = MagicMock()
    test_device.id = device_id
    test_device.access_token = access_token
    test_device.refresh_token = refresh_token
    test_device.is_access_token_expired = MagicMock(return_value=is_expired)

    if member:
        test_device.member = mock_async_property(member)

    return test_device


def create_test_platforms(platform_types: list[str] = None) -> list[MagicMock]:
    """
    Create standardized test platform objects.

    Args:
        platform_types: List of platform types (defaults to ["participant", "patient"])

    Returns:
        List of MagicMock objects configured as platforms
    """
    if platform_types is None:
        platform_types = ["participant", "patient"]

    platforms = []
    for i, platform_type in enumerate(platform_types):
        platform = MagicMock()
        platform.platform_id = f"{platform_type}_{i + 1}"
        platform.platform_type = platform_type
        platforms.append(platform)

    return platforms


def create_error_response(
    success: bool = False, error: str = "Test error"
) -> dict:
    """
    Create a standardized error response dictionary.

    Args:
        success: Whether the operation was successful
        error: The error message

    Returns:
        Dictionary with success and error keys
    """
    return {"success": success, "error": error}


def create_success_response(
    message: str = "Operation successful", **kwargs
) -> dict:
    """
    Create a standardized success response dictionary.

    Args:
        message: Success message
        **kwargs: Additional key-value pairs to include

    Returns:
        Dictionary with success=True, message, and any additional kwargs
    """
    response = {"success": True, "message": message}
    response.update(kwargs)
    return response


class PatchContextManager:
    """
    A utility class to simplify creating patch context managers with common patterns.
    """

    @staticmethod
    def member_not_found(
        member_get_path: str = "app.routers.manager.Member.get",
    ):
        """Create a patch context for member not found scenarios."""
        return patch(
            member_get_path, new_callable=AsyncMock, return_value=None
        )

    @staticmethod
    def member_found(
        member, member_get_path: str = "app.routers.manager.Member.get"
    ):
        """Create a patch context for member found scenarios."""
        return patch(
            member_get_path, new_callable=AsyncMock, return_value=member
        )

    @staticmethod
    def device_not_found(device_module_path: str, mock_device_model):
        """Create a patch context for device not found scenarios."""
        device_mock, device_actions = mock_device_model
        device_actions["filter"].first = AsyncMock(return_value=None)
        return patch(device_module_path, device_mock)

    @staticmethod
    def device_found(device_module_path: str, mock_device_model, device):
        """Create a patch context for device found scenarios."""
        device_mock, device_actions = mock_device_model
        device_actions["filter"].first = AsyncMock(return_value=device)
        return patch(device_module_path, device_mock)

    @staticmethod
    def get_user_data_error(
        get_user_data_path: str = "app.routers.manager.get_user_data",
    ):
        """Create a patch context for get_user_data error scenarios."""
        return patch(
            get_user_data_path, new_callable=AsyncMock, side_effect=Exception()
        )

    @staticmethod
    def get_user_data_success(
        get_user_data_path: str = "app.routers.manager.get_user_data",
        return_value=None,
    ):
        """Create a patch context for get_user_data success scenarios."""
        return patch(
            get_user_data_path,
            new_callable=AsyncMock,
            return_value=return_value,
        )


class TestAssertions:
    """
    Common assertion patterns used across multiple tests.
    """

    @staticmethod
    def assert_http_exception(exception_info, expected_status_code: int):
        """Assert HTTPException with expected status code."""
        assert exception_info.value.status_code == expected_status_code

    @staticmethod
    def assert_error_response(actual_response: dict, expected_error: str):
        """Assert error response format."""
        assert actual_response["success"] is False
        assert expected_error in actual_response["error"]

    @staticmethod
    def assert_success_response(
        actual_response: dict, expected_message: str = None
    ):
        """Assert success response format."""
        assert actual_response["success"] is True
        if expected_message:
            assert expected_message in actual_response["message"]

    @staticmethod
    def assert_device_health_response(
        actual_response: dict, member_email: str, expected_health: bool
    ):
        """Assert device health response format."""
        assert actual_response == {member_email: expected_health}

    @staticmethod
    def assert_template_response(mock_templates, template_name: str, expected_context: dict):
        """Assert template response was called correctly."""
        mock_templates.TemplateResponse.assert_called_once_with(template_name, expected_context)

    @staticmethod
    def assert_mock_awaited_once_with(mock_obj, *args, **kwargs):
        """Assert mock was awaited once with specific arguments."""
        mock_obj.assert_awaited_once_with(*args, **kwargs)

    @staticmethod
    def assert_user_data_response(actual_response: dict):
        """Assert standard user data response format."""
        expected_keys = ["blood_pressure_measurements", "heart_rate_measurements", "weight_measurements"]
        for key in expected_keys:
            assert key in actual_response
            assert isinstance(actual_response[key], list)


class ManagerTestHelpers:
    """
    High-level test helpers for manager router tests to eliminate duplication.
    """

    @staticmethod
    async def run_token_health_test(device_type, device_module_path, member, should_succeed=True):
        """
        Run a standardized token health test for any device type.

        Args:
            device_type: ActivityDevice enum value
            device_module_path: Path to device module for patching
            member: Test member object
            should_succeed: Whether the test should succeed or fail
        """
        from unittest.mock import patch, AsyncMock
        from tests.unit.test_utils_test import create_test_device_with_member, PatchContextManager

        test_device = create_test_device_with_member(member=member)

        with (
            patch(f"{device_module_path}.all", new_callable=AsyncMock, return_value=[test_device]),
            PatchContextManager.get_user_data_success("app.routers.manager.get_user_data") if should_succeed
            else PatchContextManager.get_user_data_error("app.routers.manager.get_user_data"),
        ):
            from app.routers.manager import check_token_health
            actual_value = await check_token_health(device_type)
            return actual_value == {member.email: should_succeed}

    @staticmethod
    async def run_device_info_test(device_type, device_module_path, member, mock_device_model, device_found=True):
        """
        Run a standardized device info test.

        Args:
            device_type: ActivityDevice enum value
            device_module_path: Path to device module for patching
            member: Test member object
            mock_device_model: Mock device model fixture
            device_found: Whether device should be found
        """
        from tests.unit.test_utils_test import PatchContextManager, create_test_device_data

        if device_found:
            test_device = create_test_device_data()
            with (
                PatchContextManager.member_found(member, "app.routers.manager.Member.get"),
                PatchContextManager.device_found(device_module_path, mock_device_model, test_device),
            ):
                from app.routers.manager import ui_get_device_info
                result = await ui_get_device_info(member.email, device_type)
                return result.get("success", False)
        else:
            with (
                PatchContextManager.member_found(member, "app.routers.manager.Member.get"),
                PatchContextManager.device_not_found(device_module_path, mock_device_model),
            ):
                from app.routers.manager import ui_get_device_info
                result = await ui_get_device_info(member.email, device_type)
                return not result.get("success", True)

    @staticmethod
    def create_standard_user_response(member, device_data, platforms=None, activities=None):
        """Create a standardized user response object."""
        response = {
            "id": member.id,
            "email": member.email,
            "activities": activities or {},
            "member_platforms": platforms or [],
            "withings": device_data,
            "fitbit": device_data,
        }
        return response
