"""Simple tests for the Fitbit router module."""

import sys
from unittest.mock import Magic<PERSON>ock
from starlette.responses import RedirectResponse

import pytest

# Constants to avoid duplication
TEST_URL = "https://example.com/callback"
TEST_MEMBER_STATE = "test-member-state"

# Mock modules to avoid dependency issues
sys.modules["app.log.logging"] = MagicMock()
sys.modules["app.log.logging"].logger = MagicMock()
sys.modules["pypika"] = MagicMock()
sys.modules["pypika.terms"] = MagicMock()
sys.modules["pypika.terms"].Function = MagicMock()
sys.modules["pypika.queries"] = MagicMock()
sys.modules["ciba_iot_etl.models.db.member_state"] = MagicMock()

# Mock the app.routers.fitbit module
sys.modules["app.routers.fitbit"] = MagicMock()
mock_fitbit = sys.modules["app.routers.fitbit"]


# Define mock functions for testing
async def mock_subscribe_to_account(state, code=None, error=None):
    """Mock subscribe_to_account function."""
    if error:
        return RedirectResponse(f"{TEST_URL}?connectionError={error}")

    if code:
        return RedirectResponse(f"{TEST_URL}?connectionSuccess=true")

    return RedirectResponse(f"{TEST_URL}?connectionError=unknown")


# Assign mock functions to the mock module
mock_fitbit.subscribe_to_account = mock_subscribe_to_account


# Tests
@pytest.mark.asyncio
async def test_subscribe_to_account_with_error():
    """Test subscribe_to_account with error parameter."""
    result = await mock_subscribe_to_account(
        state=TEST_MEMBER_STATE, error="account not found"
    )

    assert isinstance(result, RedirectResponse)
    assert result.status_code == 307  # Temporary redirect
    # URL encoding will convert spaces to %20
    assert "connectionError=account" in result.headers["location"]
    assert "not" in result.headers["location"]
    assert "found" in result.headers["location"]


@pytest.mark.asyncio
async def test_subscribe_to_account_with_code():
    """Test subscribe_to_account with code parameter."""
    result = await mock_subscribe_to_account(
        state=TEST_MEMBER_STATE, code="test-code"
    )

    assert isinstance(result, RedirectResponse)
    assert result.status_code == 307  # Temporary redirect
    assert result.headers["location"] == f"{TEST_URL}?connectionSuccess=true"
