"""
Tests for user management functionality in the manager router.

This module contains tests for:
- get_users: Retrieving lists of users
- get_user: Retrieving individual user details
- ui_users: UI endpoint for user listing
- ui_user_detail: UI endpoint for user details
"""

from unittest.mock import AsyncMock, patch

import pytest
from ciba_iot_etl.models.pydantic.common import ActivityDevice, PlatformType
from fastapi import HTT<PERSON>Exception

from app.routers.manager import get_users, get_user, ui_users, ui_user_detail
from tests.unit.routers.manager_test_utils_test import (
    WITHINGS_MODULE,
    FITBIT_MODULE,
    MEMBER_PLATFORM_MODEL,
    MEMBER_GET,
    test_member,
    test_request,
    ManagerTestData,
    mock_member_model,
    mock_device_model,
    mock_member_platform_model,
    mock_templates,
)
from tests.unit.test_utils_test import (
    create_measure_mock,
    create_test_device_data,
    create_test_platforms,
    PatchContextManager,
    TestAssertions,
)


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_device, test_module",
    [
        (ActivityDevice.WITHINGS, WITHINGS_MODULE),
        (ActivityDevice.FITBIT, FITBIT_MODULE),
    ],
)
async def test_get_users_with_activity_device(
    test_device, test_module, mock_member_model, mock_device_model
):
    """
    get_users should return a list of users filtered by activity device.
    """
    mock_model, mock_model_actions = mock_member_model
    mock_model_actions["all"].values = AsyncMock(
        return_value=[
            {"id": 1, "member_id": "member1"},
            {"id": 2, "member_id": "member2"},
        ]
    )

    member_mock, member_mock_actions = mock_member_model
    member_mock_actions["filter"].values = AsyncMock(return_value=[])

    with (
        patch(test_module, mock_model),
        patch("app.routers.manager.Member", member_mock),
    ):
        actual_value = await get_users(test_device)

        assert actual_value == []


@pytest.mark.asyncio
async def test_get_users_with_no_activity_device(mock_member_model):
    """
    get_users should return a list of all users when no device filter is provided.
    """
    test_response = ManagerTestData.create_test_user_response()
    member_mock, member_mock_actions = mock_member_model
    member_mock_actions["all"].values = AsyncMock(return_value=test_response)

    with patch("app.routers.manager.Member", member_mock):
        actual_value = await get_users()

        assert actual_value == test_response


@pytest.mark.asyncio
async def test_get_user_with_not_found_error():
    """
    get_user should raise a not found exception when the provided email is not found.
    """
    with PatchContextManager.member_not_found(MEMBER_GET):
        with pytest.raises(HTTPException) as expected_error:
            await get_user("<EMAIL>")

        TestAssertions.assert_http_exception(expected_error, 404)


@pytest.mark.asyncio
async def test_get_user_without_activities(
    mock_device_model, mock_member_platform_model
):
    """
    get_user should return user data without activities info when the activities flag is false.
    """
    test_device_data = create_test_device_data()
    mock_device, mock_device_actions = mock_device_model
    mock_device_actions["filter"].first = AsyncMock(
        return_value=test_device_data
    )

    member_platform_mock, member_platform_actions = mock_member_platform_model
    member_platform_actions["filter"].all = AsyncMock(return_value=[])

    with (
        patch(WITHINGS_MODULE, mock_device),
        patch(FITBIT_MODULE, mock_device),
        patch(MEMBER_PLATFORM_MODEL, member_platform_mock),
        patch(
            MEMBER_GET,
            new_callable=AsyncMock,
            return_value=test_member,
        ),
    ):
        actual_value = await get_user(test_member.email)

        assert actual_value == {
            "id": test_member.id,
            "email": test_member.email,
            "activities": {},
            "member_platforms": [],
            "withings": test_device_data,
            "fitbit": test_device_data,
        }


@pytest.mark.asyncio
async def test_get_user_with_activities(
    mock_device_model, mock_member_platform_model
):
    """
    get_user should return user data including activities when activities flag is true.
    """
    test_device_data = create_test_device_data()
    mock_device, mock_device_actions = mock_device_model
    mock_device_actions["filter"].first = AsyncMock(
        return_value=test_device_data
    )

    test_platforms = create_test_platforms(
        [PlatformType.participant.value, PlatformType.patient.value]
    )
    member_platform_mock, member_platform_mock_actions = (
        mock_member_platform_model
    )
    member_platform_mock_actions["filter"].all = AsyncMock(
        return_value=test_platforms
    )

    with (
        patch(WITHINGS_MODULE, mock_device),
        patch(FITBIT_MODULE, mock_device),
        patch(MEMBER_PLATFORM_MODEL, member_platform_mock),
        patch("app.routers.manager.Activity", create_measure_mock("activity")),
        patch("app.routers.manager.Sleep", create_measure_mock("sleep")),
        patch("app.routers.manager.Weight", create_measure_mock("weight")),
        patch(
            "app.routers.manager.HeartRate", create_measure_mock("heartRate")
        ),
        patch(
            "app.routers.manager.BloodPressure",
            create_measure_mock("bloodPressure"),
        ),
        patch(
            MEMBER_GET,
            new_callable=AsyncMock,
            return_value=test_member,
        ),
    ):
        actual_value = await get_user(test_member.email, activities=True)

        assert actual_value == {
            "id": test_member.id,
            "email": test_member.email,
            "activities": {
                "patient_activity": [],
                "patient_bloodPressure": [],
                "patient_heartRate": [],
                "patient_sleep": [],
                "patient_weight": [],
            },
            "member_platforms": test_platforms,
            "withings": test_device_data,
            "fitbit": test_device_data,
        }


@pytest.mark.asyncio
async def test_ui_users(mock_templates):
    """
    ui_users should return the user template with the users' data.
    """
    test_response = [{"id": "1", "email": "<EMAIL>"}]

    with (
        patch("app.routers.manager.templates", mock_templates),
        patch(
            "app.routers.manager.get_users",
            new_callable=AsyncMock,
            return_value=test_response,
        ),
    ):
        await ui_users(test_request)

        mock_templates.TemplateResponse.assert_called_once_with(
            "users.html",
            {"request": test_request, "users": test_response},
        )


@pytest.mark.asyncio
async def test_ui_user_detail(mock_templates):
    """
    ui_user_detail should return the user detail template with the provided user data.
    """
    test_response = {"id": test_member.id, "email": test_member.email}

    with (
        patch("app.routers.manager.templates", mock_templates),
        patch(
            "app.routers.manager.get_user",
            new_callable=AsyncMock,
            return_value=test_response,
        ),
    ):
        await ui_user_detail(test_request, test_member.email)

        mock_templates.TemplateResponse.assert_called_once_with(
            "user_detail.html",
            {"request": test_request, "user": test_response},
        )
