"""
Tests for user data retrieval functionality in the manager router.

This module contains tests for:
- get_user_data: Core function for retrieving user data from devices
- get_user_data_api: API endpoint wrapper for get_user_data
- ui_get_user_data: UI endpoint wrapper for get_user_data
"""

from unittest.mock import AsyncMock, MagicMock, patch

import pendulum
import pytest
from ciba_iot_etl.extract.withings_api.common import MeasureGetMeasResponse
from ciba_iot_etl.models.pydantic.common import ActivityDevice
from fastapi import HTTPException

from app.routers.manager import get_user_data, get_user_data_api, ui_get_user_data
from tests.unit.routers.manager_test_utils import (
    WITHINGS_MODULE,
    FITBIT_MODULE,
    MEMBER_GET,
    GET_USER_DATA,
    test_member,
)
from tests.unit.test_utils_test import (
    PatchContextManager,
    TestAssertions,
)


@pytest.mark.asyncio
async def test_get_user_data_with_not_found():
    """
    get_user_data should raise 404 when provided email not found.
    """
    with PatchContextManager.member_not_found(MEMBER_GET):
        with pytest.raises(HTTPException) as expected_error:
            await get_user_data("aaaaah!", ActivityDevice.WITHINGS)

        TestAssertions.assert_http_exception(expected_error, 404)


@pytest.mark.asyncio
async def test_get_user_data_with_invalid_device():
    """
    get_user_data should raise 400 when provided device is not supported.
    """
    with PatchContextManager.member_found(test_member, MEMBER_GET):
        with pytest.raises(HTTPException) as expected_error:
            await get_user_data("<EMAIL>", ActivityDevice.DEXCOM)

        TestAssertions.assert_http_exception(expected_error, 400)


@pytest.mark.asyncio
async def test_get_user_data_for_fitbit(mock_device_model):
    """
    get_user_data should return the member fitbit data.
    """
    test_fitbit = MagicMock()
    test_fitbit.id = "fitbit_id"
    test_fitbit.access_token = "fitbit_token"
    is_expired_mock = MagicMock(return_value=False)
    test_fitbit.is_access_token_expired = is_expired_mock
    fitbit_mock, fitbit_mock_actions = mock_device_model
    fitbit_mock_actions["filter"].first = AsyncMock(return_value=test_fitbit)

    with (
        patch(FITBIT_MODULE, fitbit_mock),
        patch(
            "app.routers.manager.fetch_fitbit_data",
            new_callable=AsyncMock,
            return_value={},
        ) as fetch_mock,
        patch(
            MEMBER_GET,
            new_callable=AsyncMock,
            return_value=test_member,
        ),
    ):
        actual_value = await get_user_data(
            test_member.email, ActivityDevice.FITBIT
        )

        assert actual_value == {}
        fetch_mock.assert_awaited_once()


@pytest.mark.asyncio
async def test_get_user_data_for_withings(mock_device_model):
    """
    get_user_data should return the member withings data.
    """
    test_withings = MagicMock()
    test_withings.id = "withings_id"
    test_withings.access_token = "test_token"
    withings_mock, withings_mock_actions = mock_device_model
    withings_mock_actions["filter"].first = AsyncMock(
        return_value=test_withings
    )
    test_withings_response = MeasureGetMeasResponse(
        measuregrps=[],
        timezone="America/New_York",
        updatetime=pendulum.parse("2025-01-10"),
    )

    with (
        patch(WITHINGS_MODULE, withings_mock),
        patch(
            "app.routers.manager.process_user_message",
            new_callable=AsyncMock,
            return_value=test_withings_response,
        ) as process_mock,
        patch(
            MEMBER_GET,
            new_callable=AsyncMock,
            return_value=test_member,
        ),
    ):
        actual_value = await get_user_data(
            test_member.email,
            ActivityDevice.WITHINGS,
            pendulum.parse("2025-01-01"),
            pendulum.parse("2025-01-15"),
        )

        assert actual_value == {
            "blood_pressure_measurements": [],
            "heart_rate_measurements": [],
            "weight_measurements": [],
        }
        process_mock.assert_awaited_once()


@pytest.mark.asyncio
async def test_get_user_data_api(mock_device_model):
    """
    get_user_data_api should call get_user_data with the provided parameters.
    """
    with patch(
        GET_USER_DATA, new_callable=AsyncMock, return_value={}
    ) as get_data_mock:
        test_start = pendulum.parse("2025-02-20")
        test_end = pendulum.parse("2025-02-28")
        actual_value = await get_user_data_api(
            test_member.email,
            ActivityDevice.WITHINGS,
            test_start,
            test_end,
        )

        assert actual_value == {}
        get_data_mock.assert_awaited_once_with(
            test_member.email,
            ActivityDevice.WITHINGS,
            test_start,
            test_end,
        )


@pytest.mark.asyncio
async def test_ui_get_user_data():
    """
    ui_get_user_data should call get_user_data with correct arguments.
    """
    with patch(
        "app.routers.manager.get_user_data", new_callable=AsyncMock
    ) as mock_get_user_data:
        await ui_get_user_data(test_member.email, ActivityDevice.DEXCOM)

        mock_get_user_data.assert_awaited_once_with(
            test_member.email, ActivityDevice.DEXCOM, None, None
        )
