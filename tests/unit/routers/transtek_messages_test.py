SCALE_STATUS = {
    "createdAt": 1750782511,
    "deviceId": "************",
    "isTest": False,
    "messageType": "status",
    "modelNumber": "GBS-2104-G",
    "status": {
        "appv": "1.0.3.1",
        "at_t": 7,
        "bat": 100,
        "data_type": "scale_gen2_status",
        "fv": "17",
        "hv": "TSF2104-20210828",
        "iccid": "8944501211215714336f",
        "imei": "016338001452388",
        "imsi": "234500067571433",
        "mcuv": "1.1.7.1",
        "mfr": "lifesense",
        "net": "eMTC",
        "ops": "T-Mobile",
        "sig": 12,
        "tz": "UTC-7",
        "uid": "************",
    },
}

SCALE_TELEMETRY = {
    "createdAt": 1750782513,
    "data": {
        "bat": 100,
        "data_type": "scale_gen2_measure",
        "iccid": "8944501211215714336f",
        "imei": "016338001452388",
        "lts": 7,
        "sig": 12,
        "ts": 1750782491,
        "tz": "UTC-7",
        "uid": "************",
        "wet": 0,
        "wt": 75200,
    },
    "deviceId": "************",
    "isTest": False,
    "messageType": "telemetry",
    "modelNumber": "GBS-2104-G",
}

BPM_STATUS = {
    "createdAt": 1750782541,
    "deviceId": "100251300981",
    "isTest": False,
    "messageType": "status",
    "modelNumber": "TMB-2092-G",
    "status": {
        "at_t": 6,
        "bat": 100,
        "data_type": "bpm_gen2_status",
        "imei": "016323002143673",
        "net": "eMTC",
        "ops": "AT&T",
        "sig": 12,
        "tp": 212,
        "tz": "UTC-7",
    },
}

BPM_ANALYSIS = {
    "createdAt": 1750782543,
    "deviceId": "100251300981",
    "isTest": False,
    "messageType": "status",
    "modelNumber": "TMB-2092-G",
    "status": {
        "data_type": "bpm_gen2_analysis",
        "imei": "016323002143673",
        "100it": 953,
        "50it": 478,
        "op": 1,
        "ovip": 161,
        "ovit": 1690,
        "pn": 0,
        "wp": 160,
        "zp": 4085,
    },
}

BPM_TELEMETRY = {
    "createdAt": 1750782542,
    "data": {
        "bat": 100,
        "data_type": "bpm_gen2_measure",
        "dia": 96,
        "hand": False,
        "iccid": "89011703324488934642",
        "ihb": False,
        "imei": "016323002143673",
        "pul": 112,
        "sig": 13,
        "sn": "100251300981",
        "sys": 125,
        "tri": False,
        "ts": 1750782514,
        "tz": "UTC-7",
        "user": 1,
    },
    "deviceId": "100251300981",
    "isTest": False,
    "messageType": "telemetry",
    "modelNumber": "TMB-2092-G",
}


SCALE_ERROR = {
    "deviceId": "************",
    "createdAt": 1750715294,
    "status": {
        "imei": "************",
        "data_type": "scale_gen2_err",
        "err_ts": 1750714881,
        "err_c": 16,
    },
    "isTest": False,
    "modelNumber": "GBS-2104-G",
    "messageType": "status",
}


BPM_ERROR = {
    "deviceId": "************",
    "createdAt": 1750715294,
    "status": {
        "imei": "************",
        "data_type": "bpm_gen2_err",
        "err_ts": 1750714881,
        "err_c": 100,
    },
    "isTest": False,
    "modelNumber": "GBS-2104-G",
    "messageType": "status",
}


UNKNOWN_DEVICE = {
    "deviceId": "************",
    "createdAt": 1750715294,
    "status": {
        "data_type": "magical_device_status",
        "a": 0,
        "b": 1,
        "c": "d",
    },
    "isTest": False,
    "modelNumber": "XXX-1234-G",
    "messageType": "status",
}

TEST_TELEMETRY = {
    "deviceId": "forward-telemetry-device-id",
    "createdAt": 1750858551,
    "data": {
        "wt": 71763,
        "bmi": 0,
        "fat": 0,
        "bm": 0,
        "mus": 0,
        "ts": 1750858551,
    },
    "modelNumber": "forward-telemetry-model-number",
    "messageType": "telemetry",
    "isTest": True,
}

TEST_STATUS = {
    "deviceId": "forward-status-device-id",
    "createdAt": 1750858595,
    "status": {
        "mfr": "Transtek",
        "hv": "H001",
        "fv": "F001",
        "uid": "forward-status-device-id",
        "imei": "044531931966344",
        "imsi": "325481707291435",
        "did": "forward-status-device-id",
        "bat": 37,
        "sig": 26,
    },
    "modelNumber": "forward-status-model-number",
    "messageType": "status",
    "isTest": True,
}
