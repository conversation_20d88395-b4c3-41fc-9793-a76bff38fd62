from unittest.mock import AsyncMock, Mock, patch
from uuid import uuid4
from datetime import datetime

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from ciba_iot_etl.models.db.transtek import Transtek
from ciba_iot_etl.models.db.member import Member
from ciba_iot_etl.models.pydantic.transtek import (
    TranstekDeviceType,
    TranstekStatus,
)
from ciba_iot_etl.models.pydantic.common import Carrier

from app.main import app

test_member_id = str(uuid4())
test_device_id = "test_dev_123"  # Max 12 characters
test_imei = "123456789012345"  # Max 15 characters
test_exception = Exception("Test exception")


@pytest.fixture
def client():
    return TestClient(app)


@pytest.fixture
def mock_member():
    """Configure a mock Member for testing purposes"""
    member = Mock(spec=Member)
    member.id = uuid4()
    member.email = "<EMAIL>"
    return member


@pytest.fixture
def mock_transtek_device():
    """Configure a mock Transtek device for testing purposes"""
    device = Mock(spec=Transtek)
    device.id = uuid4()
    device.device_id = test_device_id
    device.imei = test_imei
    device.model = "GBS-2104-G"
    device.device_type = TranstekDeviceType.SCALE
    device.tracking_number = "1Z999AA1234567890"
    device.carrier = Carrier.UPS
    device.timezone = "UTC"
    device.last_status_report = {"status": "active"}
    device.status = TranstekStatus.ACTIVE
    device.created_at = datetime.now()
    device.updated_at = datetime.now()
    device.member_id = uuid4()  # Should be UUID, not string
    device.tracking_url = (
        "https://www.ups.com/track?track=yes&trackNums=1Z999AA1234567890"
    )
    return device


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_member_id",
    new_callable=AsyncMock,
)
@patch(
    "app.routers.transtek.Member.get_by_platform",
    new_callable=AsyncMock,
)
async def test_get_device_by_member_id_success(
    mock_get_by_platform,
    mock_get_device_by_member_id,
    client,
    mock_member,
    mock_transtek_device,
):
    """get_device should successfully return device when found by member_id with member_type"""
    mock_get_by_platform.return_value = mock_member
    mock_get_device_by_member_id.return_value = mock_transtek_device

    response = client.get(
        f"/transtek/device?member_id={test_member_id}&member_type=participant"
    )

    assert response.status_code == status.HTTP_200_OK

    response_data = response.json()
    assert "id" in response_data
    assert "device_id" in response_data
    assert "imei" in response_data
    assert "model" in response_data
    assert "device_type" in response_data
    assert "status" in response_data
    assert "created_at" in response_data
    assert "updated_at" in response_data

    assert response_data["device_id"] == test_device_id
    assert response_data["imei"] == test_imei
    assert response_data["model"] == "GBS-2104-G"

    # Verify the two-step lookup process
    mock_get_by_platform.assert_called_once_with(
        platform_type="participant", platform_id=test_member_id
    )
    mock_get_device_by_member_id.assert_called_once_with(str(mock_member.id))


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_device_id",
    new_callable=AsyncMock,
)
async def test_get_device_by_device_id_success(
    mock_get_device_by_device_id, client, mock_transtek_device
):
    """get_device should successfully return device when found by device_id"""
    mock_get_device_by_device_id.return_value = mock_transtek_device

    response = client.get(f"/transtek/device?device_id={test_device_id}")

    assert response.status_code == status.HTTP_200_OK

    response_data = response.json()
    assert response_data["device_id"] == test_device_id
    assert response_data["imei"] == test_imei

    mock_get_device_by_device_id.assert_called_once_with(test_device_id)


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_imei",
    new_callable=AsyncMock,
)
async def test_get_device_by_imei_success(
    mock_get_device_by_imei, client, mock_transtek_device
):
    """get_device should successfully return device when found by imei"""
    mock_get_device_by_imei.return_value = mock_transtek_device

    response = client.get(f"/transtek/device?imei={test_imei}")

    assert response.status_code == status.HTTP_200_OK

    response_data = response.json()
    assert response_data["device_id"] == test_device_id
    assert response_data["imei"] == test_imei

    mock_get_device_by_imei.assert_called_once_with(test_imei)


@pytest.mark.asyncio
async def test_get_device_no_parameters(client):
    """get_device should return 400 when no parameters are provided"""
    response = client.get("/transtek/device")

    assert response.status_code == status.HTTP_400_BAD_REQUEST

    response_data = response.json()
    assert "detail" in response_data
    assert (
        response_data["detail"]
        == "One parameter is required: member_id (with member_type), device_id, or imei"
    )


@pytest.mark.asyncio
async def test_get_device_member_id_without_member_type(client):
    """get_device should return 400 when member_id is provided without member_type"""
    response = client.get(f"/transtek/device?member_id={test_member_id}")

    assert response.status_code == status.HTTP_400_BAD_REQUEST

    response_data = response.json()
    assert "detail" in response_data
    assert (
        response_data["detail"]
        == "member_id and member_type must be provided together"
    )


@pytest.mark.asyncio
async def test_get_device_member_type_without_member_id(client):
    """get_device should return 400 when member_type is provided without member_id"""
    response = client.get("/transtek/device?member_type=participant")

    assert response.status_code == status.HTTP_400_BAD_REQUEST

    response_data = response.json()
    assert "detail" in response_data
    assert (
        response_data["detail"]
        == "member_id and member_type must be provided together"
    )


@pytest.mark.asyncio
async def test_get_device_multiple_parameters(client):
    """get_device should return 400 when multiple search parameters are provided"""
    response = client.get(
        f"/transtek/device?member_id={test_member_id}&member_type=participant&device_id={test_device_id}"
    )

    assert response.status_code == status.HTTP_400_BAD_REQUEST

    response_data = response.json()
    assert "detail" in response_data
    assert (
        response_data["detail"]
        == "One parameter is required: member_id (with member_type), device_id, or imei"
    )


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.Member.get_by_platform",
    new_callable=AsyncMock,
)
async def test_get_device_by_member_id_member_not_found(
    mock_get_by_platform, client
):
    """get_device should return 404 when member is not found by platform lookup"""
    mock_get_by_platform.return_value = None

    response = client.get(
        f"/transtek/device?member_id={test_member_id}&member_type=participant"
    )

    assert response.status_code == status.HTTP_404_NOT_FOUND

    response_data = response.json()
    assert "detail" in response_data
    assert response_data["detail"] == "Member not found"

    mock_get_by_platform.assert_called_once_with(
        platform_type="participant", platform_id=test_member_id
    )


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_member_id",
    new_callable=AsyncMock,
)
@patch(
    "app.routers.transtek.Member.get_by_platform",
    new_callable=AsyncMock,
)
async def test_get_device_by_member_id_device_not_found(
    mock_get_by_platform, mock_get_device_by_member_id, client, mock_member
):
    """get_device should return 404 when device is not found for the member"""
    mock_get_by_platform.return_value = mock_member
    mock_get_device_by_member_id.return_value = None

    response = client.get(
        f"/transtek/device?member_id={test_member_id}&member_type=participant"
    )

    assert response.status_code == status.HTTP_404_NOT_FOUND

    response_data = response.json()
    assert "detail" in response_data
    assert response_data["detail"] == "Device not found"

    mock_get_by_platform.assert_called_once_with(
        platform_type="participant", platform_id=test_member_id
    )
    mock_get_device_by_member_id.assert_called_once_with(str(mock_member.id))


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_device_id",
    new_callable=AsyncMock,
)
async def test_get_device_by_device_id_not_found(
    mock_get_device_by_device_id, client
):
    """get_device should return 404 when device is not found by device_id"""
    mock_get_device_by_device_id.return_value = None

    response = client.get(f"/transtek/device?device_id={test_device_id}")

    assert response.status_code == status.HTTP_404_NOT_FOUND

    response_data = response.json()
    assert "detail" in response_data
    assert response_data["detail"] == "Device not found"

    mock_get_device_by_device_id.assert_called_once_with(test_device_id)


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_imei",
    new_callable=AsyncMock,
)
async def test_get_device_by_imei_not_found(mock_get_device_by_imei, client):
    """get_device should return 404 when device is not found by imei"""
    mock_get_device_by_imei.return_value = None

    response = client.get(f"/transtek/device?imei={test_imei}")

    assert response.status_code == status.HTTP_404_NOT_FOUND

    response_data = response.json()
    assert "detail" in response_data
    assert response_data["detail"] == "Device not found"

    mock_get_device_by_imei.assert_called_once_with(test_imei)


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.Member.get_by_platform",
    new_callable=AsyncMock,
)
async def test_get_device_by_member_id_member_lookup_internal_server_error(
    mock_get_by_platform, client
):
    """get_device should return 500 when member lookup raises unexpected exception"""
    mock_get_by_platform.side_effect = test_exception

    response = client.get(
        f"/transtek/device?member_id={test_member_id}&member_type=participant"
    )

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    response_data = response.json()
    assert "detail" in response_data
    assert "Internal server error: Test exception" in response_data["detail"]

    mock_get_by_platform.assert_called_once_with(
        platform_type="participant", platform_id=test_member_id
    )


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_member_id",
    new_callable=AsyncMock,
)
@patch(
    "app.routers.transtek.Member.get_by_platform",
    new_callable=AsyncMock,
)
async def test_get_device_by_member_id_device_lookup_internal_server_error(
    mock_get_by_platform, mock_get_device_by_member_id, client, mock_member
):
    """get_device should return 500 when device lookup raises unexpected exception"""
    mock_get_by_platform.return_value = mock_member
    mock_get_device_by_member_id.side_effect = test_exception

    response = client.get(
        f"/transtek/device?member_id={test_member_id}&member_type=participant"
    )

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    response_data = response.json()
    assert "detail" in response_data
    assert "Internal server error: Test exception" in response_data["detail"]

    mock_get_by_platform.assert_called_once_with(
        platform_type="participant", platform_id=test_member_id
    )
    mock_get_device_by_member_id.assert_called_once_with(str(mock_member.id))


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_device_id",
    new_callable=AsyncMock,
)
async def test_get_device_by_device_id_internal_server_error(
    mock_get_device_by_device_id, client
):
    """get_device should return 500 when repository method raises unexpected exception"""
    mock_get_device_by_device_id.side_effect = test_exception

    response = client.get(f"/transtek/device?device_id={test_device_id}")

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    response_data = response.json()
    assert "detail" in response_data
    assert "Internal server error: Test exception" in response_data["detail"]

    mock_get_device_by_device_id.assert_called_once_with(test_device_id)


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_imei",
    new_callable=AsyncMock,
)
async def test_get_device_by_imei_internal_server_error(
    mock_get_device_by_imei, client
):
    """get_device should return 500 when repository method raises unexpected exception"""
    mock_get_device_by_imei.side_effect = test_exception

    response = client.get(f"/transtek/device?imei={test_imei}")

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    response_data = response.json()
    assert "detail" in response_data
    assert "Internal server error: Test exception" in response_data["detail"]

    mock_get_device_by_imei.assert_called_once_with(test_imei)


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_member_id",
    new_callable=AsyncMock,
)
@patch(
    "app.routers.transtek.Member.get_by_platform",
    new_callable=AsyncMock,
)
async def test_get_device_response_structure_validation(
    mock_get_by_platform,
    mock_get_device_by_member_id,
    client,
    mock_member,
    mock_transtek_device,
):
    """get_device should return response matching TranstekResponse model structure"""
    mock_get_by_platform.return_value = mock_member
    mock_get_device_by_member_id.return_value = mock_transtek_device

    response = client.get(
        f"/transtek/device?member_id={test_member_id}&member_type=participant"
    )

    assert response.status_code == status.HTTP_200_OK

    response_data = response.json()

    # Verify response structure matches TranstekResponse model
    required_fields = [
        "id",
        "device_id",
        "imei",
        "model",
        "device_type",
        "status",
        "created_at",
        "updated_at",
    ]
    for field in required_fields:
        assert field in response_data
        assert response_data[field] is not None

    optional_fields = [
        "tracking_number",
        "carrier",
        "timezone",
        "last_status_report",
        "member_id",
    ]
    for field in optional_fields:
        assert field in response_data  # Field should exist but can be null

    # Verify data types
    assert isinstance(response_data["id"], str)
    assert isinstance(response_data["device_id"], str)
    assert isinstance(response_data["imei"], str)
    assert isinstance(response_data["model"], str)
    assert isinstance(response_data["device_type"], str)
    assert isinstance(response_data["status"], str)
    assert isinstance(response_data["created_at"], str)
    assert isinstance(response_data["updated_at"], str)


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_device_id",
    new_callable=AsyncMock,
)
async def test_get_device_with_minimal_device_data(
    mock_get_device_by_device_id, client
):
    """get_device should handle device with minimal required fields"""
    minimal_device = Mock(spec=Transtek)
    minimal_device.id = uuid4()
    minimal_device.device_id = test_device_id
    minimal_device.imei = test_imei
    minimal_device.model = "GBS-2104-G"
    minimal_device.device_type = TranstekDeviceType.SCALE
    minimal_device.tracking_number = None
    minimal_device.carrier = None
    minimal_device.timezone = None
    minimal_device.last_status_report = None
    minimal_device.status = TranstekStatus.PAIRED
    minimal_device.created_at = datetime.now()
    minimal_device.updated_at = datetime.now()
    minimal_device.member_id = None
    minimal_device.tracking_url = ""  # Empty string when no tracking info

    mock_get_device_by_device_id.return_value = minimal_device

    response = client.get(f"/transtek/device?device_id={test_device_id}")

    assert response.status_code == status.HTTP_200_OK

    response_data = response.json()
    assert response_data["device_id"] == test_device_id
    assert response_data["imei"] == test_imei
    assert response_data["tracking_number"] is None
    assert response_data["carrier"] is None
    assert response_data["timezone"] is None
    assert response_data["last_status_report"] is None
    assert response_data["member_id"] is None


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_imei",
    new_callable=AsyncMock,
)
async def test_get_device_with_different_device_types(
    mock_get_device_by_imei, client
):
    """get_device should handle different device types correctly"""
    bpm_device = Mock(spec=Transtek)
    bpm_device.id = uuid4()
    bpm_device.device_id = "bpm_dev_456"
    bpm_device.imei = "987654321098765"
    bpm_device.model = "TMB-2092-G"
    bpm_device.device_type = TranstekDeviceType.BPM
    bpm_device.tracking_number = "1Z999BB9876543210"
    bpm_device.carrier = Carrier.FEDEX
    bpm_device.timezone = "America/New_York"
    bpm_device.last_status_report = {"status": "paired", "battery": "85%"}
    bpm_device.status = TranstekStatus.PAIRED
    bpm_device.created_at = datetime.now()
    bpm_device.updated_at = datetime.now()
    bpm_device.member_id = uuid4()
    bpm_device.tracking_url = (
        "https://www.fedex.com/fedextrack/?trknbr=1Z999BB9876543210"
    )

    mock_get_device_by_imei.return_value = bpm_device

    response = client.get(f"/transtek/device?imei={bpm_device.imei}")

    assert response.status_code == status.HTTP_200_OK

    response_data = response.json()
    assert response_data["device_id"] == "bpm_dev_456"
    assert response_data["imei"] == "987654321098765"
    assert response_data["model"] == "TMB-2092-G"
    assert response_data["device_type"] == TranstekDeviceType.BPM
    assert response_data["carrier"] == Carrier.FEDEX
    assert response_data["timezone"] == "America/New_York"
    assert response_data["last_status_report"]["battery"] == "85%"


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_member_id",
    new_callable=AsyncMock,
)
@patch(
    "app.routers.transtek.Member.get_by_platform",
    new_callable=AsyncMock,
)
async def test_get_device_with_different_status_values(
    mock_get_by_platform,
    mock_get_device_by_member_id,
    client,
    mock_member,
    mock_transtek_device,
):
    """get_device should handle different device status values correctly"""
    mock_get_by_platform.return_value = mock_member

    status_values = [
        TranstekStatus.AVAILABLE,
        TranstekStatus.PAIRED,
        TranstekStatus.SHIPPED,
        TranstekStatus.ACTIVE,
        TranstekStatus.DISCONNECTED,
    ]

    for status_value in status_values:
        mock_transtek_device.status = status_value
        mock_get_device_by_member_id.return_value = mock_transtek_device

        response = client.get(
            f"/transtek/device?member_id={test_member_id}&member_type=participant"
        )

        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert response_data["status"] == status_value

        mock_get_device_by_member_id.reset_mock()
        mock_get_by_platform.reset_mock()


@pytest.mark.asyncio
@patch(
    "app.routers.transtek.TranstekRepository.get_device_by_member_id",
    new_callable=AsyncMock,
)
@patch(
    "app.routers.transtek.Member.get_by_platform",
    new_callable=AsyncMock,
)
async def test_get_device_by_member_id_patient_type_success(
    mock_get_by_platform,
    mock_get_device_by_member_id,
    client,
    mock_member,
    mock_transtek_device,
):
    """get_device should successfully return device when found by member_id with patient member_type"""
    mock_get_by_platform.return_value = mock_member
    mock_get_device_by_member_id.return_value = mock_transtek_device

    response = client.get(
        f"/transtek/device?member_id={test_member_id}&member_type=patient"
    )

    assert response.status_code == status.HTTP_200_OK

    response_data = response.json()
    assert response_data["device_id"] == test_device_id
    assert response_data["imei"] == test_imei

    # Verify the two-step lookup process with patient type
    mock_get_by_platform.assert_called_once_with(
        platform_type="patient", platform_id=test_member_id
    )
    mock_get_device_by_member_id.assert_called_once_with(str(mock_member.id))
