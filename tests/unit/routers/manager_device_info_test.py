"""
Tests for device information functionality in the manager router.

This module contains tests for:
- ui_get_device_info: Get device information for users
"""

from unittest.mock import AsyncMock, patch

import pytest
from ciba_iot_etl.models.pydantic.common import ActivityDevice

from app.routers.manager import ui_get_device_info
from tests.unit.routers.manager_test_utils_test import (
    WITHINGS_MODULE,
    FITBIT_MODULE,
    WITHINGS_API_CALL,
    FITBIT_CALL_API,
    MEMBER_GET,
    test_member,
    fitbit_test_devices,
    ManagerTestAssertions,
    ManagerTestData,
)
from tests.unit.test_utils_test import (
    PatchContextManager,
)


@pytest.mark.asyncio
async def test_ui_get_device_info_with_user_not_found():
    """
    ui_get_device_info should return unsuccessful response
    when no user found.
    """
    with PatchContextManager.member_not_found(MEMBER_GET):
        actual_value = await ui_get_device_info(
            "random", ActivityDevice.WITHINGS
        )

        ManagerTestAssertions.assert_error_response(
            actual_value, "Member not found: random"
        )


@pytest.mark.asyncio
async def test_ui_get_device_info_with_unsupported_device():
    """
    ui_get_device_info should return unsuccessful response
    when no supported device is provided.
    """
    with patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member):
        actual_value = await ui_get_device_info(
            test_member.email, ActivityDevice.OURARING
        )

        assert actual_value == {
            "success": False,
            "error": "Unsupported device type: ActivityDevice.OURARING",
        }


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_device, test_module, error_prefix",
    [
        (ActivityDevice.FITBIT, FITBIT_MODULE, "Fitbit"),
        (ActivityDevice.WITHINGS, WITHINGS_MODULE, "Withings"),
    ],
)
async def test_ui_get_device_info_for_device_not_found(
    test_device, test_module, error_prefix, mock_device_model
):
    """
    ui_get_device_info should return unsuccessful response
    when activity device is not found.
    """
    with (
        PatchContextManager.member_found(test_member, MEMBER_GET),
        PatchContextManager.device_not_found(test_module, mock_device_model),
    ):
        actual_value = await ui_get_device_info(test_member.email, test_device)

        ManagerTestAssertions.assert_error_response(
            actual_value,
            f"{error_prefix} connection not found for member: {test_member.email}",
        )


@pytest.mark.asyncio
async def test_ui_get_device_info_for_withings_with_exception(
    mock_device_model,
):
    """
    ui_get_device_info should return unsuccessful response
    when withings device call raises an exception.
    """
    test_device = ManagerTestData.create_test_withings_device()
    withings_mock, withings_mock_actions = mock_device_model
    withings_mock_actions["filter"].first = AsyncMock(return_value=test_device)

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(WITHINGS_MODULE, withings_mock),
        patch(
            WITHINGS_API_CALL,
            new_callable=AsyncMock,
            side_effect=Exception(),
        ),
    ):
        actual_value = await ui_get_device_info(
            test_member.email, ActivityDevice.WITHINGS
        )

        assert actual_value == {
            "success": False,
            "error": "Failed to get device info: ",
        }


@pytest.mark.asyncio
@pytest.mark.parametrize("test_error_code", [401, 601, 501])
async def test_ui_get_device_info_for_withings_with_errors(
    test_error_code, mock_device_model
):
    """
    ui_get_device_info should return unsuccessful response
    when withings device returns error codes.
    """
    test_device = ManagerTestData.create_test_withings_device()
    withings_mock, withings_mock_actions = mock_device_model
    withings_mock_actions["filter"].first = AsyncMock(return_value=test_device)
    test_response = {"status": test_error_code}

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(WITHINGS_MODULE, withings_mock),
        patch(
            WITHINGS_API_CALL,
            new_callable=AsyncMock,
            return_value=test_response,
        ),
    ):
        actual_value = await ui_get_device_info(
            test_member.email, ActivityDevice.WITHINGS
        )

        assert actual_value["success"] is False


@pytest.mark.asyncio
async def test_ui_get_device_info_for_withings_success(mock_device_model):
    """
    ui_get_device_info should return successful response
    including withings linked devices.
    """
    test_device = ManagerTestData.create_test_withings_device()
    withings_mock, withings_mock_actions = mock_device_model
    withings_mock_actions["filter"].first = AsyncMock(return_value=test_device)
    test_response = ManagerTestData.create_withings_api_response()

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(WITHINGS_MODULE, withings_mock),
        patch(
            WITHINGS_API_CALL,
            new_callable=AsyncMock,
            return_value=test_response,
        ),
        patch(
            "app.routers.manager.Withings.expire_old_refresh_token",
            new_callable=AsyncMock,
        ),
    ):
        actual_value = await ui_get_device_info(
            test_member.email, ActivityDevice.WITHINGS
        )

        assert actual_value["success"] is True
        assert actual_value["devices"] == [
            {
                "first_session_date": "a",
                "last_session_date": "b",
            },
            {
                "first_session_date": "2025-06-01 05:00:00",
                "last_session_date": "2025-06-02 05:00:00",
            },
        ]


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_error",
    [
        {},
        {"type": "test", "message": "message"},
        {"type": "expired_token", "message": "token expired"},
        {"type": "invalid_token", "message": "token not valid"},
        {"type": "rate_limit_exceeded", "message": "limit exceeded"},
    ],
)
async def test_ui_get_device_info_for_fitbit_with_error(
    test_error, mock_device_model
):
    """
    ui_get_device_info should return unsuccessful response
    when the fitbit device returns errors.
    """
    test_device = ManagerTestData.create_test_fitbit_device()
    fitbit_mock, fitbit_mock_actions = mock_device_model
    fitbit_mock_actions["filter"].first = AsyncMock(return_value=test_device)
    test_response = {"error": test_error}

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(FITBIT_MODULE, fitbit_mock),
        patch(
            FITBIT_CALL_API,
            new_callable=AsyncMock,
            return_value=test_response,
        ),
    ):
        actual_value = await ui_get_device_info(
            test_member.email, ActivityDevice.FITBIT
        )

        assert actual_value["success"] is False


@pytest.mark.asyncio
async def test_ui_get_device_info_for_fitbit_with_exception(mock_device_model):
    """
    ui_get_device_info should return unsuccessful response
    when the fitbit call raises an exception.
    """
    test_device = ManagerTestData.create_test_fitbit_device()
    test_device.access_token = "fitbit_access"
    fitbit_mock, fitbit_mock_actions = mock_device_model
    fitbit_mock_actions["filter"].first = AsyncMock(return_value=test_device)

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(FITBIT_MODULE, fitbit_mock),
        patch(
            FITBIT_CALL_API,
            new_callable=AsyncMock,
            side_effect=Exception(),
        ),
    ):
        actual_value = await ui_get_device_info(
            test_member.email, ActivityDevice.FITBIT
        )

        assert actual_value == {
            "success": False,
            "error": "Failed to get device info: ",
        }


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_response, expected_response",
    [
        ({}, []),
        (fitbit_test_devices, fitbit_test_devices),
    ],
)
async def test_ui_get_device_info_for_fitbit_success(
    test_response, expected_response, mock_device_model
):
    """
    ui_get_device_info should return successful response
    with fitbit devices list.
    """
    test_device = ManagerTestData.create_test_fitbit_device()
    fitbit_mock, fitbit_mock_actions = mock_device_model
    fitbit_mock_actions["filter"].first = AsyncMock(return_value=test_device)

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(FITBIT_MODULE, fitbit_mock),
        patch(
            FITBIT_CALL_API,
            new_callable=AsyncMock,
            return_value=test_response,
        ),
    ):
        actual_value = await ui_get_device_info(
            test_member.email, ActivityDevice.FITBIT
        )

        assert actual_value["success"] is True
        assert actual_value["devices"] == expected_response
