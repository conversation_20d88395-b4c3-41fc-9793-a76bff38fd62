from datetime import date
from http import HTT<PERSON>tatus
from unittest.mock import AsyncMock

import pytest
from fastapi.exceptions import HTTPException

from app.common.exceptions import MeasuresException
from app.pydantic_model.measures_api import (
    DashboardSummary,
    DistanceUnit,
    WeightUnit,
    MeasureType,
    GroupOption,
    MeasuresResponse,
    GlucoseMeasuresResponse,
)
from app.routers.measures import get_measures, get_summary
from tests.unit.common import TEST_MEMBER_ID

TEST_TYPE = "patient"
test_start = date(2020, 1, 1)
test_end = date(2020, 1, 31)


@pytest.fixture
def mock_get_member(mocker) -> AsyncMock:
    """
    fixture to mock Member.get_by_platform method
    """
    # Create a simple AsyncMock without trying to spec the Member class
    mocked_get_member = mocker.patch(
        "ciba_iot_etl.models.db.member.Member.get_by_platform",
        new_callable=AsyncMock,
    )
    return mocked_get_member


@pytest.mark.asyncio
async def test_get_summary_1():
    """
    get_summary should throw a bad request exception when provided date filters are invalid.
    """
    with pytest.raises(HTTPException) as actual_exception:
        await get_summary(
            TEST_MEMBER_ID,
            TEST_TYPE,
            test_end,
            test_start,
        )

    assert actual_exception.value.status_code == HTTPStatus.BAD_REQUEST


@pytest.mark.asyncio
async def test_get_summary_2(mock_get_member):
    """
    get_summary should throw a not found exception when provided member id is not found.
    """
    mock_get_member.return_value = None

    with pytest.raises(HTTPException) as actual_exception:
        await get_summary(
            TEST_MEMBER_ID,
            TEST_TYPE,
            test_start,
            test_end,
        )

    assert actual_exception.value.status_code == HTTPStatus.NOT_FOUND


@pytest.mark.asyncio
async def test_get_summary_3(mock_get_member, mocker):
    """
    get_summary should throw an internal server exception when measures retrieval fails.
    """
    mocked_member = (
        AsyncMock()
    )  # Don't use spec=Member to avoid InvalidSpecError
    mocked_member.id = TEST_MEMBER_ID
    mock_get_member.return_value = mocked_member
    mocked_get_summary = mocker.patch(
        "app.services.measures.weight.WeightService.get_dashboard_summary",
        new_callable=AsyncMock,
    )
    mocked_get_summary.side_effect = MeasuresException("error on get summary")

    with pytest.raises(HTTPException) as actual_exception:
        await get_summary(
            TEST_MEMBER_ID,
            TEST_TYPE,
            test_start,
            test_end,
            WeightUnit.KG,
            DistanceUnit.MI,
        )

    assert (
        actual_exception.value.status_code == HTTPStatus.INTERNAL_SERVER_ERROR
    )


@pytest.mark.asyncio
async def test_get_summary_4(mocker):
    """
    get_summary should return the DashboardSummary response.
    """
    mocked_get_member = mocker.patch(
        "ciba_iot_etl.models.db.member.Member.get_by_platform",
        new_callable=AsyncMock,
    )
    mocked_member = (
        AsyncMock()
    )  # Don't use spec=Member to avoid InvalidSpecError
    mocked_member.id = TEST_MEMBER_ID
    mocked_get_member.return_value = mocked_member
    mocked_get_weight_summary = mocker.patch(
        "app.services.measures.weight.WeightService.get_dashboard_summary",
        new_callable=AsyncMock,
    )
    mocked_get_weight_summary.return_value = None
    mocked_get_heart_rate_summary = mocker.patch(
        "app.services.measures.heart_rate.HeartRateService.get_dashboard_summary",
        new_callable=AsyncMock,
    )
    mocked_get_heart_rate_summary.return_value = None
    mocked_get_blood_pressure_summary = mocker.patch(
        "app.services.measures.blood_pressure.BloodPressureService.get_dashboard_summary",
        new_callable=AsyncMock,
    )
    mocked_get_blood_pressure_summary.return_value = None
    mocked_get_sleep_summary = mocker.patch(
        "app.services.measures.sleep.SleepService.get_dashboard_summary",
        new_callable=AsyncMock,
    )
    mocked_get_sleep_summary.return_value = None
    mocked_get_activity_summary = mocker.patch(
        "app.services.measures.activity.ActivityService.get_dashboard_summary",
        new_callable=AsyncMock,
    )
    mocked_get_activity_summary.return_value = None
    mocked_get_glucose_summary = mocker.patch(
        "app.services.measures.glucose_level.GlucoseLevelService.get_dashboard_summary",
        new_callable=AsyncMock,
    )
    mocked_get_glucose_summary.return_value = None

    actual_value = await get_summary(
        TEST_MEMBER_ID,
        TEST_TYPE,
        test_start,
        test_end,
        WeightUnit.KG,
        DistanceUnit.MI,
    )

    assert actual_value == DashboardSummary(
        bloodPressure=None,
        heartRate=None,
        weight=None,
        activity=None,
        sleep=None,
        glucoseLevel=None,
    )


@pytest.mark.asyncio
async def test_get_measures_1():
    """
    get_measures should throw bad request exception when provided date filters are invalid.
    """
    with pytest.raises(HTTPException) as actual_exception:
        await get_measures(
            MeasureType.BLOOD_PRESSURE,
            TEST_MEMBER_ID,
            TEST_TYPE,
            test_end,
            test_start,
            group_by=GroupOption.DAY,
        )

    assert actual_exception.value.status_code == HTTPStatus.BAD_REQUEST


@pytest.mark.asyncio
async def test_get_measures_2(mock_get_member):
    """
    get_measures should throw not found exception when provided member id is not found.
    """
    mock_get_member.return_value = None

    with pytest.raises(HTTPException) as actual_exception:
        await get_measures(
            MeasureType.WEIGHT,
            TEST_MEMBER_ID,
            TEST_TYPE,
            test_start,
            test_end,
            unit=WeightUnit.LB,
        )

    assert actual_exception.value.status_code == HTTPStatus.NOT_FOUND


@pytest.mark.asyncio
async def test_get_measures_3(mock_get_member):
    """
    get_measures should throw bad request exception when provided measure type is not valid.
    """
    mocked_member = (
        AsyncMock()
    )  # Don't use spec=Member to avoid InvalidSpecError
    mocked_member.id = TEST_MEMBER_ID
    mock_get_member.return_value = mocked_member

    with pytest.raises(HTTPException) as actual_exception:
        # Use a string cast to simulate an invalid measure type
        await get_measures(
            "random",  # type: ignore
            TEST_MEMBER_ID,
            TEST_TYPE,
            test_start,
            test_end,
            distance_unit=DistanceUnit.MI,
        )

    assert actual_exception.value.status_code == HTTPStatus.BAD_REQUEST


@pytest.mark.asyncio
async def test_get_measures_4(mock_get_member, mocker):
    """
    get_measures should throw internal error exception when get measures fails.
    """
    mocked_member = (
        AsyncMock()
    )  # Don't use spec=Member to avoid InvalidSpecError
    mocked_member.id = TEST_MEMBER_ID
    mock_get_member.return_value = mocked_member
    mocked_get_summary = mocker.patch(
        "app.services.measures.weight.WeightService.get_measures_data",
        new_callable=AsyncMock,
    )
    mocked_get_summary.side_effect = MeasuresException("error on get measures")

    with pytest.raises(HTTPException) as actual_exception:
        await get_measures(
            MeasureType.WEIGHT,
            TEST_MEMBER_ID,
            TEST_TYPE,
            test_start,
            test_end,
        )

    assert (
        actual_exception.value.status_code == HTTPStatus.INTERNAL_SERVER_ERROR
    )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_measure, module, expected_value",
    [
        (
            MeasureType.WEIGHT,
            "app.services.measures.weight.WeightService.get_measures_data",
            MeasuresResponse(
                unit="Kg", type=MeasureType.WEIGHT, summary=None, values=[]
            ),
        ),
        (
            MeasureType.HEART_RATE,
            "app.services.measures.heart_rate.HeartRateService.get_measures_data",
            MeasuresResponse(
                unit="bpm",
                type=MeasureType.HEART_RATE,
                summary=None,
                values=[],
            ),
        ),
        (
            MeasureType.BLOOD_PRESSURE,
            "app.services.measures.blood_pressure.BloodPressureService.get_measures_data",
            MeasuresResponse(
                unit="mmHg",
                type=MeasureType.BLOOD_PRESSURE,
                summary=None,
                values=[],
            ),
        ),
        (
            MeasureType.SLEEP,
            "app.services.measures.sleep.SleepService.get_measures_data",
            MeasuresResponse(
                unit="minutes", type=MeasureType.SLEEP, summary=None, values=[]
            ),
        ),
        (
            MeasureType.ACTIVITY,
            "app.services.measures.activity.ActivityService.get_measures_data",
            MeasuresResponse(
                unit="mi", type=MeasureType.ACTIVITY, summary=None, values=[]
            ),
        ),
        (
            MeasureType.GLUCOSE_LEVEL,
            "app.services.measures.glucose_level.GlucoseLevelService.get_measures_data",
            GlucoseMeasuresResponse(
                unit="mg/dL",
                type=MeasureType.WEIGHT,
                summary=None,
                values=[],
                hypoAndHyperEvents=[],
            ),
        ),
    ],
)
async def test_get_measures_5(
    test_measure, module, expected_value, mock_get_member, mocker
):
    """
    get_measures should return the requested measures.
    """
    mocked_member = (
        AsyncMock()
    )  # Don't use spec=Member to avoid InvalidSpecError
    mocked_member.id = TEST_MEMBER_ID
    mock_get_member.return_value = mocked_member
    mocked_get_summary = mocker.patch(module, new_callable=AsyncMock)
    mocked_get_summary.return_value = expected_value

    actual_value = await get_measures(
        test_measure,
        TEST_MEMBER_ID,
        TEST_TYPE,
        test_start,
        test_end,
    )

    assert actual_value == expected_value
