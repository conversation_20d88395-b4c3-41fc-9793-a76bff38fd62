"""
Shared test utilities and fixtures for manager router tests.

This module provides common fixtures, mock objects, and utility functions
that are used across multiple manager test files to reduce code duplication.
"""

from unittest.mock import AsyncMock, MagicMock
from uuid import UUID

import pytest
from fastapi import Request

from tests.unit.test_utils_test import (
    COMMON_PATCHES,
    create_mock_device_model,
    create_mock_member_model,
    create_mock_member_platform_model,
    create_test_member,
)

# Use shared patch paths from common test utilities
WITHINGS_MODULE = COMMON_PATCHES["withings_module"]
WITHINGS_REFRESH = COMMON_PATCHES["withings_refresh"]
WITHINGS_API_CALL = COMMON_PATCHES["withings_api_call"]
FITBIT_MODULE = COMMON_PATCHES["fitbit_module"]
FITBIT_REFRESH = COMMON_PATCHES["fitbit_refresh"]
FITBIT_ALL = COMMON_PATCHES["fitbit_all"]
FITBIT_CALL_API = COMMON_PATCHES["fitbit_call_api"]
GET_USER_DATA = COMMON_PATCHES["get_user_data"]
MEMBER_PLATFORM_MODEL = COMMON_PATCHES["member_platform_model"]
MEMBER_GET = COMMON_PATCHES["member_get"]

# Create shared test member with consistent ID
test_member = create_test_member()
test_member.id = UUID("6b466245-c321-408f-9a9c-e85ff4a46ba2")

# Create shared test request mock
test_request = MagicMock(spec=Request)


@pytest.fixture
def mock_member_model():
    """Create a mock member model using shared utilities."""
    return create_mock_member_model()


@pytest.fixture
def mock_device_model():
    """Create a mock device model using shared utilities."""
    return create_mock_device_model()


@pytest.fixture
def mock_member_platform_model():
    """Create a mock member platform model using shared utilities."""
    return create_mock_member_platform_model()


@pytest.fixture
def mock_templates():
    """Create a mock templates object for UI tests."""
    templates_mock = MagicMock()
    templates_mock.TemplateResponse = MagicMock()
    return templates_mock


# Common test data for Fitbit device info tests
fitbit_test_devices = [
    {"id": "1", "model": "scale"},
    {"id": "2", "model": "tracker"},
]


class ManagerTestAssertions:
    """Common assertion methods for manager tests."""

    @staticmethod
    def assert_device_health_response(actual_value, email, expected_health):
        """Assert device health response format."""
        assert actual_value == {email: expected_health}

    @staticmethod
    def assert_error_response(actual_value, expected_error_message):
        """Assert error response format."""
        assert actual_value == {
            "success": False,
            "error": expected_error_message,
        }

    @staticmethod
    def assert_success_response(actual_value, expected_message=None):
        """Assert success response format."""
        assert actual_value["success"] is True
        if expected_message:
            assert actual_value["message"] == expected_message


class ManagerTestData:
    """Common test data for manager tests."""

    @staticmethod
    def create_test_user_response():
        """Create test user response data."""
        return [
            {"id": 1, "email": "<EMAIL>"},
            {"id": 2, "email": "<EMAIL>"},
        ]

    @staticmethod
    def create_test_withings_device():
        """Create a test Withings device mock."""
        test_device = MagicMock()
        test_device.id = "withings_id"
        test_device.access_token = "withings_access"
        return test_device

    @staticmethod
    def create_test_fitbit_device():
        """Create a test Fitbit device mock."""
        test_device = MagicMock()
        test_device.id = "fitbit_id"
        test_device.access_token = "fitbit_access"
        return test_device

    @staticmethod
    def create_withings_api_response(status=0, devices=None):
        """Create a test Withings API response."""
        if devices is None:
            devices = [
                {
                    "first_session_date": "a",
                    "last_session_date": "b",
                },
                {
                    "first_session_date": "1748754000",
                    "last_session_date": "1748840400",
                },
            ]

        return {
            "status": status,
            "body": {"devices": devices} if status == 0 else {},
        }

    @staticmethod
    def create_fitbit_error_response(error_type="test", message="message"):
        """Create a test Fitbit error response."""
        return {"error": {"type": error_type, "message": message}}


class ManagerPatchContexts:
    """Common patch context managers for manager tests."""

    @staticmethod
    def mock_get_user_data_success():
        """Context manager for successful get_user_data calls."""
        from unittest.mock import patch

        return patch(GET_USER_DATA, new_callable=AsyncMock, return_value={})

    @staticmethod
    def mock_get_user_data_error():
        """Context manager for failed get_user_data calls."""
        from unittest.mock import patch

        return patch(
            GET_USER_DATA, new_callable=AsyncMock, side_effect=Exception()
        )

    @staticmethod
    def mock_member_found(member):
        """Context manager for found member."""
        from unittest.mock import patch

        return patch(MEMBER_GET, new_callable=AsyncMock, return_value=member)

    @staticmethod
    def mock_member_not_found():
        """Context manager for member not found."""
        from unittest.mock import patch

        return patch(MEMBER_GET, new_callable=AsyncMock, return_value=None)


# Export commonly used items
__all__ = [
    "WITHINGS_MODULE",
    "WITHINGS_REFRESH",
    "WITHINGS_API_CALL",
    "FITBIT_MODULE",
    "FITBIT_REFRESH",
    "FITBIT_ALL",
    "FITBIT_CALL_API",
    "GET_USER_DATA",
    "MEMBER_PLATFORM_MODEL",
    "MEMBER_GET",
    "test_member",
    "test_request",
    "fitbit_test_devices",
    "ManagerTestAssertions",
    "ManagerTestData",
    "ManagerPatchContexts",
]
