"""
Tests for user operations functionality in the manager router.

This module contains tests for:
- ui_sync_user: Sync user data across platforms
- ui_disconnect_user: Disconnect user from devices
"""

from unittest.mock import AsyncMock, patch

import pytest
from ciba_iot_etl.models.pydantic.common import ActivityDevice

from app.routers.manager import ui_sync_user, ui_disconnect_user
from app.routers.requests.api_request import DisconnectRequets
from tests.unit.routers.manager_test_utils_test import (
    MEMBER_PLATFORM_MODEL,
    MEMBER_GET,
    test_member,
    test_request,
)
from tests.unit.test_utils_test import (
    create_test_platforms,
    PatchContextManager,
)


@pytest.mark.asyncio
async def test_ui_sync_user(mock_member_platform_model):
    """
    ui_sync_user should call sync_user with correct arguments
    for every member platform.
    """
    test_platforms = create_test_platforms(["patient", "participant"])
    platform_mock, platform_mock_actions = mock_member_platform_model
    platform_mock_actions["filter"].all = AsyncMock(
        return_value=test_platforms
    )

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(MEMBER_PLATFORM_MODEL, platform_mock),
        patch(
            "app.routers.manager.sync_user", new_callable=AsyncMock
        ) as sync_mock,
    ):
        await ui_sync_user(test_member.email, ActivityDevice.FITBIT)

        assert sync_mock.await_count == 2


@pytest.mark.asyncio
async def test_ui_disconnect_user(mock_member_platform_model):
    """
    ui_disconnect_user should call disconnect_user with correct arguments
    for every member platform.
    """
    test_platforms = create_test_platforms(["patient"])
    test_platforms[0].platform_id = "my_id"  # Override the default ID
    platform_mock, platform_mock_actions = mock_member_platform_model
    platform_mock_actions["filter"].all = AsyncMock(
        return_value=test_platforms
    )

    with (
        PatchContextManager.member_found(test_member, MEMBER_GET),
        patch(MEMBER_PLATFORM_MODEL, platform_mock),
        patch(
            "app.routers.manager.disconnect_user", new_callable=AsyncMock
        ) as disconnect_mock,
    ):
        actual_value = await ui_disconnect_user(
            test_request, test_member.email, ActivityDevice.FITBIT
        )

        assert actual_value == {"disconnected": True}
        disconnect_mock.assert_awaited_once_with(
            DisconnectRequets(
                member_id=str(test_platforms[0].platform_id),
                type_device=ActivityDevice.FITBIT,
                member_type=test_platforms[0].platform_type,
            )
        )
