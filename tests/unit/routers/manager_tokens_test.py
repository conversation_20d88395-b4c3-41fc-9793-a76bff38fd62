"""
Tests for token management functionality in the manager router.

This module contains tests for:
- ui_refresh_user_token: Refresh user tokens for devices
- _refresh_fitbit_token: Internal function for refreshing Fitbit tokens
"""

from unittest.mock import AsyncMock, MagicMock, patch

import pendulum
import pytest
from ciba_iot_etl.models.pydantic.common import ActivityDevice
from fastapi import HTTPException

from app.routers.manager import ui_refresh_user_token, _refresh_fitbit_token
from tests.unit.routers.manager_test_utils_test import (
    WITHINGS_MODULE,
    FITBIT_MODULE,
    WITHINGS_REFRESH,
    FITBIT_REFRESH,
    MEMBER_GET,
    test_member,
    ManagerTestAssertions,
)


@pytest.mark.asyncio
async def test_ui_refresh_user_token_with_member_not_found():
    """
    ui_refresh_user_token should return unsuccessful response
    when the provided user is not found.
    """
    with patch(MEMBER_GET, new_callable=AsyncMock, return_value=None):
        actual_value = await ui_refresh_user_token(
            "another test", ActivityDevice.OURARING
        )

        assert actual_value["success"] is False
        assert actual_value["error"] == "Member not found: another test"


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_device, test_module",
    [
        (ActivityDevice.FITBIT, FITBIT_MODULE),
        (ActivityDevice.WITHINGS, WITHINGS_MODULE),
    ],
)
async def test_ui_refresh_user_token_with_device_not_found(
    test_device, test_module, mock_device_model
):
    """
    ui_refresh_user_token should return unsuccessful response
    when the provided device is not found.
    """
    device_mock, device_mock_actions = mock_device_model
    device_mock_actions["filter"].first = AsyncMock(return_value=None)

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(test_module, device_mock),
    ):
        actual_value = await ui_refresh_user_token(
            test_member.email, test_device
        )

        assert actual_value["success"] is False


@pytest.mark.asyncio
async def test_ui_refresh_user_token_for_withings_with_exception(
    mock_device_model,
):
    """
    ui_refresh_user_token should return unsuccessful response
    when withings refresh raises an exception.
    """
    test_withings = MagicMock()
    test_withings.refresh_token = "<PASSWORD>"
    device_mock, device_mock_actions = mock_device_model
    device_mock_actions["filter"].first = AsyncMock(return_value=test_withings)

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(WITHINGS_MODULE, device_mock),
        patch(
            WITHINGS_REFRESH,
            new_callable=AsyncMock,
            side_effect=Exception(),
        ),
    ):
        actual_value = await ui_refresh_user_token(
            test_member.email, ActivityDevice.WITHINGS
        )

        assert actual_value["success"] is False


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_response",
    [
        {"error": "invalid_grant"},
        {"error": "test_error"},
        {},
    ],
)
async def test_ui_refresh_user_token_for_withings_with_errors(
    test_response, mock_device_model
):
    """
    ui_refresh_user_token should return unsuccessful response
    when withings refresh token fails.
    """
    test_withings = MagicMock()
    test_withings.refresh_token = "<PASSWORD>"
    device_mock, device_mock_actions = mock_device_model
    device_mock_actions["filter"].first = AsyncMock(return_value=test_withings)
    device_mock_actions["filter"].update = AsyncMock()

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(WITHINGS_MODULE, device_mock),
        patch(
            WITHINGS_REFRESH,
            new_callable=AsyncMock,
            return_value=test_response,
        ),
    ):
        actual_value = await ui_refresh_user_token(
            test_member.email, ActivityDevice.WITHINGS
        )

        assert actual_value["success"] is False


@pytest.mark.asyncio
async def test_ui_refresh_user_token_for_withings_success(mock_device_model):
    """
    ui_refresh_user_token should return successful response.
    """
    test_withings = MagicMock()
    test_withings.refresh_token = "withings_123"
    device_mock, device_mock_actions = mock_device_model
    device_mock_actions["filter"].first = AsyncMock(return_value=test_withings)
    device_mock_actions["filter"].update = AsyncMock()
    test_response = {
        "access_token": "withings_abc",
        "refresh_token": "withings_xyz",
        "expires_in": 10800,
    }
    test_updated = MagicMock()
    test_updated.access_token = test_response["access_token"]
    test_updated.refresh_token = test_response["refresh_token"]
    test_updated.expires_in = test_response["expires_in"]
    test_updated.expires_in = pendulum.parse("2025-05-31")

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(WITHINGS_MODULE, device_mock),
        patch(
            WITHINGS_REFRESH,
            new_callable=AsyncMock,
            return_value=test_response,
        ),
        patch(
            "app.routers.manager.Withings.update_tokens",
            new_callable=AsyncMock,
            return_value=test_updated,
        ),
    ):
        actual_value = await ui_refresh_user_token(
            test_member.email, ActivityDevice.WITHINGS
        )

        ManagerTestAssertions.assert_success_response(
            actual_value, "Withings token refreshed successfully"
        )


@pytest.mark.asyncio
async def test_ui_refresh_user_token_for_fitbit_with_exception(
    mock_device_model,
):
    """
    ui_refresh_user_token should return unsuccessful response
    when fitbit refresh raises an exception.
    """
    test_fitbit = MagicMock()
    test_fitbit.refresh_token = "something"
    device_mock, device_mock_actions = mock_device_model
    device_mock_actions["filter"].first = AsyncMock(return_value=test_fitbit)

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(FITBIT_MODULE, device_mock),
        patch(
            FITBIT_REFRESH,
            new_callable=AsyncMock,
            side_effect=Exception(),
        ),
    ):
        actual_value = await ui_refresh_user_token(
            test_member.email, ActivityDevice.FITBIT
        )

        assert actual_value["success"] is False


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_error, test_re_auth",
    [
        ("invalid_grant", True),
        ("api_error", False),
    ],
)
async def test_ui_refresh_user_token_for_fitbit_with_errors(
    test_error, test_re_auth, mock_device_model
):
    """
    ui_refresh_user_token should return unsuccessful response
    when fitbit refresh presents errors.
    """
    test_fitbit = MagicMock()
    test_fitbit.refresh_token = "valid_refresh_token"
    test_fitbit.id = "test_fitbit_id"
    test_fitbit.is_refresh_token_expired.return_value = False
    test_fitbit.refresh_token_expires_at = pendulum.now().add(days=30)
    test_fitbit.old_refresh_token = None

    # Create error result based on test parameters
    from ciba_iot_etl.models.pydantic.token_management import (
        TokenRefreshResult,
        TokenErrorType,
    )

    if test_error == "invalid_grant":
        error_result = TokenRefreshResult(
            success=False,
            error_type=TokenErrorType.INVALID_GRANT,
            error_message="invalid_grant: Refresh token is invalid",
            requires_reauth=True,
            platform="fitbit",
        )
    else:  # api_error
        error_result = TokenRefreshResult(
            success=False,
            error_type=TokenErrorType.API_ERROR,
            error_message="API error occurred",
            requires_reauth=False,
            platform="fitbit",
        )

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(
            "ciba_iot_etl.models.db.fitbit.Fitbit.filter"
        ) as mock_fitbit_filter,
        patch(
            "ciba_iot_etl.extract.common.token_refresh.refresh_fitbit_token_with_fallback",
            new_callable=AsyncMock,
            return_value=error_result,
        ),
        patch(
            "ciba_iot_etl.monitoring.token_metrics.log_token_refresh_metrics"
        ),
    ):
        # Set up the filter chain
        mock_fitbit_filter.return_value.first = AsyncMock(
            return_value=test_fitbit
        )
        mock_fitbit_filter.return_value.update = AsyncMock()

        actual_value = await ui_refresh_user_token(
            test_member.email, ActivityDevice.FITBIT
        )

        assert actual_value["success"] is False
        assert actual_value.get("requires_reauth") == test_re_auth


@pytest.mark.asyncio
async def test_ui_refresh_user_token_for_fitbit_success(mock_device_model):
    """
    ui_refresh_user_token should return a successful response.
    """
    test_fitbit = MagicMock()
    test_fitbit.refresh_token = "valid_refresh_token"
    test_fitbit.id = "test_fitbit_id"
    test_fitbit.is_refresh_token_expired.return_value = False
    test_fitbit.refresh_token_expires_at = pendulum.now().add(days=30)
    test_fitbit.old_refresh_token = None

    _, device_mock_actions = mock_device_model
    device_mock_actions["filter"].first = AsyncMock(return_value=test_fitbit)
    device_mock_actions["filter"].update = AsyncMock()

    # Mock the successful token refresh result
    successful_result = MagicMock()
    successful_result.success = True
    successful_result.error = None
    successful_result.access_token = "new_access_token"
    successful_result.refresh_token = "new_refresh_token"
    successful_result.expires_in = 3600

    # Mock the updated Fitbit object
    updated_fitbit = MagicMock()
    updated_fitbit.access_token_expires_at = pendulum.parse("2025-07-16")

    with (
        patch(MEMBER_GET, new_callable=AsyncMock, return_value=test_member),
        patch(
            "ciba_iot_etl.models.db.fitbit.Fitbit.filter"
        ) as mock_fitbit_filter,
        patch(
            "ciba_iot_etl.extract.common.token_refresh.refresh_fitbit_token_with_fallback",
            new_callable=AsyncMock,
            return_value=successful_result,
        ),
        patch(
            "ciba_iot_etl.monitoring.token_metrics.log_token_refresh_metrics"
        ),
        patch(
            "ciba_iot_etl.models.db.fitbit.Fitbit.update_tokens",
            new_callable=AsyncMock,
            return_value=updated_fitbit,
        ),
    ):
        # Set up the filter chain
        mock_fitbit_filter.return_value.first = AsyncMock(
            return_value=test_fitbit
        )
        mock_fitbit_filter.return_value.update = AsyncMock()
        actual_value = await ui_refresh_user_token(
            test_member.email, ActivityDevice.FITBIT
        )

        ManagerTestAssertions.assert_success_response(
            actual_value, "Fitbit token refreshed successfully"
        )


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_error, expected_code",
    [
        ("invalid_grant", 401),
        ("api_error", 500),
    ],
)
async def test_refresh_fitbit_token_with_errors(
    test_error, expected_code, mock_device_model
):
    """
    _refresh_fitbit_token should raise HTTPError
    when the token refresh fails.
    """
    test_fitbit = MagicMock()
    test_fitbit.id = "test_fitbit_id"
    test_fitbit.is_refresh_token_expired.return_value = False
    test_fitbit.refresh_token_expires_at = pendulum.now().add(days=30)
    test_fitbit.old_refresh_token = None

    # Create error result based on test parameters
    from ciba_iot_etl.models.pydantic.token_management import (
        TokenRefreshResult,
        TokenErrorType,
    )

    if test_error == "invalid_grant":
        error_result = TokenRefreshResult(
            success=False,
            error_type=TokenErrorType.INVALID_GRANT,
            error_message="invalid_grant: Refresh token is invalid",
            requires_reauth=True,
            platform="fitbit",
        )
    else:  # api_error
        error_result = TokenRefreshResult(
            success=False,
            error_type=TokenErrorType.API_ERROR,
            error_message="API error occurred",
            requires_reauth=False,
            platform="fitbit",
        )

    with (
        patch(
            "ciba_iot_etl.extract.common.token_refresh.refresh_fitbit_token_with_fallback",
            new_callable=AsyncMock,
            return_value=error_result,
        ),
        patch(
            "ciba_iot_etl.monitoring.token_metrics.log_token_refresh_metrics"
        ),
        patch(
            "ciba_iot_etl.models.db.fitbit.Fitbit.filter"
        ) as mock_fitbit_filter,
    ):
        # Set up the filter chain for the health update
        mock_fitbit_filter.return_value.update = AsyncMock()

        test_service = MagicMock()  # Create the test service mock

        with pytest.raises(HTTPException) as expected_error:
            await _refresh_fitbit_token(test_fitbit, test_service)

        assert expected_error.value.status_code == expected_code
