"""
Tests for device health monitoring functionality in the manager router.

This module contains tests for:
- get_devices_health: Get devices with unhealthy connections
- ui_get_devices_health: UI endpoint for device health
- check_health: Check device health wrapper
- check_devices_health: Check devices health wrapper
- check_token_health: Check token health for devices
"""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from ciba_iot_etl.models.pydantic.common import ActivityDevice

from app.routers.manager import (
    get_devices_health,
    ui_get_devices_health,
    check_health,
    check_devices_health,
    check_token_health,
)
from tests.unit.routers.manager_test_utils_test import (
    WITHINGS_MODULE,
    FITBIT_MODULE,
    FITBIT_ALL,
    GET_USER_DATA,
    test_member,
    ManagerTestAssertions,
)
from tests.unit.test_utils_test import (
    mock_async_property,
    create_test_device_with_member,
    PatchContextManager,
)


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_device, test_module",
    [
        (ActivityDevice.WITHINGS, WITHINGS_MODULE),
        (ActivityDevice.FITBIT, FITBIT_MODULE),
    ],
)
async def test_get_devices_health(test_device, test_module, mock_device_model):
    """
    get_devices_health should return the information of the devices
    with unhealthy connections.
    """
    test_return_device = MagicMock()
    test_return_device.member = mock_async_property(test_member)
    device_mock, device_mock_actions = mock_device_model
    device_mock_actions["filter"].all = AsyncMock(
        return_value=[test_return_device]
    )

    with patch(test_module, device_mock):
        actual_value = await get_devices_health(test_device)

        assert actual_value == {test_member.email: False}


@pytest.mark.asyncio
async def test_ui_get_devices_health():
    """
    ui_get_devices_health should call get_devices_health with the provided parameters.
    """
    actual_value = await ui_get_devices_health(ActivityDevice.OURARING)

    assert actual_value == {}


@pytest.mark.asyncio
async def test_check_devices_health():
    """
    check_devices_health should call get_devices_health with the provided parameters.
    """
    with patch(
        "app.routers.manager.get_devices_health", new_callable=AsyncMock
    ) as get_devices_health_mock:
        await check_devices_health(ActivityDevice.FITBIT)

        get_devices_health_mock.assert_awaited_once_with(ActivityDevice.FITBIT)


@pytest.mark.asyncio
async def test_check_token_health_with_unsupported_device():
    """
    check_token_health should return empty device info
    when unsupported device is provided.
    """
    actual_value = await check_token_health(ActivityDevice.OURARING)

    assert actual_value == {}


@pytest.mark.asyncio
async def test_check_token_health_for_withings_with_error():
    """
    check_token_health should return withings devices with unhealthy
    connection status due to an error.
    """
    test_device = create_test_device_with_member(member=test_member)

    with (
        patch(
            "app.routers.manager.Withings.all",
            new_callable=AsyncMock,
            return_value=[test_device],
        ),
        PatchContextManager.get_user_data_error(GET_USER_DATA),
    ):
        actual_value = await check_token_health(ActivityDevice.WITHINGS)

        ManagerTestAssertions.assert_device_health_response(
            actual_value, test_member.email, False
        )


@pytest.mark.asyncio
async def test_check_token_health_for_withings():
    """
    check_token_health should return withings devices health status connection.
    """
    test_device = create_test_device_with_member(member=test_member)

    with (
        patch(
            "app.routers.manager.Withings.all",
            new_callable=AsyncMock,
            return_value=[test_device],
        ),
        PatchContextManager.get_user_data_success(
            GET_USER_DATA
        ) as get_data_mock,
    ):
        actual_value = await check_token_health(ActivityDevice.WITHINGS)

        ManagerTestAssertions.assert_device_health_response(
            actual_value, test_member.email, True
        )
        get_data_mock.assert_awaited_once()


@pytest.mark.asyncio
async def test_check_token_health_for_fitbit_with_error():
    """
    check_token_health should return fitbit devices
    with unhealthy connection status due to an error.
    """
    test_device = MagicMock()
    test_device.member = mock_async_property(test_member)
    test_device.is_access_token_expired = MagicMock(return_value=False)

    with (
        patch(FITBIT_ALL, new_callable=AsyncMock, return_value=[test_device]),
        patch(GET_USER_DATA, new_callable=AsyncMock, side_effect=Exception()),
    ):
        actual_value = await check_token_health(ActivityDevice.FITBIT)

        assert actual_value == {test_member.email: False}


@pytest.mark.asyncio
async def test_check_token_health_for_fitbit_with_refreshing_error(
    mock_device_model,
):
    """
    check_token_health should return fitbit unhealthy connections
    when refreshing token is not successful.
    """
    test_device = MagicMock()
    test_device.id = "Test Id"
    test_device.refresh_token = "Test refresh"
    test_device.member = mock_async_property(test_member)
    fitbit_mock, fitbit_mock_actions = mock_device_model
    fitbit_mock_actions["filter"].update = AsyncMock()
    test_device.is_access_token_expired = MagicMock(return_value=True)
    test_refresh_response = MagicMock()
    test_refresh_response.error = "invalid refresh token"

    with (
        patch(FITBIT_MODULE, fitbit_mock),
        patch(FITBIT_ALL, new_callable=AsyncMock, return_value=[test_device]),
        patch(
            "app.routers.manager.FitbitLoader.refresh_token",
            new_callable=AsyncMock,
            return_value=test_refresh_response,
        ),
    ):
        actual_value = await check_token_health(ActivityDevice.FITBIT)

        assert actual_value == {test_member.email: False}


@pytest.mark.asyncio
async def test_check_token_health_for_fitbit_with_updating_error(
    mock_device_model,
):
    """
    check_token_health should return fitbit unhealthy connections
    when updating token fails.
    """
    test_device = MagicMock()
    test_device.id = "Test Id"
    test_device.refresh_token = "Test refresh"
    test_device.member = mock_async_property(test_member)
    test_device.is_access_token_expired = MagicMock(return_value=True)
    fitbit_mock, fitbit_mock_actions = mock_device_model
    fitbit_mock_actions["filter"].update = AsyncMock()
    test_refresh_response = MagicMock()
    test_refresh_response.error = None
    test_refresh_response.access_token = "New access token"
    test_refresh_response.refresh_token = "New refresh token"
    test_refresh_response.expires_in = 7200

    with (
        patch(FITBIT_MODULE, fitbit_mock),
        patch(FITBIT_ALL, new_callable=AsyncMock, return_value=[test_device]),
        patch(
            "app.routers.manager.Fitbit.update_tokens",
            new_callable=AsyncMock,
            side_effect=Exception(),
        ),
        patch(
            "app.routers.manager.FitbitLoader.refresh_token",
            new_callable=AsyncMock,
            return_value=test_refresh_response,
        ),
    ):
        actual_value = await check_token_health(ActivityDevice.FITBIT)

        assert actual_value == {test_member.email: False}


@pytest.mark.asyncio
async def test_check_token_health_for_fitbit():
    """
    check_token_health should return fitbit devices connection status.
    """
    test_device = MagicMock()
    test_device.member = mock_async_property(test_member)
    test_device.is_access_token_expired = MagicMock(return_value=False)

    with (
        patch(FITBIT_ALL, new_callable=AsyncMock, return_value=[test_device]),
        patch(GET_USER_DATA, new_callable=AsyncMock) as get_data_mock,
    ):
        actual_value = await check_token_health(ActivityDevice.FITBIT)

        assert actual_value == {test_member.email: True}
        get_data_mock.assert_awaited_once()


@pytest.mark.asyncio
async def test_check_health():
    """
    check_health should call check_token_health with the provided activity device.
    """
    with patch(
        "app.routers.manager.check_token_health", new_callable=AsyncMock
    ) as token_health_mock:
        await check_health(ActivityDevice.FITBIT)

        token_health_mock.assert_awaited_once_with(ActivityDevice.FITBIT)
