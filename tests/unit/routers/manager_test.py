"""
Manager Router Tests - Index File

This file previously contained all tests for the manager router functionality.
The tests have been reorganized into separate files to reduce code duplication
and improve maintainability.

Test files organization:
- manager_users_test.py: Tests for user management (get_users, get_user, ui_users, ui_user_detail)
- manager_user_data_test.py: Tests for user data retrieval (get_user_data, get_user_data_api, ui_get_user_data)
- manager_health_test.py: Tests for device health monitoring (get_devices_health, check_token_health, etc.)
- manager_device_info_test.py: Tests for device information (ui_get_device_info)
- manager_tokens_test.py: Tests for token management (ui_refresh_user_token, _refresh_fitbit_token)
- manager_operations_test.py: Tests for user operations (ui_sync_user, ui_disconnect_user)

Shared utilities:
- manager_test_utils.py: Common fixtures, mocks, and utilities for all manager tests

To run all manager tests:
    uv run pytest tests/unit/routers/manager_*_test.py

To run a specific test category:
    uv run pytest tests/unit/routers/manager_users_test.py
    uv run pytest tests/unit/routers/manager_health_test.py
    etc.
"""

# This file is now empty of tests - all tests have been moved to separate files
# If you need to add new manager tests, please add them to the appropriate
# category file or create a new one following the naming convention
