from unittest import TestCase

from ciba_iot_etl.helpers.crypto import generate_hmac_sha256


class CryptoHelperTesting(TestCase):
    secret_key: str = "secret"
    secret_message: str = "my_secret_str"
    expected_hash: str = (
        "edcbfd9acbbc47c50a97b397bb93f38de99408df4946db4dd23dc3b61d2275bf"
    )

    def test_generate_hmac_sha256(self) -> None:
        """
        Test generating hmac sha256 hash signature
        """
        signature = generate_hmac_sha256(self.secret_message, self.secret_key)
        assert signature == self.expected_hash
