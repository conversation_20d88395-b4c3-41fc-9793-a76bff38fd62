import sys

import httpx
from ciba_iot_etl.models.pydantic.common import ActivityDevice, PlatformType
from ciba_iot_etl.models.db.base import DeviceType
from ciba_iot_etl.models.db.weight import Weight
from ciba_iot_etl.models.db.heart_rate import HeartRate
from ciba_iot_etl.models.db.blood_pressure import BloodPressure

BASE_URL = "https://rpm-single-dev.cibahealth.com/"


def get_code_existing_device(
    member_id: str,
    email: str,
    device_type: ActivityDevice = ActivityDevice.WITHINGS,
    platform_type: PlatformType = PlatformType.patient,
):
    url = f"{BASE_URL}/devices/get_code"
    params = {
        "type_device": device_type.value,
        "mail": email,
        "member_type": platform_type.value,
        "member_id": member_id,
    }
    headers = {"X-Auth-Key": "1"}
    response = httpx.get(url, params=params, headers=headers)
    assert response.status_code == 200, response.text
    response_data = response.json()
    if "auth_url" in response_data:
        return response_data["auth_url"]

    if "healthy" in response_data:
        return response_data


def sync_device(
    member_id: str,
    device_type: ActivityDevice = ActivityDevice.WITHINGS,
    platform_type: PlatformType = PlatformType.patient,
):
    url = f"{BASE_URL}/devices/sync"
    payload = {
        "type_device": device_type.value,
        "member_type": platform_type.value,
        "member_id": member_id,
        "start_date": **********,
    }
    headers = {"X-Auth-Key": "1"}

    response = httpx.post(url, json=payload, headers=headers)
    assert response.status_code == 200, response.text
    response_data = response.json()
    print(response_data)


def disconnect_device(
    member_id: str,
    device_type: ActivityDevice = ActivityDevice.WITHINGS,
    platform_type: PlatformType = PlatformType.patient,
):
    url = f"{BASE_URL}/devices/disconnect"
    payload = {
        "type_device": device_type.value,
        "member_type": platform_type.value,
        "member_id": member_id,
    }
    headers = {"X-Auth-Key": "1"}

    response = httpx.post(url, json=payload, headers=headers)
    assert response.status_code == 200, response.text
    response_data = response.json()
    print(response_data)


async def insert_data(
    member_id: str,
    device_type: ActivityDevice = ActivityDevice.WITHINGS,
):
    db_device = None
    if device_type == ActivityDevice.WITHINGS:
        db_device = DeviceType.WITHINGS
    elif device_type == ActivityDevice.FITBIT:
        db_device = DeviceType.FITBIT

    await Weight(
        member_id=member_id,
        device=db_device,
        weight=70.0,
        date=**********,
    ).save()
    await HeartRate(
        member_id=member_id,
        device=device_type.value,
        heart_rate=70,
        date=**********,
    ).save()
    await BloodPressure(
        member_id=member_id,
        device=device_type.value,
        systolic=120,
        diastolic=80,
        date=**********,
    ).save()


if __name__ == "__main__":
    BASE_URL = "https://rpm-single.cibahealth.com"

    member_id = "58E15FE3-C575-41DD-96FF-7B543538FA17"
    email = "<EMAIL>"

    auth_url = get_code_existing_device(
        member_id=member_id,
        email=email,
    )
    print(auth_url)
    continue_decision = input("Do you want to continue? (y/n): ")
    if continue_decision.lower() == "n":
        sys.exit(0)

    sync_device(
        member_id=member_id,
    )

    continue_decision = input("Do you want to continue? (y/n): ")
    if continue_decision.lower() == "n":
        sys.exit(0)

    disconnect_device(
        member_id=member_id,
    )
