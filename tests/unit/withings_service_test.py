import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from app.services.withings import MemberWithings, WithingsServiceException
from tests.unit.test_utils_test import (
    DummyQuery,
    create_mock_withings_device,
    create_mock_platform,
    COMMON_PATCHES,
)


@pytest.fixture
def member_withings(mock_withings_loader, mock_member_enhanced):
    """Fixture to create a MemberWithings instance with mocked dependencies."""
    return MemberWithings(
        client=mock_withings_loader, member=mock_member_enhanced
    )


@pytest.mark.asyncio
async def test_handle_subscribe_to_account_failure(
    member_withings, mock_withings_loader
):
    """
    Test that handle_subscribe_to_account returns an error message on failure.

    This test simulates an exception during token retrieval.
    It ensures that the method returns a response containing an error message.
    """
    mock_withings_loader.get_token.side_effect = Exception("Error")
    response = await member_withings.handle_subscribe_to_account(
        state="state", code="code", email="email", site="site"
    )
    assert "error" in response


@pytest.mark.asyncio
async def test_subcribe_to_user_devices_existing(
    member_withings, mock_member_enhanced
):
    """
    Test that subscribe_to_user_devices returns an existing Withings ID.

    This test mocks the Member object to simulate an existing Withings ID.
    It ensures that the method returns the existing ID without making further API calls.
    """
    withings = create_mock_withings_device(device_id="existing_id")
    mock_member_enhanced.get_withings_id = AsyncMock(return_value=withings.id)
    dummy_query = DummyQuery(withings)
    with patch(COMMON_PATCHES["withings_filter"]) as mocked_device:
        mocked_device.return_value = dummy_query
        withings_id = await member_withings.subcribe_to_user_devices(
            state="state", code="code", email="email", site="site"
        )
        assert withings_id == "existing_id"


@pytest.mark.asyncio
async def test_subcribe_to_notification_success(
    member_withings, mock_withings_loader
):
    """
    Test that subcribe_to_notification returns True on success.

    This test mocks the WithingsLoader to simulate successful subscription creation.
    It ensures that the method returns True when all subscriptions are successful.
    """
    mock_withings_loader.create_subscription.return_value = {"status": 0}
    withings = create_mock_withings_device(device_id="mock_withings_id")
    with patch(
        COMMON_PATCHES["withings_get_or_none"],
        new_callable=AsyncMock,
        return_value=withings,
    ):
        result = await member_withings.subcribe_to_notification(
            withings_id="mock_withings_id"
        )
        assert result


@pytest.mark.asyncio
async def test_subcribe_to_notification_failure(
    member_withings, mock_withings_loader
):
    """
    Test that subcribe_to_notification returns False on failure.

    This test mocks the WithingsLoader to simulate a failed subscription creation.
    It ensures that the method returns False when any subscription fails.
    """
    mock_withings_loader.create_subscription.return_value = {
        "status": 1,
        "error": "error",
    }
    withings = create_mock_withings_device(device_id="mock_withings_id")
    with patch(
        COMMON_PATCHES["withings_get_or_none"],
        new_callable=AsyncMock,
        return_value=withings,
    ):
        result = await member_withings.subcribe_to_notification(
            withings_id="mock_withings_id"
        )
        assert not result


@pytest.mark.asyncio
async def test_handle_notification_process(
    member_withings, mock_member_enhanced
):
    """
    Test that handle_notification processes notifications for supported applications.

    This test mocks the Member object to simulate platform retrieval.
    It ensures that the method processes notifications for supported application types.
    """
    mock_member_enhanced.get_platforms.return_value = [create_mock_platform()]
    mock_member_enhanced.get_withings_id.return_value = "withings_id"
    with pytest.raises(WithingsServiceException):
        await member_withings.handle_notification(
            userid=1,
            appli=1,
            startdate=0,
            enddate=0,
            correlation_id="correlation_id_test",
        )


@pytest.mark.asyncio
async def test_send_pull_notification_success(
    member_withings, mock_member_enhanced, mock_queue
):
    """
    Test that send_pull_notification returns True on success.

    This test mocks the queue service to simulate successful message sending.
    It ensures that the method returns True when the notification is sent successfully.
    """
    mock_member_enhanced.get_withings_id.return_value = "withings_id"
    with patch(COMMON_PATCHES["queue_get_queue"]) as mock_get_queue:
        mock_get_queue.return_value = mock_queue
        with pytest.raises(WithingsServiceException):
            await member_withings.send_pull_notification(
                startdate=0,
                enddate=0,
                platforms=[],
                correlation_id="correlation_id_test",
            )


@pytest.mark.asyncio
async def test_send_pull_notification_failure(
    member_withings, mock_member_enhanced
):
    """
    Test that send_pull_notification raises WithingsServiceException when Withings ID is missing.

    This test ensures that the method raises an exception when the Withings ID is not found.
    """
    mock_member_enhanced.get_withings_id.return_value = None
    with pytest.raises(WithingsServiceException):
        await member_withings.send_pull_notification(
            startdate=0,
            enddate=0,
            platforms=[],
            correlation_id="correlation_id_test",
        )


@pytest.mark.asyncio
async def test_sync_device(member_withings, mock_member_enhanced, mock_queue):
    """
    Test that sync_device sends a pull notification.

    This test mocks the queue service to simulate successful message sending.
    It ensures that the method sends a pull notification to sync the device.
    """
    mock_member_enhanced.get_platforms.return_value = [create_mock_platform()]
    mock_member_enhanced.get_withings_id.return_value = "withings_id"
    with patch(COMMON_PATCHES["queue_get_queue"]) as mock_get_queue:
        mock_get_queue.return_value = mock_queue
        with pytest.raises(WithingsServiceException):
            await member_withings.sync_device(
                start_date=0, correlation_id="correlation_id_test"
            )


@pytest.mark.asyncio
async def test_disconnect_device_success(
    member_withings, mock_withings_loader, mock_member_enhanced
):
    """
    Test that disconnect_device returns True on successful disconnection.

    This test mocks the WithingsLoader to simulate successful subscription deletion.
    It ensures that the method returns True when the device is successfully disconnected.
    """
    mock_withings_loader.delete_subscription.return_value = {"status": 0}
    mock_member_enhanced.get_withings_id.return_value = "withings_id"
    with patch(
        COMMON_PATCHES["withings_delete"],
        new_callable=AsyncMock,
    ):
        result = await member_withings.disconnect_device()
        assert result, result


@pytest.mark.asyncio
async def test_disconnect_device_failure(
    member_withings, mock_withings_loader, mock_member_enhanced
):
    """
    Test that disconnect_device handles errors during disconnection.

    This test mocks the WithingsLoader to simulate a failed subscription deletion.
    It ensures that the method handles errors gracefully and still returns True.
    """
    mock_withings_loader.delete_subscription.return_value = {
        "status": 1,
        "error": "error",
    }
    mock_member_enhanced.get_withings_id.return_value = "withings_id"
    with patch(
        COMMON_PATCHES["withings_delete"],
        new_callable=AsyncMock,
    ):
        result = await member_withings.disconnect_device()
        assert result, result


@pytest.mark.asyncio
async def test_renew_credential_success(
    member_withings, mock_withings_loader, mock_member_enhanced
):
    """
    Test that renew_credential updates tokens successfully.

    This test mocks the WithingsLoader to simulate successful token refresh.
    It ensures that the method updates the member's credentials with new tokens.
    """
    mock_member_enhanced.get_withings_refresh_token.return_value = (
        "refresh_token"
    )
    mock_member_enhanced.get_withings_id.return_value = "test_withings_id"
    mock_withings_loader.refresh_token.return_value = {
        "body": {
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
        }
    }

    # Mock the new token refresh logic
    from ciba_iot_etl.models.pydantic.token_management import (
        TokenRefreshResult,
    )

    successful_result = TokenRefreshResult(
        success=True,
        token_data={
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
            "expires_in": 3600,
        },
        platform="withings",
    )

    with (
        patch(
            "ciba_iot_etl.extract.common.token_refresh.refresh_withings_token_with_fallback",
            new_callable=AsyncMock,
            return_value=successful_result,
        ),
        patch(
            "ciba_iot_etl.monitoring.token_metrics.log_token_refresh_metrics"
        ),
        patch(
            COMMON_PATCHES["withings_get"],
            new_callable=AsyncMock,
            return_value=MagicMock(),
        ),
        patch(
            COMMON_PATCHES["withings_update_tokens"],
            new_callable=AsyncMock,
            return_value=MagicMock(),
        ),
    ):
        await member_withings.renew_credential()


@pytest.mark.asyncio
async def test_renew_credential_failure(
    member_withings, mock_withings_loader, mock_member_enhanced
):
    """
    Test that renew_credential raises WithingsServiceException when refresh token is missing.

    This test ensures that the method raises an exception when the refresh token is not found.
    """
    mock_member_enhanced.get_withings_refresh_token.return_value = None
    with pytest.raises(WithingsServiceException):
        await member_withings.renew_credential()
