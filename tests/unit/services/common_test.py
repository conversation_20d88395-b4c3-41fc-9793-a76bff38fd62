from unittest.mock import MagicMock, patch, AsyncMock
from urllib.parse import quote

import pytest
from ciba_iot_etl.models.pydantic.common import ActivityDevice

from app.services.common import (
    append_error_to_url,
    subscribe_to_account_helper,
)
from tests.unit.common import TEST_URL, TEST_EMAIL


@pytest.mark.parametrize(
    "test_url, test_error",
    [
        ("https://sample.com", None),
        ("https://sample.com?connectionError=test", "test"),
    ],
)
def test_append_error_to_url_without_changes(test_url, test_error):
    """
    append_error_to_url should return the provided url without changes.
    """
    actual_value = append_error_to_url(test_url, test_error)

    assert actual_value == test_url


@pytest.mark.parametrize(
    "test_url, test_error, expected_value",
    [
        (
            "https://sample.com",
            "invalid_token",
            "https://sample.com?connectionError=invalid_token",
        ),
        (
            "https://sample.com?id=1",
            "invalid_state",
            "https://sample.com?id=1&connectionError=invalid_state",
        ),
    ],
)
def test_append_error_to_url_with_changes(
    test_url, test_error, expected_value
):
    """
    append_error_to_url should return the provided url with connectionError query param.
    """
    actual_value = append_error_to_url(test_url, test_error)

    assert actual_value == expected_value


@pytest.mark.asyncio
async def test_subscribe_to_account_helper():
    """
    subscribe_to_account_helper should return a redirection
    with connectionError when a processing error occurred.
    """
    test_member = MagicMock()
    test_member.email = TEST_EMAIL
    expected_error = "Failed to handle subscription"

    with (
        patch(
            "app.services.common.MemberState.get_member_by_state",
            new_callable=AsyncMock,
            return_value=test_member,
        ),
        patch(
            "app.services.common.Member.get_platforms",
            new_callable=AsyncMock,
            return_value=[],
        ),
        patch(
            "app.services.common.process_member_state",
            new_callable=AsyncMock,
            return_value=(TEST_URL, 0),
        ),
    ):
        member_device_handler = MagicMock()
        member_device_handler.side_effect = Exception("Mocked error")
        actual_value = await subscribe_to_account_helper(
            client=MagicMock(),
            member_device_handler=member_device_handler,
            email="",
            code="ABC123",
            state="*********",
            site=TEST_URL,
            error="",
            activity_device=ActivityDevice.FITBIT,
        )

        assert (
            actual_value.headers.get("Location")
            == f"{TEST_URL}?connectionError={quote(expected_error)}"
        )
