from unittest.mock import MagicMock

import pytest

from app.services.utils.devices import check_health


@pytest.fixture(params=[True, None])
def mock_device(request):
    healthy_flag = request.param
    test_device = MagicMock()

    if healthy_flag is None:
        delattr(test_device, "healthy")
    else:
        test_device.healthy = healthy_flag

    return test_device


def test_check_health_with_positive_response(mock_device):
    """
    check_health should return true
    when the device is healthy or valid.
    """
    actual_value = check_health(mock_device)

    assert actual_value is True


def test_check_health_with_negative_response():
    """
    check_health should return false
    when the device is unhealthy.
    """
    test_device = MagicMock()
    test_device.healthy = False

    actual_value = check_health(test_device)

    assert actual_value is False
