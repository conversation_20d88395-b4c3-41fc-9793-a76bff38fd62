from datetime import date, datetime, timedelta
from uuid import uuid4
from unittest.mock import patch, MagicMock, AsyncMock

import pytest
from ciba_iot_etl.models.db.sleep import Sleep
from pydantic_core import ValidationError

from app.common.exceptions import MeasuresException
from app.services.measures.sleep import SleepService
from app.pydantic_model.measures_api import SleepScoreTable
from tests.unit.helpers.sleep_test import test_day_measure

TEST_ERROR_MESSAGE = "test"
test_start_date = date(2020, 1, 5)
test_end_date = date(2020, 1, 15)
test_validation_error = ValidationError(TEST_ERROR_MESSAGE, [])


@pytest.mark.asyncio
async def test_get_dashboard_summary(mocker):
    """
    get_dashboard_summary should raise a MeasuresException
    when a ValueError is raised.
    """
    mock_query = MagicMock()
    mocker.patch(
        "app.services.measures.sleep.get_base_query", return_value=mock_query
    )
    with patch(
        "app.services.measures.sleep.SleepService.get_measures"
    ) as mock_get_measures:
        mock_get_measures.side_effect = test_validation_error

        with pytest.raises(MeasuresException) as actual_exception:
            await SleepService.get_dashboard_summary(
                uuid4(), test_start_date, test_end_date
            )

        assert actual_exception.type is MeasuresException


@pytest.mark.asyncio
async def test_get_measures_data(mocker):
    """
    get_measures_data should raise a MeasuresException
    when a ValueError is raised.
    """
    mock_query = MagicMock()
    mocker.patch(
        "app.services.measures.sleep.get_base_query", return_value=mock_query
    )
    with patch(
        "app.services.measures.sleep.SleepService.get_measures"
    ) as mock_get_measures:
        mock_get_measures.side_effect = test_validation_error

        with pytest.raises(MeasuresException) as actual_exception:
            await SleepService.get_measures_data(
                uuid4(), test_start_date, test_end_date
            )

        assert actual_exception.type is MeasuresException


@pytest.mark.asyncio
async def test_get_measures(mocker):
    """
    get_measures should return the measures and summary in the provided date range.
    """
    # Create a real SleepScoreTable instance instead of a MagicMock
    real_sleep_score = SleepScoreTable(
        duration_max=50,
        quality_max=25,
        restoration_max=25,
        sleep_max=100,
        duration_score=40,
        quality_score=20,
        restoration_score=20,
        sleep_score=80,
    )

    # Patch the calculate_sleep_score function to return a real SleepScoreTable
    mocker.patch(
        "app.helpers.sleep.calculate_sleep_score",
        return_value=real_sleep_score,
    )

    test_today = datetime(2024, 1, 21)
    test_measure = Sleep(
        created_at=test_today,
        updated_at=test_today,
        start_time=test_today,
        end_time=test_today + timedelta(hours=8),
        duration=test_day_measure.duration,
        bed_time=test_day_measure.bed_time,
        asleep_time=test_day_measure.asleep_time,
        deep_time=test_day_measure.deep_time,
        deep_count=1,
        light_time=test_day_measure.light_time,
        light_count=2,
        rem_time=test_day_measure.rem_time,
        rem_count=3,
        wake_time=test_day_measure.wake_time,
        wake_count=4,
        efficiency=test_day_measure.efficiency,
    )
    mock_base_query = MagicMock()
    mock_base_query.order_by = AsyncMock(return_value=[test_measure])

    with mocker.patch("pendulum.now", return_value=test_today):
        actual_measures, actual_today = await SleepService.get_measures(
            mock_base_query,
            ["2024-01-19", "2024-01-20", "2024-01-21"],
        )

    assert len(actual_measures) == 3
    assert actual_measures[-1].duration == test_measure.duration
    assert actual_measures[-1].asleep_time == test_measure.asleep_time
    assert actual_measures[-1].start_time_in_bed == test_measure.start_time
    assert actual_measures[-1].end_time_in_bed == test_measure.end_time
    assert actual_today.date == test_measure.end_time.date()
    assert actual_today.duration == test_measure.duration
    assert actual_today.asleep_time == test_measure.asleep_time
