from unittest.mock import AsyncMock, MagicMock, patch
from uuid import UUID

import pytest
from ciba_iot_etl.models.pydantic.common import ActivityDevice
from ciba_iot_etl.models.db.transtek import Transtek

from app.common.messages import (
    DEVICE_VENDOR_NOT_SUPPORTED,
    DEVICE_CONNECTION_NOT_FOUND,
)
from app.services.data_fetch.factory import (
    get_connection_model,
    get_data_fetch_class,
    DataFetcherFactory,
)
from app.services.data_fetch.withings import WithingsDataFetcher
from app.services.data_fetch.transtek import TranstekDataFetcher

test_member_id = UUID("79c6c2af-86a4-4799-a14f-5ead3474e5aa")


@pytest.fixture
def mock_withings_model():
    withings_mock = MagicMock()
    filter_mock = MagicMock()
    withings_mock.filter.return_value = filter_mock

    return withings_mock, filter_mock


@pytest.fixture
def mock_transtek_model():
    transtek_mock = MagicMock(spec=Transtek)
    filter_mock = AsyncMock()
    transtek_mock.filter = filter_mock

    return transtek_mock, filter_mock


def test_get_connection_model_with_error():
    """
    get_connection_model should raise a ValueError
    when the provided device vendor is not supported.
    """
    with pytest.raises(ValueError) as expected_error:
        get_connection_model(ActivityDevice.FITBIT)

    assert expected_error.value.args[0] == DEVICE_VENDOR_NOT_SUPPORTED


def test_get_data_fetch_class_with_error():
    """
    get_data_fetch_class should raise a ValueError
    when the provided device vendor is not supported.
    """
    with pytest.raises(ValueError) as expected_error:
        get_data_fetch_class(ActivityDevice.DEXCOM)

    assert expected_error.value.args[0] == DEVICE_VENDOR_NOT_SUPPORTED


@pytest.mark.asyncio
async def test_factory_instantiation_for_withings_with_error(
    mock_withings_model,
):
    """
    DataFetcherFactory should raise a RuntimeError
    when the connection for the provided vendor is not found.
    """
    withings_mock, filter_mock = mock_withings_model
    filter_mock.first = AsyncMock(return_value=None)

    with patch("app.services.data_fetch.factory.Withings", withings_mock):
        with pytest.raises(RuntimeError) as expected_error:
            await DataFetcherFactory.create(
                test_member_id, ActivityDevice.WITHINGS
            )

        assert expected_error.value.args[0] == DEVICE_CONNECTION_NOT_FOUND


@pytest.mark.asyncio
async def test_factory_instantiation_for_withings_success(mock_withings_model):
    """
    DataFetcherFactory should instantiate a WithingsDataFetcher.
    """
    test_connection = MagicMock()
    withings_mock, filter_mock = mock_withings_model
    filter_mock.first = AsyncMock(return_value=test_connection)

    with patch("app.services.data_fetch.factory.Withings", withings_mock):
        actual_value = await DataFetcherFactory.create(
            test_member_id, ActivityDevice.WITHINGS
        )

        assert isinstance(actual_value, WithingsDataFetcher)
        assert hasattr(actual_value, "get_devices")
        assert hasattr(actual_value, "get_measures")


@pytest.mark.asyncio
async def test_factory_instantiation_for_transtek_with_error(
    mock_transtek_model,
):
    """
    DataFetcherFactory should raise a RuntimeError
    when the connection for the provided vendor is not found.
    """
    transtek_model, filter_mock = mock_transtek_model
    filter_mock.return_value = None

    with patch("app.services.data_fetch.factory.Transtek", transtek_model):
        with pytest.raises(RuntimeError) as expected_error:
            await DataFetcherFactory.create(
                test_member_id, ActivityDevice.TRANSTEK
            )

        assert expected_error.value.args[0] == DEVICE_CONNECTION_NOT_FOUND


@pytest.mark.asyncio
async def test_factory_instantiation_for_transtek_success(mock_transtek_model):
    """
    DataFetcherFactory should instantiate a WithingsDataFetcher.
    """
    test_connection = MagicMock()
    transtek_mock, filter_mock = mock_transtek_model
    filter_mock.return_value = test_connection

    with patch("app.services.data_fetch.factory.Transtek", transtek_mock):
        actual_value = await DataFetcherFactory.create(
            test_member_id, ActivityDevice.TRANSTEK
        )

        assert isinstance(actual_value, TranstekDataFetcher)
        assert hasattr(actual_value, "get_devices")
        assert hasattr(actual_value, "get_measures")
