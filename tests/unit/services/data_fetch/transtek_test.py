from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
import pytest

from ciba_iot_etl.models.db.transtek import Transtek, TranstekDeviceType
from app.pydantic_model.measures_api import Measure
from app.services.data_fetch.transtek import TranstekDataFetcher
from ciba_iot_etl.models.db.weight import Weight
from ciba_iot_etl.models.db.blood_pressure import BloodPressure


@pytest.fixture
def mock_scale():
    mock_device = MagicMock(spec=Transtek)
    mock_device.device_id = "123"
    mock_device.device_type = TranstekDeviceType.SCALE
    mock_device.updated_at = "123"

    return mock_device


@pytest.fixture
def mock_bpm():
    mock_device = MagicMock(spec=Transtek)
    mock_device.device_id = "123"
    mock_device.device_type = TranstekDeviceType.BPM
    mock_device.updated_at = "123"

    return mock_device


@pytest.mark.asyncio
async def test_get_devices(mock_scale):
    fetcher = TranstekDataFetcher(
        member_id=uuid4(), transtek_devices=[mock_scale]
    )
    device_list = await fetcher.get_devices()

    assert len(device_list) == 1


@pytest.mark.asyncio
async def test_get_measures_scale(mock_scale):
    fetcher = TranstekDataFetcher(
        member_id=uuid4(), transtek_devices=[mock_scale]
    )

    with patch(
        "app.services.data_fetch.transtek.WeightService"
    ) as service_mock:
        with patch(
            "app.services.data_fetch.transtek.get_weight_base_query"
        ) as base_mock:
            with patch(
                "app.services.data_fetch.transtek.map_transtek_weight"
            ) as map_mock:
                mock_weight = MagicMock(spec=Weight)
                service_mock.get_measures = AsyncMock(
                    return_value=[mock_weight]
                )
                base_mock.return_value = ...
                map_mock.return_value = MagicMock(spec=Measure)
                measures = await fetcher.get_measures(start_date=10000)

    assert len(measures) == 1


@pytest.mark.asyncio
async def test_get_measures_bpm(mock_bpm):
    fetcher = TranstekDataFetcher(
        member_id=uuid4(), transtek_devices=[mock_bpm]
    )

    with patch(
        "app.services.data_fetch.transtek.BloodPressureService"
    ) as service_mock:
        with patch(
            "app.services.data_fetch.transtek.get_blood_base_query"
        ) as base_mock:
            with patch(
                "app.services.data_fetch.transtek.map_transtek_blood_pressure"
            ) as map_mock:
                mock_pressure = MagicMock(spec=BloodPressure)
                service_mock.get_measures = AsyncMock(
                    return_value=[mock_pressure]
                )
                base_mock.return_value = ...
                map_mock.return_value = [
                    MagicMock(spec=Measure),
                    MagicMock(spec=Measure),
                ]
                measures = await fetcher.get_measures(start_date=10000)

    assert len(measures) == 2
