from datetime import datetime
from decimal import Decimal

from app.services.measures.weight import WeightService

test_measures = []
test_date = datetime.now()


def test_get_summary_1():
    """
    get_summary should return a positive weight change value
    when the measures list represents an increase in weight
    """
    incremental_measures = [
        {"value": Decimal("70.5"), "created_at": test_date},
        {"value": Decimal("80.5"), "created_at": test_date},
    ]

    actual_value = WeightService.get_summary(incremental_measures)

    assert actual_value.weightChange == Decimal("10.0")
    assert actual_value.averageWeightChange == Decimal(5)
    assert actual_value.lastUpdate == test_date
    assert actual_value.currentWeight == incremental_measures[-1]["value"]


def test_get_summary_2():
    """
    get_summary should return a negative weight change value
    when the measures list represents a decrease in weight
    """
    decreasing_measures = [
        {"value": Decimal("60.25"), "created_at": test_date},
        {"value": Decimal("50.75"), "created_at": test_date},
    ]

    actual_value = WeightService.get_summary(decreasing_measures)

    assert actual_value.weightChange == Decimal("-9.5")
    assert actual_value.averageWeightChange == Decimal("-4.75")
    assert actual_value.lastUpdate == test_date
    assert actual_value.currentWeight == decreasing_measures[-1]["value"]
