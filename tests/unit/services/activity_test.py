from datetime import datetime
from unittest.mock import MagicMock, AsyncMock

import pytest
from ciba_iot_etl.models.db.activity import ActivityCategory
from ciba_iot_etl.helpers.measurement import UnitOfMeasurement

from app.pydantic_model.measures_api import (
    ActivityDay,
    ActivitySummary,
    DistanceUnit,
    MeasuresResponse,
    MeasureType,
    TodaysSummary,
)
from app.services.measures.activity import ActivityService
from tests.unit.common import TEST_MEMBER_ID

TOTAL_DAYS = 7
QUERY_MODULE = "app.services.measures.activity.get_base_query"
test_end = datetime(2025, 6, 5)
test_start = datetime(2025, 6, 3)


@pytest.mark.asyncio
async def test_get_dashboard_summary_1(mocker):
    """
    get_dashboard_summary should return None when no measures are available.
    """
    mock_base_query = MagicMock()
    mock_base_query.order_by.return_value.values = AsyncMock(return_value=[])
    mocker.patch(QUERY_MODULE, return_value=mock_base_query)
    actual_value = await ActivityService.get_dashboard_summary(
        TEST_MEMBER_ID, test_start, test_end, DistanceUnit.KM
    )

    assert actual_value is None


@pytest.mark.asyncio
async def test_get_dashboard_summary_2(mocker):
    """
    get_dashboard_summary should return the dashboard summary for activity measures.
    """
    mock_base_query = MagicMock()
    mock_base_query.order_by.return_value.values = AsyncMock(
        return_value=[
            {
                "created_at": test_end,
                "unit": 5,
                "value": 100,
                "activity_category": ActivityCategory.WALK,
            }
        ]
    )
    mocker.patch(QUERY_MODULE, return_value=mock_base_query)
    actual_value = await ActivityService.get_dashboard_summary(
        TEST_MEMBER_ID, test_start, test_end, DistanceUnit.KM
    )
    expected_today = TodaysSummary(
        steps=0,
        active_minutes=0,
        distance=0,
        distance_unit=DistanceUnit.KM,
    )

    assert actual_value == ActivitySummary(
        today=expected_today,
        exercise_days=0,
        total_days=TOTAL_DAYS,
        activity_details=None,
        lastUpdate=test_end,
    )


@pytest.mark.asyncio
async def test_get_measures_data_1(mocker):
    """
    get_measures_data should return the dashboard summary for activity measures
    and distance values using miles.
    """
    mock_base_query = MagicMock()
    mock_base_query.order_by.return_value.values = AsyncMock(
        return_value=[
            {
                "created_at": test_end,
                "unit": 6,
                "value": 2000,
                "activity_category": ActivityCategory.WALK,
            },
            {
                "created_at": test_end,
                "unit": 8,
                "value": 100,
                "activity_category": ActivityCategory.EXERCISE,
            },
        ]
    )
    mocker.patch(QUERY_MODULE, return_value=mock_base_query)

    with mocker.patch("pendulum.now", return_value=test_end):
        actual_value = await ActivityService.get_measures_data(
            TEST_MEMBER_ID, test_end, test_end
        )

    expected_today = TodaysSummary(
        steps=2000,
        active_minutes=0,
        distance=0.95,
        distance_unit=DistanceUnit.MI,
    )

    assert actual_value == MeasuresResponse(
        unit=DistanceUnit.MI,
        type=MeasureType.ACTIVITY,
        summary=ActivitySummary(
            today=expected_today,
            exercise_days=1,
            total_days=TOTAL_DAYS,
            activity_details=None,
            lastUpdate=test_end,
        ),
        values=[
            ActivityDay(
                exercised=True, minutes=0, date="2025-06-05", steps=2000
            ),
        ],
    )


@pytest.mark.asyncio
async def test_get_measures_data_2(mocker):
    """
    get_measures_data should return the dashboard summary for activity measures
    consolidating day measures and distance values using kilometers.
    """
    mock_base_query = MagicMock()
    mock_base_query.order_by.return_value.values = AsyncMock(
        return_value=[
            {
                "created_at": test_end,
                "unit": 8,
                "value": 100,
                "activity_category": ActivityCategory.EXERCISE,
            },
            {
                "created_at": test_end,
                "unit": UnitOfMeasurement.STEP,
                "value": 1500,
                "activity_category": ActivityCategory.WALK,
            },
            {
                "created_at": test_end,
                "unit": UnitOfMeasurement.MINUTE,
                "value": 60,
                "activity_category": ActivityCategory.GENERAL,
            },
        ]
    )
    mocker.patch(QUERY_MODULE, return_value=mock_base_query)

    with mocker.patch("pendulum.now", return_value=test_end):
        actual_value = await ActivityService.get_measures_data(
            TEST_MEMBER_ID, test_end, test_end, DistanceUnit.KM
        )

    expected_today = TodaysSummary(
        steps=1500,
        active_minutes=60,
        distance=3.48,
        distance_unit=DistanceUnit.KM,
    )

    assert actual_value == MeasuresResponse(
        unit=DistanceUnit.KM,
        type=MeasureType.ACTIVITY,
        summary=ActivitySummary(
            today=expected_today,
            exercise_days=0,
            total_days=TOTAL_DAYS,
            activity_details=None,
            lastUpdate=test_end,
        ),
        values=[
            ActivityDay(
                exercised=False, minutes=60, date="2025-06-05", steps=1500
            ),
        ],
    )
