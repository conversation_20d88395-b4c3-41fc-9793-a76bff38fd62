from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from app.helpers.transtek import (
    update_device_status,
    update_device_telemetry,
    check_existing_transtek_device,
)
from app.services.queue import Queue

from ciba_iot_etl.models.db.member import Member
from ciba_iot_etl.models.pydantic.transtek import (
    TranstekStatusMessage,
    TranstekTelemetryMessage,
)


class AsyncChainMock:
    def __init__(self, return_value):
        self.return_value = return_value
        self._filter_kwargs = None
        self._filter_args = None
        self._prefetch_args = None

    def filter(self, *args, **kwargs):
        self._filter_args = args
        self._filter_kwargs = kwargs
        return self

    def first(self):
        return self

    def prefetch_related(self, *args):
        self._prefetch_args = args
        return self

    def __await__(self):
        async def _return():
            return self.return_value

        return _return().__await__()


@pytest.mark.asyncio
async def test_update_device_status():
    mock_device = MagicMock()
    mock_member = MagicMock(spec=Member)
    mock_member.id = "123"

    mock_device.member = mock_member
    mock_device.member.platforms = []
    mock_device.save = AsyncMock()

    mock_chain = AsyncChainMock(mock_device)

    message = MagicMock(spec=TranstekStatusMessage)
    message.deviceId = "123"

    with patch("app.helpers.transtek.Transtek", mock_chain):
        await update_device_status(message)
        mock_device.save.assert_called_once()


@pytest.mark.asyncio
async def test_update_device_telemetry():
    mock_device = MagicMock()
    mock_member = MagicMock(spec=Member)
    mock_member.id = "123"

    mock_device.member = mock_member
    mock_device.member.platforms = []
    mock_device.save = AsyncMock()

    mock_chain = AsyncChainMock(mock_device)

    message = MagicMock(spec=TranstekTelemetryMessage)
    message.deviceId = "123"

    mock_queue = MagicMock(spec=Queue)
    mock_queue.send = AsyncMock()

    model_dump_json_mock = MagicMock(return_value="")

    with patch("app.helpers.transtek.Transtek", mock_chain):
        with patch(
            "app.helpers.transtek.PullDataNotification.model_dump_json",
            model_dump_json_mock,
        ):
            await update_device_telemetry(message, mock_queue, "")
            mock_queue.send.assert_called_once()


@pytest.mark.asyncio
async def test_check_existing_transtek_device_exists_by_imei():
    """Test that check_existing_transtek_device returns True when device exists by IMEI."""
    mock_device = MagicMock()
    mock_chain = AsyncChainMock(mock_device)

    with patch("app.helpers.transtek.Transtek", mock_chain):
        result = await check_existing_transtek_device(imei="123456789012345")

        assert result is True
        # Verify that filter was called with Q object
        assert mock_chain._filter_args is not None
        assert len(mock_chain._filter_args) == 1


@pytest.mark.asyncio
async def test_check_existing_transtek_device_exists_by_device_id():
    """Test that check_existing_transtek_device returns True when device exists by device_id."""
    mock_device = MagicMock()
    mock_chain = AsyncChainMock(mock_device)

    with patch("app.helpers.transtek.Transtek", mock_chain):
        result = await check_existing_transtek_device(device_id="DEV123456789")

        assert result is True
        # Verify that filter was called with Q object
        assert mock_chain._filter_args is not None
        assert len(mock_chain._filter_args) == 1


@pytest.mark.asyncio
async def test_check_existing_transtek_device_exists_by_both():
    """Test that check_existing_transtek_device returns True when device exists by both IMEI and device_id."""
    mock_device = MagicMock()
    mock_chain = AsyncChainMock(mock_device)

    with patch("app.helpers.transtek.Transtek", mock_chain):
        result = await check_existing_transtek_device(
            imei="123456789012345", device_id="DEV123456789"
        )

        assert result is True
        # Verify that filter was called with Q object
        assert mock_chain._filter_args is not None
        assert len(mock_chain._filter_args) == 1


@pytest.mark.asyncio
async def test_check_existing_transtek_device_not_exists():
    """Test that check_existing_transtek_device returns False when no device exists."""
    mock_chain = AsyncChainMock(None)

    with patch("app.helpers.transtek.Transtek", mock_chain):
        result = await check_existing_transtek_device(imei="123456789012345")

        assert result is False
        # Verify that filter was called with Q object
        assert mock_chain._filter_args is not None
        assert len(mock_chain._filter_args) == 1


@pytest.mark.asyncio
async def test_check_existing_transtek_device_none_parameters():
    """Test that check_existing_transtek_device returns False when both parameters are None."""
    mock_chain = AsyncChainMock(None)

    with patch("app.helpers.transtek.Transtek", mock_chain):
        result = await check_existing_transtek_device(
            imei=None, device_id=None
        )

        assert result is False
        # Verify that filter was called with Q object
        assert mock_chain._filter_args is not None
        assert len(mock_chain._filter_args) == 1


@pytest.mark.asyncio
async def test_check_existing_transtek_device_empty_strings():
    """Test that check_existing_transtek_device returns False when parameters are empty strings."""
    mock_chain = AsyncChainMock(None)

    with patch("app.helpers.transtek.Transtek", mock_chain):
        result = await check_existing_transtek_device(imei="", device_id="")

        assert result is False
        # Verify that filter was called with Q object
        assert mock_chain._filter_args is not None
        assert len(mock_chain._filter_args) == 1


@pytest.mark.asyncio
async def test_check_existing_transtek_device_default_parameters():
    """Test that check_existing_transtek_device works with default parameters."""
    mock_chain = AsyncChainMock(None)

    with patch("app.helpers.transtek.Transtek", mock_chain):
        result = await check_existing_transtek_device()

        assert result is False
        # Verify that filter was called with Q object
        assert mock_chain._filter_args is not None
        assert len(mock_chain._filter_args) == 1
