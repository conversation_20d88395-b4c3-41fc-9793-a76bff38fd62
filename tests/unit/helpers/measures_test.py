import pytest
from ciba_iot_etl.helpers.measurement import DeviceType
from ciba_iot_etl.models.pydantic.common import ActivityDevice

from app.common.messages import DEVICE_VENDOR_NOT_SUPPORTED
from app.helpers.measures import get_vendor_equivalence


@pytest.mark.parametrize(
    "test_vendor, expected_value",
    [
        (ActivityDevice.WITHINGS, DeviceType.WITHINGS),
        (ActivityDevice.FITBIT, DeviceType.FITBIT),
        (ActivityDevice.DEXCOM, DeviceType.DEXCOM),
    ],
)
def test_get_vendor_equivalence_success(test_vendor, expected_value):
    """
    get_vendor_equivalence should return the DeviceType equivalence.
    """
    actual_value = get_vendor_equivalence(test_vendor)

    assert expected_value == actual_value


def test_get_vendor_equivalence_failure():
    """
    get_vendor_equivalence should raise an exception
    when device vendor is not supported.
    """
    with pytest.raises(ValueError) as expected_error:
        get_vendor_equivalence(ActivityDevice.OURARING)

    assert expected_error.value.args[0] == DEVICE_VENDOR_NOT_SUPPORTED
