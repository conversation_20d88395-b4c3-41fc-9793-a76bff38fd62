from datetime import date

from app.helpers.dates import get_dates_range


def test_get_dates_range():
    """
    get_dates_range should return a list with all the dates
    from date 1 to date 2 including the limits
    """
    actual_value = get_dates_range(date(2025, 1, 15), date(2025, 1, 20))
    expected_value = [
        "2025-01-15",
        "2025-01-16",
        "2025-01-17",
        "2025-01-18",
        "2025-01-19",
        "2025-01-20",
    ]

    assert actual_value == expected_value
