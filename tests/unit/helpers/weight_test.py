from unittest.mock import AsyncMock, MagicMock, patch
from uuid import UUID

import pendulum
import pytest
from ciba_iot_etl.models.pydantic.common import ActivityDevice

from app.helpers.weight import get_last_sync_by_vendor

test_id = UUID("36bff7f3-58a1-4bbe-91ff-13ece2bda059")


@pytest.fixture
def mock_weight_model():
    weight_mock = MagicMock()
    filter_mock = MagicMock()
    order_mock = MagicMock()
    filter_mock.order_by.return_value = order_mock
    weight_mock.filter.return_value = filter_mock

    return weight_mock, order_mock


@pytest.mark.asyncio
async def test_get_last_sync_by_vendor_success(mock_weight_model):
    """
    get_last_sync_by_vendor should return last sync timestamp.
    """
    test_measure = MagicMock()
    test_measure.created_at = pendulum.parse("2025-01-01")
    weight_mock, order_mock = mock_weight_model
    order_mock.first = AsyncMock(return_value=test_measure)

    with patch("app.helpers.weight.Weight", weight_mock):
        actual_value = await get_last_sync_by_vendor(
            test_id, ActivityDevice.WITHINGS
        )

        assert actual_value == int(test_measure.created_at.timestamp())


@pytest.mark.asyncio
async def test_get_last_sync_by_vendor_with_no_values(mock_weight_model):
    """
    get_last_sync_by_vendor should return none
    when there is no records in weight table.
    """
    weight_mock, order_mock = mock_weight_model
    order_mock.first = AsyncMock(return_value=None)

    with patch("app.helpers.weight.Weight", weight_mock):
        actual_value = await get_last_sync_by_vendor(
            test_id, ActivityDevice.WITHINGS
        )

        assert actual_value is None
