from copy import deepcopy
from datetime import datetime, timedelta, timezone

import pendulum
import pytest

from app.helpers.sleep import (
    calculate_sleep_score,
    fill_sleep_days,
    get_summary,
    update_day_measure,
)
from app.pydantic_model.measures_api import (
    Sleep,
    SleepSummary,
    SleepScoreTable,
)

hour_parse_factor = 60
test_date = datetime(2025, 1, 1, tzinfo=timezone.utc)
test_day_key = "2025-01-20"

default_sleep_score = SleepScoreTable(
    duration_score=46, quality_score=15, restoration_score=15, sleep_score=62
)
zeros_sleep_score = SleepScoreTable(
    duration_score=0, quality_score=0, restoration_score=15, sleep_score=15
)

default_test_day_measure = Sleep(
    sleep_date=test_day_key,
    start_time="",
    end_time="",
    asleep_time=0,
    duration=0,
    efficiency=0,
    bed_time=0,
    sleep_score_table=default_sleep_score,
    deep_time=0,
    light_time=0,
    rem_time=0,
    wake_time=0,
    start_time_in_bed=pendulum.parse(test_day_key),
    end_time_in_bed=pendulum.parse(test_day_key),
)
test_day_measure = Sleep(
    sleep_date=test_day_key,
    start_time="00:00:00",
    end_time="08:00:00",
    asleep_time=425,
    duration=480,
    efficiency=90,
    sleep_score_table=default_sleep_score,
    bed_time=480,
    deep_time=100,
    light_time=200,
    rem_time=125,
    wake_time=55,
    start_time_in_bed=pendulum.parse("2025-01-20T00:00:00"),
    end_time_in_bed=pendulum.parse("2025-01-20T08:00:00"),
)


def test_fill_sleep_days():
    """
    fill_sleep_days should return a dictionary
    with all the dates in the specified range with default values.
    """
    test_range = ["2025-01-10", "2025-01-11", "2025-01-12"]
    actual_value = fill_sleep_days(test_range)

    assert test_range[0] in actual_value
    assert test_range[1] in actual_value
    assert test_range[2] in actual_value
    assert actual_value[test_range[0]].duration == 0
    assert actual_value[test_range[1]].duration == 0
    assert actual_value[test_range[2]].duration == 0


def test_update_day_measure_1():
    """
    update_day_measure should add the measure
    when the day is not present in the sleep days.
    """
    test_sleep_days = {test_day_key: default_test_day_measure}
    update_day_measure(test_sleep_days, test_day_key, test_day_measure)

    assert test_sleep_days[test_day_key] == test_day_measure


def test_update_day_measure_2():
    """
    update_day_measure should replace the measure
    when the day duration field has a falsy value.
    """
    test_sleep_days = {test_day_key: default_test_day_measure}
    update_day_measure(test_sleep_days, test_day_key, test_day_measure)

    assert test_sleep_days[test_day_key] == test_day_measure


def test_update_day_measure_3():
    """
    update_day_measure should add the measure fields values
    when the day duration field is already present in the sleep days.
    """
    test_measure = deepcopy(default_test_day_measure)
    test_measure.start_time = "00:00:00"
    test_measure.end_time = "00:00:00"
    test_sleep_days = {test_day_key: test_measure}
    update_day_measure(test_sleep_days, test_day_key, test_day_measure)

    assert (
        test_sleep_days[test_day_key].asleep_time
        == test_day_measure.asleep_time
    )
    assert test_sleep_days[test_day_key].duration == test_day_measure.duration
    assert test_sleep_days[test_day_key].bed_time == test_day_measure.bed_time
    assert (
        test_sleep_days[test_day_key].deep_time == test_day_measure.deep_time
    )
    assert (
        test_sleep_days[test_day_key].light_time == test_day_measure.light_time
    )
    assert test_sleep_days[test_day_key].rem_time == test_day_measure.rem_time
    assert (
        test_sleep_days[test_day_key].wake_time == test_day_measure.wake_time
    )


def test_get_summary_1():
    """
    get_summary should return the summary of the requested day
    when it is within the sleep days
    """
    test_measures = {
        "2025-01-01": Sleep(
            sleep_date=test_date.date().isoformat(),
            start_time=test_date.time().isoformat(),
            end_time=(test_date + timedelta(hours=8)).time().isoformat(),
            asleep_time=400,
            duration=480,
            efficiency=0,
            sleep_score_table=default_sleep_score,
            bed_time=480,
            deep_time=100,
            light_time=200,
            rem_time=100,
            wake_time=80,
            start_time_in_bed=test_date,
            end_time_in_bed=test_date + timedelta(hours=8),
        )
    }
    actual_value = get_summary(test_measures, test_date, test_date)

    assert isinstance(actual_value, SleepSummary)
    assert actual_value.date == test_date.date()
    assert actual_value.duration == test_measures["2025-01-01"].duration
    assert actual_value.asleep_time == test_measures["2025-01-01"].asleep_time
    assert actual_value.lastUpdate == test_date
    assert actual_value.sleep_score == default_sleep_score.sleep_score


@pytest.mark.parametrize(
    "test_measures, test_last_update",
    [({}, test_date), ({"2025-01-03": None}, None)],
)
def test_get_summary_2(test_measures, test_last_update):
    """
    get_summary should return None when there is no sleep days
    """
    actual_value = get_summary(test_measures, test_date, test_last_update)

    assert actual_value is None


def test_get_summary_3():
    """
    get_summary should return a sleep summary with default values
    when the requested day is not within the sleep days
    """
    test_measures = {"2025-01-02": None}
    actual_value = get_summary(test_measures, test_date, test_date)

    assert isinstance(actual_value, SleepSummary)
    assert actual_value.date == test_date.date()
    assert actual_value.duration == 0
    assert actual_value.asleep_time == 0
    assert actual_value.lastUpdate == test_date


@pytest.mark.parametrize(
    "duration, target, deep_time, rem_time, asleep_time, restfulness_factor, expected_value",
    [
        (
            test_day_measure.duration / hour_parse_factor,
            8,
            test_day_measure.deep_time / hour_parse_factor,
            test_day_measure.rem_time / hour_parse_factor,
            test_day_measure.asleep_time / hour_parse_factor,
            test_day_measure.efficiency / 100,
            SleepScoreTable(
                duration_score=50,
                quality_score=13,
                restoration_score=22,
                sleep_score=86,
            ),
        ),
        (0, 0, 0, 0, 0, 0.6, zeros_sleep_score),
    ],
)
def test_calculate_sleep_score(
    duration,
    target,
    deep_time,
    rem_time,
    asleep_time,
    restfulness_factor,
    expected_value,
):
    """
    calculate_sleep_score should return a sleep score table.
    """
    actual_value = calculate_sleep_score(
        duration, target, deep_time, rem_time, asleep_time, restfulness_factor
    )
    assert actual_value.duration_score == expected_value.duration_score
    assert actual_value.quality_score == expected_value.quality_score
    assert actual_value.restoration_score == expected_value.restoration_score
    assert actual_value.sleep_score == expected_value.sleep_score
