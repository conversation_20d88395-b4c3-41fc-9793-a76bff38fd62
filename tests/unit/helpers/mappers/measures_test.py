from unittest.mock import MagicMock
import pendulum
from ciba_iot_etl.extract.withings_api.common import (
    MeasureGetMeasGroup,
    MeasureGetMeasMeasure,
)
from ciba_iot_etl.extract.withings_api.const import MeasureType
from ciba_iot_etl.models.db.weight import Weight
from ciba_iot_etl.models.db.blood_pressure import BloodPressure

from app.helpers.mappers.measures import (
    map_withings_weight_group,
    map_transtek_weight,
    map_transtek_blood_pressure,
)
from app.pydantic_model.measures_api import Measure


def test_map_withings_weight_group():
    """
    map_withings_weight_group should return a list of Measure objects
    from a withings weight measures group.
    """
    test_timestamp = 1748736000
    test_data = MeasureGetMeasGroup(
        grpid=123,
        attrib=987,
        date=test_timestamp,
        created=test_timestamp,
        modified=test_timestamp,
        category=1,
        measures=[
            MeasureGetMeasMeasure(
                type=MeasureType.WEIGHT,
                value=7510,
                unit=-2,
            )
        ],
    )

    actual_value = map_withings_weight_group(test_data)

    assert actual_value == [
        Measure(
            value=165.6,
            unit="lbs",
            created_at=pendulum.from_timestamp(test_timestamp),
        )
    ]


def test_map_transtek_weight():
    weight = MagicMock(spec=Weight)
    weight.value = 100
    actual_value = map_transtek_weight(weight)

    assert actual_value is not None
    assert actual_value.value == 100


def test_map_transtek_blood_pressure():
    blood = MagicMock(spec=BloodPressure)
    blood.systolic_value = 100
    blood.diastolic_value = 100
    actual_values = map_transtek_blood_pressure(blood)

    assert actual_values[0] is not None
    assert actual_values[1] is not None
    assert actual_values[0].value == 100
    assert actual_values[1].value == 100
