import pytest

from app.helpers.url import append_query_string
from tests.unit.common import TEST_URL


@pytest.mark.parametrize("test_value", ["", None])
def test_append_query_string_1(test_value):
    """
    append_query_string should return the url without changes
    when the provided value is falsy
    """
    actual_value = append_query_string(TEST_URL, "param1", test_value)

    assert actual_value == TEST_URL


def test_append_query_string_2():
    """
    append_query_string should return the url and only the provided key-value pair
    when the provided value is truthy and the url has no query strings
    """
    actual_value = append_query_string(TEST_URL, "param1", "1")

    assert actual_value == f"{TEST_URL}?param1=1"


def test_append_query_string_3():
    """
    append_query_string should return the url with the provided key-value pair
    when the provided value is truthy
    """
    test_url = f"{TEST_URL}?param1=1"
    actual_value = append_query_string(test_url, "param2", "2")

    assert actual_value == f"{test_url}&param2=2"
