from datetime import datetime, timedelta

import pytest

from app.helpers.glucose_level import (
    get_glucose_events_count,
    get_glucose_level_events,
    group_measures,
)
from app.pydantic_model.measures_api import (
    GlucoseEventType,
    GlucoseLevelEventEntry,
    GroupOption,
)

test_date = datetime(2025, 1, 1)


@pytest.mark.parametrize(
    "test_measures, expected_value",
    [
        ([], (0, 0)),
        ([{"value": 90}], (0, 0)),
        ([{"value": 200}], (1, 0)),
        ([{"value": 65}], (0, 1)),
        ([{"value": 185}, {"value": 55}], (1, 1)),
    ],
)
def test_get_glucose_events_count(test_measures, expected_value):
    """
    get_glucose_events_count should return the total number of
    hypoglycemia and hyperglycemia events
    """
    actual_value = get_glucose_events_count(test_measures)

    assert actual_value == expected_value


@pytest.mark.parametrize(
    "test_measures, expected_value",
    [
        ([], []),
        ([{"created_at": test_date, "value": 85}], []),
        (
            [{"created_at": test_date, "value": 180}],
            [
                GlucoseLevelEventEntry(
                    createdAt=test_date, value=180, type=GlucoseEventType.HIGH
                )
            ],
        ),
        (
            [{"created_at": test_date, "value": 70}],
            [
                GlucoseLevelEventEntry(
                    createdAt=test_date, value=70, type=GlucoseEventType.LOW
                )
            ],
        ),
        (
            [
                {"created_at": test_date, "value": 60},
                {"created_at": test_date, "value": 90},
                {"created_at": test_date, "value": 190},
            ],
            [
                GlucoseLevelEventEntry(
                    createdAt=test_date, value=60, type=GlucoseEventType.LOW
                ),
                GlucoseLevelEventEntry(
                    createdAt=test_date, value=190, type=GlucoseEventType.HIGH
                ),
            ],
        ),
    ],
)
def test_get_glucose_events(test_measures, expected_value):
    """
    get_glucose_events should return a list of glucose events with a type
    to differentiate between hypoglycemia and hyperglycemia events
    """
    actual_value = get_glucose_level_events(test_measures)

    assert actual_value == expected_value


def test_group_measures_1():
    """
    group_measures should return an empty list
    when an empty measurements list is provided
    """
    test_measures = []
    actual_value = group_measures(test_measures)

    assert actual_value == test_measures


def test_group_measures_2():
    """
    group_measures should return a list of measurements grouped by day
    """
    next_day = test_date + timedelta(days=1)
    test_measures = [
        {"created_at": test_date, "value": 90},
        {"created_at": test_date, "value": 100},
        {"created_at": next_day, "value": 50},
        {"created_at": next_day, "value": 60},
    ]
    actual_value = group_measures(test_measures, GroupOption.DAY)

    assert actual_value[0]["value"] == 95
    assert actual_value[1]["value"] == 55


def test_group_measures_3():
    """
    group_measures should return a list of measurements grouped by hour
    """
    test_measures = [
        {"created_at": test_date, "value": 60},
        {"created_at": test_date + timedelta(minutes=15), "value": 80},
        {"created_at": test_date + timedelta(minutes=60), "value": 100},
        {"created_at": test_date + timedelta(minutes=75), "value": 120},
    ]
    actual_value = group_measures(test_measures, GroupOption.HOUR)

    assert actual_value[0]["value"] == 70
    assert actual_value[1]["value"] == 110
