#!/bin/sh
S3_PARTICIPANT_BUCKET_NAME=participant-ecosystem
S3_DATA_LAKE_BUCKET_NAME=ciba-data-lake-bucket
SQS_PULLER_QUEUE_NAME=iot-data-puller
SQS_PARTICIPANT_QUEUE_NAME=participant-iot-data-writer
SQS_PATIENT_QUEUE_NAME=patient-iot-data-writer
SNS_SLACK_NAME=slack-notification-topic
SNS_PARTICIPANT_NAME=iot-data-puller-participant-sns
SNS_PATIENT_NAME=iot-data-puller-patient-sns

#SQS
echo "Creating Data Puller SQS queue"
PULLER_QUEUE_URL=$(awslocal sqs create-queue --queue-name $SQS_PULLER_QUEUE_NAME | jq -r '.QueueUrl')

PULLER_QUEUE_ARN=$(awslocal sqs get-queue-attributes \
  --queue-url $PULLER_QUEUE_URL \
  --attribute-names QueueArn --query 'Attributes.QueueArn' --output text)

echo "Creating Participant Writer SQS queue"
PART_WRITER_QUEUE_URL=$(awslocal sqs create-queue --queue-name $SQS_PARTICIPANT_QUEUE_NAME | jq -r '.QueueUrl')

PART_WRITER_QUEUE_ARN=$(awslocal sqs get-queue-attributes \
  --queue-url $PART_WRITER_QUEUE_URL \
  --attribute-names QueueArn --query 'Attributes.QueueArn' --output text)

echo "Creating Patient Writer SQS queue"
PATIENT_WRITER_QUEUE_URL=$(awslocal sqs create-queue --queue-name $SQS_PATIENT_QUEUE_NAME | jq -r '.QueueUrl')

PATIENT_WRITER_QUEUE_ARN=$(awslocal sqs get-queue-attributes \
  --queue-url $PATIENT_WRITER_QUEUE_URL \
  --attribute-names QueueArn --query 'Attributes.QueueArn' --output text)

# SNS
echo "Creating Slack Notification SNS topic"
SLACK_SNS_TOPIC_ARN=$(awslocal sns create-topic --name $SNS_SLACK_NAME | jq -r '.TopicArn')

awslocal ses verify-email-identity --email $SMTP_USER

awslocal sns subscribe \
   --topic-arn $SLACK_SNS_TOPIC_ARN \
   --protocol email \
   --notification-endpoint $SMTP_USER

PARTICIPANT_SNS_TOPIC_ARN=$(awslocal sns create-topic --name $SNS_PARTICIPANT_NAME | jq -r '.TopicArn')
awslocal sns subscribe \
   --topic-arn $PARTICIPANT_SNS_TOPIC_ARN \
   --protocol sqs \
   --attributes RawMessageDelivery=true \
   --notification-endpoint $PART_WRITER_QUEUE_ARN

PATIENT_SNS_TOPIC_ARN=$(awslocal sns create-topic --name $SNS_PATIENT_NAME | jq -r '.TopicArn')
awslocal sns subscribe \
   --topic-arn $PATIENT_SNS_TOPIC_ARN \
   --protocol sqs \
   --attributes RawMessageDelivery=true \
   --notification-endpoint $PATIENT_WRITER_QUEUE_ARN

# S3
echo "Creating Participant S3 bucket"
awslocal s3api create-bucket --bucket $S3_PARTICIPANT_BUCKET_NAME

echo "Creating Data Lake S3 bucket"
awslocal s3api create-bucket --bucket $S3_DATA_LAKE_BUCKET_NAME

awslocal s3api list-buckets

echo "SUMMARY"
echo "-----------------------"
echo "PULLER_QUEUE_ARN: ${PULLER_QUEUE_ARN}"
echo "PART_WRITER_QUEUE_ARN: ${PART_WRITER_QUEUE_ARN}"
echo "PATIENT_WRITER_QUEUE_ARN: ${PATIENT_WRITER_QUEUE_ARN}"
echo "SLACK_SNS_TOPIC_ARN: ${SLACK_SNS_TOPIC_ARN}"
echo "PARTICIPANT_SNS_TOPIC_ARN: ${PARTICIPANT_SNS_TOPIC_ARN}"
echo "PATIENT_SNS_TOPIC_ARN: ${PATIENT_SNS_TOPIC_ARN}"
echo "-----------------------"
echo "Data Puller Queue URL: ${PULLER_QUEUE_URL}"
echo "Participant Writer Queue URL: ${PART_WRITER_QUEUE_URL}"
echo "Patient Writer Queue URL: ${PATIENT_WRITER_QUEUE_URL}"
echo "-----------------------"
