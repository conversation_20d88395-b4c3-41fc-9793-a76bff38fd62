SHELL := /bin/bash
export IMAGE_PATH := rpm-registration-api

COMPOSE_FILE = docker-compose.local.yaml
COMPOSE = docker compose -f $(COMPOSE_FILE)

lint:
	uv run black .
	uv run isort .
	uv run flake8 .
	# poetry run mypy .
	uv run pylint --fail-under=9.5 app tests

build:
	@echo "Building images with --no-cache..."
	$(COMPOSE) build

up:
	@echo "Starting services in detached mode..."
	$(COMPOSE) up -d

down:
	@echo "Stopping and removing containers, networks, and volumes..."
	$(COMPOSE) down -v

api:
	@echo "Building RPM api"
	$(COMPOSE) down app
	$(COMPOSE) build app
	$(COMPOSE) up -d app

rebuild: down build up
restart: down up
