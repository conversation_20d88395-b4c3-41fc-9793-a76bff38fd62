<!-- templates/user_detail.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>User Detail - {{ user.email }}</title>
  <style>
    body {
      font-family: Arial, sans-serif;
    }
    .device {
      border: 1px solid #ccc;
      padding: 10px;
      margin-bottom: 20px;
    }
    .buttons {
      margin-top: 10px;
    }
    .buttons button {
      margin-right: 10px;
    }
    .date-range {
      margin-top: 10px;
    }
    .date-range label {
      margin-right: 5px;
    }
    .date-range input {
      margin-right: 10px;
    }
    table {
      border-collapse: collapse;
      width: 80%;
      margin-top: 10px;
    }
    table, th, td {
      border: 1px solid #ccc;
    }
    th, td {
      padding: 8px;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
    }
    .token-expired {
      color: red;
      font-weight: bold;
    }
    .token-valid {
      color: green;
      font-weight: bold;
    }
    .reauth-button {
      background-color: #ff6b6b;
      color: white;
      font-weight: bold;
      border: none;
      padding: 8px 12px;
      border-radius: 4px;
      cursor: pointer;
    }
    .reauth-button:hover {
      background-color: #ff5252;
    }
    /* Collapsible CSS */
    .collapsible {
      background-color: #eee;
      color: #444;
      cursor: pointer;
      padding: 10px;
      width: 100%;
      border: none;
      text-align: left;
      outline: none;
      font-size: 16px;
      margin-top: 10px;
    }
    .active, .collapsible:hover {
      background-color: #ccc;
    }
    .content {
      padding: 0 10px;
      display: none;
      overflow: hidden;
    }
    .device-info-container {
      margin-top: 15px;
      padding: 15px;
      background-color: #f9f9f9;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    .device-info-container h3 {
      margin-top: 0;
      color: #444;
      border-bottom: 2px solid #eee;
      padding-bottom: 8px;
      margin-bottom: 15px;
    }
    .device-details {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    .device-item {
      flex: 1;
      min-width: 300px;
      padding: 15px;
      border: 1px solid #eee;
      border-radius: 6px;
      background-color: white;
      box-shadow: 0 1px 3px rgba(0,0,0,0.05);
      transition: transform 0.2s, box-shadow 0.2s;
    }
    .device-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    .device-item h4 {
      margin-top: 0;
      color: #333;
      border-bottom: 1px solid #eee;
      padding-bottom: 8px;
      margin-bottom: 12px;
      font-size: 16px;
    }
    .device-item ul {
      list-style-type: none;
      padding-left: 0;
      margin: 0;
    }
    .device-item li {
      padding: 5px 0;
      border-bottom: 1px dotted #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
    }
    .device-item li strong {
      flex: 0 0 40%;
      color: #666;
      font-weight: 600;
    }
  </style>
</head>
<body>
  <h1>User Detail: {{ user.email }}</h1>
  <p><strong>ID:</strong> {{ user.id }}</p>
  <p>
    <a href="/manager/ui/users">Back to User List</a>
  </p>

  <!-- Device Data Section for Withings -->
  {% if user.withings %}
  <div class="device">
    <h2>Withings Device</h2>
    <p><strong>Device ID:</strong> {{ user.withings.id }}</p>
    <p><strong>Account ID:</strong> {{ user.withings.user_id }}</p>
    <p><strong>Updated At:</strong> {{ user.withings.updated_at }}</p>
    <p><strong>Healthy:</strong> {{ user.withings.healthy }}</p>
    <p><strong>Access Token Expires At:</strong> {{ user.withings.access_token_expires_at }}</p>
    <p><strong>Is Access Token Expired:</strong> <span class="{% if user.withings.is_access_token_expired() %}token-expired{% else %}token-valid{% endif %}">{{ user.withings.is_access_token_expired() }}</span></p>
    <div class="buttons">
      <button onclick="syncDevice('{{ user.email }}', 'withings')">Sync Withings</button>
      <button onclick="disconnectDevice('{{ user.email }}', 'withings')">Disconnect Withings</button>
      <button onclick="refreshToken('{{ user.email }}', 'withings')">Refresh Token</button>
      <button onclick="getDeviceInfo('{{ user.email }}', 'withings')">Get Device Info</button>
      {% if not user.withings.healthy or user.withings.is_access_token_expired() %}
      <button onclick="window.location.href='/auth/withings?email={{ user.email }}'" class="reauth-button">Re-authenticate Withings</button>
      {% endif %}
    </div>
    <div class="date-range">
      <label for="start_date_withings">Start Date:</label>
      <input type="date" id="start_date_withings">
      <label for="end_date_withings">End Date:</label>
      <input type="date" id="end_date_withings">
      <button onclick="getUserData('{{ user.email }}', 'withings')">Get Withings Data</button>
    </div>
    <!-- Container for Withings data table -->
    <div id="data_table_withings"></div>
  </div>
  {% endif %}

  <!-- Device Data Section for Fitbit -->
  {% if user.fitbit %}
  <div class="device">
    <h2>Fitbit Device</h2>
    <p><strong>Device ID:</strong> {{ user.fitbit.id }}</p>
    <p><strong>Account ID:</strong> {{ user.fitbit.user_id }}</p>
    <p><strong>Updated At:</strong> {{ user.fitbit.updated_at }}</p>
    <p><strong>Healthy:</strong> {{ user.fitbit.healthy }}</p>
    <p><strong>Access Token Expires At:</strong> {{ user.fitbit.access_token_expires_at }}</p>
    <p><strong>Is Access Token Expired:</strong> <span class="{% if user.fitbit.is_access_token_expired() %}token-expired{% else %}token-valid{% endif %}">{{ user.fitbit.is_access_token_expired() }}</span></p>
    <div class="buttons">
      <button onclick="syncDevice('{{ user.email }}', 'fitbit')">Sync Fitbit</button>
      <button onclick="disconnectDevice('{{ user.email }}', 'fitbit')">Disconnect Fitbit</button>
      <button onclick="refreshToken('{{ user.email }}', 'fitbit')">Refresh Token</button>
      <button onclick="getDeviceInfo('{{ user.email }}', 'fitbit')">Get Device Info</button>
      {% if not user.fitbit.healthy or user.fitbit.is_access_token_expired() %}
      <button onclick="window.location.href='/auth/fitbit?email={{ user.email }}'" class="reauth-button">Re-authenticate Fitbit</button>
      {% endif %}
    </div>
    <div class="date-range">
      <label for="start_date_fitbit">Start Date:</label>
      <input type="date" id="start_date_fitbit">
      <label for="end_date_fitbit">End Date:</label>
      <input type="date" id="end_date_fitbit">
      <button onclick="getUserData('{{ user.email }}', 'fitbit')">Get Fitbit Data</button>
    </div>
    <!-- Container for Fitbit data table -->
    <div id="data_table_fitbit"></div>
  </div>
  {% endif %}

  <!-- Connected Platforms Section -->
  <h2>Connected Platforms</h2>
  <ul>
    {% for platform in user.member_platforms %}
      <li>
        <strong>{{ platform.platform_type }}</strong> - {{ platform.platform_id }}<br>
        <small>Created at: {{ platform.created_at }} | Updated at: {{ platform.updated_at }}</small>
      </li>
    {% endfor %}
  </ul>

  <!-- Recent Activities Section -->
  {% if user.activities %}
  <h2>Recent Activities in RPM DB</h2>
  {% for activity_name, records in user.activities.items() %}
    <h3>{{ activity_name }}</h3>
    {% if activity_name == "BloodPressure" %}
      <table>
        <thead>
          <tr>
            <th>Device</th>
            <th>Created At</th>
            <th>Updated At</th>
            <th>Systolic Value</th>
            <th>Diastolic Value</th>
          </tr>
        </thead>
        <tbody>
          {% for record in records %}
            <tr>
              <td>{{ record.device }}</td>
              <td>{{ record.created_at }}</td>
              <td>{{ record.updated_at }}</td>
              <td>{{ record.systolic_value }}</td>
              <td>{{ record.diastolic_value }}</td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    {% elif activity_name == "Sleep" %}
      <table>
        <thead>
          <tr>
            <th>Device</th>
            <th>Created At</th>
            <th>Updated At</th>
            <th>Duration (hours)</th>
            <th>Efficiency</th>
          </tr>
        </thead>
        <tbody>
          {% for record in records %}
            <tr>
              <td>{{ record.device }}</td>
              <td>{{ record.created_at }}</td>
              <td>{{ record.updated_at }}</td>
              <td>{{ (record.duration / 60) | round(2) }}</td>
              <td>{{ record.efficiency }}</td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    {% else %}
      <table>
        <thead>
          <tr>
            <th>Device</th>
            <th>Created At</th>
            <th>Updated At</th>
            <th>Unit</th>
            <th>Value</th>
          </tr>
        </thead>
        <tbody>
          {% for record in records %}
            <tr>
              <td>{{ record.device }}</td>
              <td>{{ record.created_at }}</td>
              <td>{{ record.updated_at }}</td>
              <td>{{ record.unit }}</td>
              <td>{{ record.value }}</td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    {% endif %}
  {% endfor %}
  {% endif %}


  <script>
    async function syncDevice(email, device) {
      try {
        const response = await fetch(`/manager/ui/users/${email}/sync?device=${device}`, {
          method: 'GET'
        });
        if (!response.ok) {
          throw new Error('Network response was not ok: ' + response.statusText);
        }
        const data = await response.json();
        alert(`Sync successful for ${device}: ${JSON.stringify(data)}`);
      } catch (error) {
        console.error('Error syncing device:', error);
        alert(`Error syncing ${device}: ${error}`);
      }
    }

    async function getDeviceInfo(email, device) {
      try {
        const response = await fetch(`/manager/ui/users/${email}/device-info/${device}`, {
          method: 'GET'
        });
        if (!response.ok) {
          throw new Error('Network response was not ok: ' + response.statusText);
        }
        const data = await response.json();

        if (data.success) {
          console.log('Device info data:', data);

          // Create a container for device info if it doesn't exist
          const deviceType = device.toLowerCase();
          let deviceInfoContainer = document.getElementById(`device_info_${deviceType}`);
          if (!deviceInfoContainer) {
            deviceInfoContainer = document.createElement('div');
            deviceInfoContainer.id = `device_info_${deviceType}`;
            deviceInfoContainer.className = 'device-info-container';

            // Find the right place to insert it (after the data_table div)
            const deviceDivs = document.querySelectorAll('.device');
            let targetDiv = null;

            // Find the device div that contains the device name
            for (const div of deviceDivs) {
              const h2 = div.querySelector('h2');
              if (h2 && h2.textContent.includes(`${deviceType.charAt(0).toUpperCase() + deviceType.slice(1)} Device`)) {
                targetDiv = div;
                break;
              }
            }

            if (targetDiv) {
              const dataTableDiv = targetDiv.querySelector(`#data_table_${deviceType}`);
              if (dataTableDiv) {
                dataTableDiv.parentNode.insertBefore(deviceInfoContainer, dataTableDiv);
              } else {
                // Fallback - add to the end of the device div
                targetDiv.appendChild(deviceInfoContainer);
              }
            } else {
              // If we can't find the device div, just add it to the body
              console.warn(`Could not find device div for ${deviceType}`);
              document.body.appendChild(deviceInfoContainer);
            }
          }

          // Format and display the device info
          let html = '<h3>Device Information</h3>';
          if (data.devices && data.devices.length > 0) {
            html += '<div class="device-details">';
            // Add a debug message to see what we're working with
            console.log('Devices data:', data.devices);

            data.devices.forEach((deviceData, index) => {
              html += `<div class="device-item">`;
              // Get the device model name based on the device type
              let deviceModel = 'Unknown';
              if (deviceData.model) {
                deviceModel = deviceData.model;
              } else if (deviceData.deviceVersion) {
                deviceModel = deviceData.deviceVersion;
              }
              html += `<h4>Device ${index + 1}: ${deviceModel}</h4>`;
              html += `<ul>`;
              // Determine if this is a Fitbit device based on its properties
              const isFitbitDevice = deviceData.hasOwnProperty('deviceVersion') && deviceData.hasOwnProperty('batteryLevel');

              // Define the order of important fields to display first based on device type
              const priorityFields = isFitbitDevice ?
                ['deviceVersion', 'type', 'battery', 'batteryLevel', 'mac', 'id', 'lastSyncTime'] :
                ['model', 'type', 'battery', 'mac_address', 'deviceid', 'first_session_date', 'last_session_date', 'fw', 'timezone'];

              // Helper function to format battery status
              const formatBatteryStatus = (status, level) => {
                if (!status) return 'Unknown';

                // For Fitbit devices with battery level percentage
                if (level !== undefined) {
                  let icon = '🔋';
                  if (level < 25) icon = '🪫';
                  return `${icon} ${status} (${level}%)`;
                }

                // For Withings devices with text status
                const batteryIcons = {
                  'high': '🔋 High',
                  'medium': '🔋 Medium',
                  'low': '🪫 Low (Please charge soon)'
                };

                return batteryIcons[status.toLowerCase()] || status;
              };

              // First display priority fields in the specified order
              for (const field of priorityFields) {
                if (deviceData.hasOwnProperty(field)) {
                  let value = deviceData[field];

                  // Special formatting for certain fields
                  if (field === 'battery') {
                    const batteryLevel = deviceData.batteryLevel;
                    value = formatBatteryStatus(value, batteryLevel);
                  }

                  // Format lastSyncTime for Fitbit
                  if (field === 'lastSyncTime' && value) {
                    // Convert ISO date string to a more readable format
                    const date = new Date(value);
                    if (!isNaN(date.getTime())) {
                      value = date.toLocaleString();
                    }
                  }

                  // Format the field name for better readability
                  const formattedField = field
                    .replace(/_/g, ' ')
                    .replace(/\b\w/g, l => l.toUpperCase());

                  html += `<li><strong>${formattedField}</strong> <span>${value}</span></li>`;
                }
              }

              // Then display any remaining fields
              for (const [key, value] of Object.entries(deviceData)) {
                if (!priorityFields.includes(key)) {
                  // Format the field name for better readability
                  const formattedField = key
                    .replace(/_/g, ' ')
                    .replace(/\b\w/g, l => l.toUpperCase());

                  html += `<li><strong>${formattedField}</strong> <span>${value}</span></li>`;
                }
              }
              html += `</ul>`;
              html += `</div>`;
            });
            html += '</div>';
          } else {
            html += '<p>No devices found.</p>';
          }

          deviceInfoContainer.innerHTML = html;
        } else {
          alert(`Error getting device info: ${data.error || 'Unknown error'}`);
        }
      } catch (error) {
        console.error('Error getting device info:', error);
        alert(`Error getting device info for ${device}: ${error}`);
      }
    }

    async function disconnectDevice(email, device) {
      try {
        const response = await fetch(`/manager/ui/users/${email}/disconnect?device=${device}`, {
          method: 'GET'
        });
        if (!response.ok) {
          throw new Error('Network response was not ok: ' + response.statusText);
        }
        const data = await response.json();
        alert(`Disconnect successful for ${device}: ${JSON.stringify(data)}`);
      } catch (error) {
        console.error('Error disconnecting device:', error);
        alert(`Error disconnecting ${device}: ${error}`);
      }
    }

    async function refreshToken(email, device) {
      try {
        const response = await fetch(`/manager/ui/users/${email}/refresh-token?device=${device}`, {
          method: 'GET'
        });
        if (!response.ok) {
          throw new Error('Network response was not ok: ' + response.statusText);
        }
        const data = await response.json();
        if (data.success) {
          alert(`Token refresh successful for ${device}: ${data.message}`);
          // Reload the page to show updated token information
          window.location.reload();
        } else if (data.requires_reauth) {
          const reauth = confirm(`${data.error}\n\nWould you like to re-authenticate now?`);
          if (reauth) {
            window.location.href = `/auth/${device.toLowerCase()}?email=${email}`;
          }
        } else {
          alert(`Error refreshing token for ${device}: ${data.error || 'Unknown error'}`);
        }
      } catch (error) {
        console.error('Error refreshing token:', error);
        alert(`Error refreshing token for ${device}: ${error}`);
      }
    }

    // Build a simple table from an array of records.
    function buildTable(data) {
      if (!Array.isArray(data)) {
        data = [data];
      }
      if (data.length === 0) {
        return '<table><tr><td>No Data Available</td></tr></table>';
      }
      const keys = Object.keys(data[0]);
      let html = '<table>';
      html += '<thead><tr>';
      keys.forEach(key => {
        html += `<th>${key}</th>`;
      });
      html += '</tr></thead>';
      html += '<tbody>';
      data.forEach(record => {
        html += '<tr>';
        keys.forEach(key => {
          html += `<td>${record[key]}</td>`;
        });
        html += '</tr>';
      });
      html += '</tbody></table>';
      return html;
    }

    // Build a collapsible table from an object with sections.
    function buildCollapsibleTable(data) {
      let html = '';
      for (const section in data) {
        if (data.hasOwnProperty(section)) {
          const records = data[section];
          html += `<div class="collapsible-section">
                     <button class="collapsible">${section} (${Array.isArray(records) ? records.length : 0})</button>
                     <div class="content">`;
          if (Array.isArray(records) && records.length > 0) {
            html += buildTable(records);
          } else {
            html += '<p>No data available.</p>';
          }
          html += '</div></div>';
        }
      }
      return html;
    }

    async function getUserData(email, device) {
      try {
        let url = `/manager/ui/users/${email}/data/${device}`;
        const startInput = document.getElementById(`start_date_${device.toLowerCase()}`);
        const endInput = document.getElementById(`end_date_${device.toLowerCase()}`);
        const params = [];
        if (startInput && startInput.value) {
          params.push(`start_date=${encodeURIComponent(startInput.value)}`);
        }
        if (endInput && endInput.value) {
          params.push(`end_date=${encodeURIComponent(endInput.value)}`);
        }
        if (params.length > 0) {
          url += '?' + params.join('&');
        }
        const response = await fetch(url, { method: 'GET' });
        if (!response.ok) {
          throw new Error('Network response was not ok: ' + response.statusText);
        }
        const data = await response.json();
        let tableHtml = '';
        // If the device is WITHINGS and the data has "weight_measurements",
        // or if the device is FITBIT and the data has "activity_measurements", use collapsible view.
        if ((device.toUpperCase() === 'WITHINGS' && data.hasOwnProperty('weight_measurements')) ||
            (device.toUpperCase() === 'FITBIT' && data.hasOwnProperty('activity_measurements'))) {
          tableHtml = buildCollapsibleTable(data);
        } else {
          tableHtml = buildTable(data);
        }
        document.getElementById(`data_table_${device.toLowerCase()}`).innerHTML = tableHtml;

        // Add collapsible toggle functionality.
        const coll = document.getElementsByClassName("collapsible");
        for (const element of coll) {
          element.addEventListener("click", function() {
            this.classList.toggle("active");
            const content = this.nextElementSibling;
            if (content.style.display === "block") {
              content.style.display = "none";
            } else {
              content.style.display = "block";
            }
          });
        }
      } catch (error) {
        console.error('Error getting user data:', error);
        alert(`Error getting data for ${device}: ${error}`);
      }
    }
  </script>
</body>
</html>
