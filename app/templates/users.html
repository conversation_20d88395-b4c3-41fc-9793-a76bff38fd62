<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>User List</title>
  <style>
    table {
      border-collapse: collapse;
      width: 80%;
      margin-bottom: 10px;
    }
    th, td {
      border: 1px solid #ccc;
      padding: 8px;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
    }
    .search-bar {
      margin-bottom: 20px;
    }
    .health-check {
      margin: 20px 0;
    }
    /* Optional styling for pagination buttons */
    #usersPagination button,
    #healthPagination button {
      margin: 0 2px;
      padding: 4px 8px;
    }
  </style>
</head>
<body>
  <h1>User List</h1>

  <!-- Toggle button for the Users Table -->
  <button id="toggleUsersTableButton" onclick="toggleTable('usersTableContainer')">
    Toggle Users Table
  </button>

  <!-- Client-side search bar (applies to users table) -->
  <div class="search-bar">
    <input type="text" id="searchInput" placeholder="Search by email...">
  </div>

  <!-- Users Table Container -->
  <div id="usersTableContainer">
    <h2>Users Table</h2>
    <table id="usersTable">
      <thead>
        <tr>
          <th>ID</th>
          <th>Email</th>
          <!-- Add more columns as needed -->
        </tr>
      </thead>
      <tbody>
        {% for user in users %}
        <tr>
          <td>{{ user.id }}</td>
          <td>
            <a href="/manager/ui/users/{{ user.email }}">
              {{ user.email }}
            </a>
          </td>
        </tr>
        {% else %}
        <tr>
          <td colspan="2">No users found.</td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
    <!-- Pagination controls for Users Table -->
    <div id="usersPagination"></div>
  </div>

  <!-- Devices Health Section -->
  <h2>Devices Health</h2>
  <div class="health-check">
    <button id="checkFitbitHealth" onclick="checkHealth('fitbit')">
      Check Fitbit Health
    </button>
    <button id="checkWithingsHealth" onclick="checkHealth('withings')">
      Check Withings Health
    </button>
    <!-- Toggle button for Health Check Results -->
    <button id="toggleHealthTableButton" onclick="toggleTable('healthResultsContainer')">
      Toggle Health Results
    </button>
  </div>

  <!-- Health Check Results Container (initially hidden) -->
  <div id="healthResultsContainer" style="display: none;">
    <h2>Health Check Results</h2>
    <table id="healthResultsTable">
      <thead>
        <tr>
          <th>Email</th>
          <th>Status</th>
        </tr>
      </thead>
      <tbody>
        <!-- Health check rows will be added dynamically -->
      </tbody>
    </table>
    <!-- Pagination controls for Health Check Results (if necessary) -->
    <div id="healthPagination"></div>
  </div>

  <!-- JavaScript for toggle, pagination, and search -->
  <script>
    /* --- Toggle Function for Expandable/Collapsible Tables --- */
    function toggleTable(containerId) {
      const container = document.getElementById(containerId);
      // If container is hidden, show it; otherwise, hide it.
      if (container.style.display === "none" || container.style.display === "") {
        container.style.display = "block";
      } else {
        container.style.display = "none";
      }
    }

    /* --- Pagination and Search for the Users Table --- */
    const rowsPerPage = 10;
    let currentPage = 1;
    const usersTableBody = document.querySelector('#usersTable tbody');
    // Capture all rows as an array (this list is used for filtering and pagination)
    const allUsersRows = Array.from(usersTableBody.querySelectorAll('tr'));
    let filteredUsersRows = allUsersRows;

    // Render the current page of the users table
    function renderUsersTable() {
      // Clear the table body
      usersTableBody.innerHTML = '';

      // Calculate the total pages based on the filtered rows
      const totalRows = filteredUsersRows.length;
      const totalPages = Math.ceil(totalRows / rowsPerPage);

      // Adjust currentPage if needed
      if (currentPage > totalPages) currentPage = totalPages;
      if (currentPage < 1) currentPage = 1;

      // Determine which rows to display on this page
      const startIndex = (currentPage - 1) * rowsPerPage;
      const endIndex = startIndex + rowsPerPage;
      const rowsToDisplay = filteredUsersRows.slice(startIndex, endIndex);

      // Append each row to the table body
      rowsToDisplay.forEach(row => {
        usersTableBody.appendChild(row);
      });

      // Render pagination buttons
      renderUsersPagination(totalPages);
    }

    // Render pagination buttons for the users table
    function renderUsersPagination(totalPages) {
      const paginationDiv = document.getElementById('usersPagination');
      paginationDiv.innerHTML = '';

      if (totalPages <= 1) return; // Do not render pagination if only one page exists

      // Create Previous button
      const prevButton = document.createElement('button');
      prevButton.textContent = 'Previous';
      prevButton.disabled = (currentPage === 1);
      prevButton.addEventListener('click', function() {
        if (currentPage > 1) {
          currentPage--;
          renderUsersTable();
        }
      });
      paginationDiv.appendChild(prevButton);

      // Create numbered page buttons
      for (let i = 1; i <= totalPages; i++) {
        const pageButton = document.createElement('button');
        pageButton.textContent = i;
        if (i === currentPage) {
          pageButton.disabled = true;
        }
        pageButton.addEventListener('click', function() {
          currentPage = i;
          renderUsersTable();
        });
        paginationDiv.appendChild(pageButton);
      }

      // Create Next button
      const nextButton = document.createElement('button');
      nextButton.textContent = 'Next';
      nextButton.disabled = (currentPage === totalPages);
      nextButton.addEventListener('click', function() {
        if (currentPage < totalPages) {
          currentPage++;
          renderUsersTable();
        }
      });
      paginationDiv.appendChild(nextButton);
    }

    // Search functionality: filter users based on email
    const searchInput = document.getElementById('searchInput');
    searchInput.addEventListener('input', function() {
      const filterText = searchInput.value.toLowerCase();
      // Filter the full list of rows based on whether the email cell contains the search text
      filteredUsersRows = allUsersRows.filter(row => {
        const emailCell = row.querySelector('td:nth-child(2)');
        return emailCell && emailCell.textContent.toLowerCase().includes(filterText);
      });
      currentPage = 1; // Reset to first page on new search
      renderUsersTable();
    });

    // Initial render of the users table
    renderUsersTable();

    /* --- Function to Check Health (with Pagination for Health Results) --- */
    async function checkHealth(device) {
      // Construct the backend URL (ensure you have a corresponding route)
      const url = `/manager/ui/devices_health/${device}`;
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        // Expecting a JSON response like: { "<EMAIL>": true, "<EMAIL>": false, ... }
        const data = await response.json();
        const healthTableBody = document.querySelector('#healthResultsTable tbody');
        healthTableBody.innerHTML = '';

        // Build an array of rows for the health check results
        const healthRows = [];
        for (const [email, healthy] of Object.entries(data)) {
          const row = document.createElement('tr');
          const emailCell = document.createElement('td');
          emailCell.textContent = email;
          const statusCell = document.createElement('td');
          statusCell.textContent = healthy ? "Healthy" : "Unhealthy";
          row.appendChild(emailCell);
          row.appendChild(statusCell);
          healthRows.push(row);
        }

        // Variables for health results pagination
        let currentHealthPage = 1;
        const healthPaginationDiv = document.getElementById('healthPagination');

        function renderHealthTable() {
          healthTableBody.innerHTML = '';
          const totalHealthRows = healthRows.length;
          const totalHealthPages = Math.ceil(totalHealthRows / rowsPerPage);
          if (currentHealthPage > totalHealthPages) currentHealthPage = totalHealthPages;
          if (currentHealthPage < 1) currentHealthPage = 1;

          const startIndex = (currentHealthPage - 1) * rowsPerPage;
          const endIndex = startIndex + rowsPerPage;
          const rowsToDisplay = healthRows.slice(startIndex, endIndex);

          rowsToDisplay.forEach(row => {
            healthTableBody.appendChild(row);
          });
          renderHealthPagination(totalHealthPages);
        }

        function renderHealthPagination(totalHealthPages) {
          healthPaginationDiv.innerHTML = '';
          if (totalHealthPages <= 1) return;

          // Previous button for health results
          const prevButton = document.createElement('button');
          prevButton.textContent = 'Previous';
          prevButton.disabled = (currentHealthPage === 1);
          prevButton.addEventListener('click', function() {
            if (currentHealthPage > 1) {
              currentHealthPage--;
              renderHealthTable();
            }
          });
          healthPaginationDiv.appendChild(prevButton);

          // Numbered buttons for health results pages
          for (let i = 1; i <= totalHealthPages; i++) {
            const pageButton = document.createElement('button');
            pageButton.textContent = i;
            if (i === currentHealthPage) {
              pageButton.disabled = true;
            }
            pageButton.addEventListener('click', function() {
              currentHealthPage = i;
              renderHealthTable();
            });
            healthPaginationDiv.appendChild(pageButton);
          }

          // Next button for health results
          const nextButton = document.createElement('button');
          nextButton.textContent = 'Next';
          nextButton.disabled = (currentHealthPage === totalHealthPages);
          nextButton.addEventListener('click', function() {
            if (currentHealthPage < totalHealthPages) {
              currentHealthPage++;
              renderHealthTable();
            }
          });
          healthPaginationDiv.appendChild(nextButton);
        }

        // Render the health results table with pagination
        renderHealthTable();

        // Ensure the health results container is visible
        document.getElementById('healthResultsContainer').style.display = 'block';
      } catch (error) {
        console.error("Error fetching health data:", error);
      }
    }
  </script>
</body>
</html>
