from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
import sentry_sdk
from fastapi import FastAP<PERSON>, status
from fastapi.middleware.cors import CORSMiddleware
from starlette.responses import Response
from starlette_exporter import PrometheusMiddleware
from tortoise.contrib.fastapi import register_tortoise
from fastapi.exception_handlers import http_exception_handler

from ciba_iot_etl.common.db import get_tortoise_orm_config
from app.log.logging import logger
from app.routers import (
    api,
    dexcom,
    fitbit,
    measures,
    system,
    user_data,
    withings,
    manager,
    transtek,
)
from app.settings import get_settings, ENV
from asgi_correlation_id import CorrelationIdMiddleware, correlation_id
from fastapi import HTTPException, Request
from json import JSONDecodeError, dumps
import pendulum

from ddtrace import patch_all

patch_all()

settings = get_settings()

app = FastAPI(
    title=settings.PROJECT_NAME,
    debug=settings.DEBUG,
    version=settings.VERSION,
)
app.add_middleware(
    CorrelationIdMiddleware,
    header_name="X-Request-ID",
    update_request_header=True,
    generator=lambda: str(pendulum.now().int_timestamp),
    transformer=lambda correlation_id: correlation_id,
    validator=None,
)

logger.info("APP initialized.")

# Only initialize Sentry in production environment
if settings.ENV == ENV.PROD:
    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        environment=settings.ENV,
        traces_sample_rate=0.5,
        profiles_sample_rate=0.5,
        enable_tracing=True,
        release=settings.VERSION,
        auto_session_tracking=True,
    )

# app.add_middleware(
#     CORSMiddleware,
#     allow_headers=settings.ALLOW_HEADERS.split(","),
#     allow_origins=settings.ALLOW_ORIGINS.split(","),
#     allow_methods=settings.ALLOW_METHODS.split(","),
#     allow_credentials=True,
# )
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["X-Request-ID"],
)
app.add_middleware(PrometheusMiddleware)

# to enable custom json logging we need to investigate why errors in
# background jobs are not propagated and are not shown

# app.middleware("http")(LoggingMiddleware())

# Include routers
app.include_router(system.router)
app.include_router(withings.router)
app.include_router(fitbit.router)
app.include_router(dexcom.router)
app.include_router(api.router)
app.include_router(user_data.router)
app.include_router(measures.router)
app.include_router(transtek.router)

if settings.ENV == ENV.DEV:
    app.include_router(manager.router)
    app.include_router(manager.ui_router)


register_tortoise(
    app,
    config=get_tortoise_orm_config(settings.default_db_url),
    add_exception_handlers=True,
)


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(
    request: Request, exc: RequestValidationError
):
    if "transtek" in str(request.url):
        body = await request.body()

        if body is not None and body != b"":
            try:
                json_body = await request.json()
                body = dumps(
                    json_body,
                    indent=4,
                    sort_keys=True,
                )
            except JSONDecodeError:
                json_body = body
        else:
            json_body = body

        message = {"message": f"Unrecognized request body: {json_body}"}
        logger.error(message)

        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=message,
        )


@app.exception_handler(Exception)
async def unhandled_exception_handler(
    request: Request, exc: Exception
) -> Response:
    return await http_exception_handler(
        request,
        HTTPException(
            500,
            "Internal server error",
            headers={"X-Request-ID": correlation_id.get() or ""},
        ),
    )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
