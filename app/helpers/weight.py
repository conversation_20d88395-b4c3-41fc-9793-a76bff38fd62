from typing import Optional
from uuid import UUID

from ciba_iot_etl.models.db.weight import Weight
from ciba_iot_etl.models.pydantic.common import ActivityDevice

from app.helpers.measures import get_vendor_equivalence


async def get_last_sync_by_vendor(
    member_id: UUID, device_vendor: ActivityDevice
) -> Optional[int]:
    """
    Function to get the most recent weight measure.

    Args:
        member_id (UUID): Member ID
        device_vendor (ActivityDevice): Device Vendor
    Returns:
        Optional[int]: Unix timestamp of the most recent weight measure or None
        when no measure was found
    """
    source_device = get_vendor_equivalence(device_vendor)

    latest_sync = (
        await Weight.filter(
            member_id=member_id,
            device=source_device,
        )
        .order_by("-created_at")
        .first()
    )

    return int(latest_sync.created_at.timestamp()) if latest_sync else None
