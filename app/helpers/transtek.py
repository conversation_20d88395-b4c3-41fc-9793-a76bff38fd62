from typing import Dict, Any, Optional

import pendulum
from fastapi import (
    HTTPException,
)
from tortoise.queryset import Q

from app.log.logging import logger
from app.routers.requests.api_request import (
    PairDeviceRequest,
    GetDeviceRequest,
)
from app.services.queue import Queue

from ciba_iot_etl.extract.transtek_api.core import MioConnectClient
from ciba_iot_etl.helpers.measurement import DeviceType
from ciba_iot_etl.models.pydantic.devices import MemberDeviceData
from ciba_iot_etl.models.pydantic.transtek import MODEL_NUMBER_TO_TYPE
from ciba_iot_etl.models.db.member import Member
from ciba_iot_etl.repositories.devices_repository import (
    MemberDevicesRepository,
)

from ciba_iot_etl.extract.transtek_api.common import MioConnectError
from ciba_iot_etl.models.pydantic.transtek import (
    TranstekStatusMessage,
    TranstekTelemetryMessage,
)
from ciba_iot_etl.models.db.transtek import Transtek, TranstekStatus
from ciba_iot_etl.models.pydantic.common import (
    ActivityDevice,
    PullDataNotification,
)
from ciba_iot_etl.models.db.member_platform import MemberPlatform


async def get_member_by_request(request_data: PairDeviceRequest) -> Member:
    member, created = await Member.get_or_create(email=request_data.email)
    if created:
        await MemberPlatform.get_or_create(
            platform_type=request_data.member_type.value,
            platform_id=request_data.member_id,
            member=member,
        )
        logger.info("member for platform created, id: %s", member.id)
    else:
        logger.info(f"Found member: {member.id}")

    return member


async def get_device_info(
    request_data: GetDeviceRequest | PairDeviceRequest,
    client: MioConnectClient,
) -> Dict[str, Any]:
    try:
        if request_data.imei:
            logger.info(f"Retrieving device by IMEI: {request_data.imei}")
            device = await client.get_device_by_imei(request_data.imei)
        elif request_data.serial_number:
            logger.info(
                f"Retrieving device by serial number: {request_data.serial_number}"
            )
            device = await client.get_device(request_data.serial_number)
        else:
            raise HTTPException(
                status_code=400,
                detail="Either IMEI or serial number must be provided",
            )

        if not device or "serialNumber" not in device:
            raise HTTPException(
                status_code=404,
                detail="Device not found or invalid device data",
            )

        logger.info(f"Retrieved device: {device.get('serialNumber')}")
        return device

    except MioConnectError as e:
        logger.error(f"Failed to retrieve device: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Failed to retrieve device information: {str(e)}",
        )


async def activate_device(device_id: str, client: MioConnectClient) -> None:
    try:
        logger.info(f"Activating device: {device_id}")
        await client.activate_device(device_id=device_id)
        logger.info(f"Device activated successfully: {device_id}")
    except MioConnectError as e:
        logger.error(f"Device activation failed for {device_id}: {e}")
        raise HTTPException(
            status_code=400, detail=f"Device activation failed: {str(e)}"
        )


async def save_device_pairing(member: Member, device: Dict[str, Any]) -> None:
    try:
        device_model = device.get("modelNumber", "Unknown")
        device_type = (
            MODEL_NUMBER_TO_TYPE[device_model]
            if "modelNumber" in device
            else "Unknown"
        )
        device_data = MemberDeviceData(
            last_synced_at=pendulum.now(),
            external_id=device["serialNumber"],
            device_type=device_type,
            vendor=DeviceType.TRANSTEK,
        )
        await MemberDevicesRepository.add_device(
            member_id=member.id, device_data=device_data
        )

        logger.info(
            f"Device pairing saved: member_id={member.id}, "
            f"device_id={device['serialNumber']}"
        )

    except Exception as e:
        logger.error(f"Failed to save device pairing: {e}")
        raise HTTPException(
            status_code=500, detail="Failed to save device pairing to database"
        )


async def update_device_status(message: TranstekStatusMessage):
    device = (
        await Transtek.filter(device_id=message.deviceId)
        .first()
        .prefetch_related("member")
    )

    if device is None or device.member is None:
        return

    if device.status in [TranstekStatus.PAIRED, TranstekStatus.SHIPPED]:
        device.status = TranstekStatus.ACTIVE

    device.last_status_report = message.model_dump_json()
    device.updated_at = pendulum.now()

    await device.save()


async def update_device_telemetry(
    message: TranstekTelemetryMessage, queue: Queue, correlation_id: str
):
    device = (
        await Transtek.filter(device_id=message.deviceId)
        .first()
        .prefetch_related("member", "member__platforms")
    )

    if device is None or device.member is None:
        logger.warning(
            f"Unpaired device with ID {message.deviceId} has sent telemetry: \n{message.model_dump_json()}"
        )
        return

    member: Member = device.member
    platforms = await member.get_platforms()

    notification = PullDataNotification(
        member_id=str(member.id),
        activity_device=ActivityDevice.TRANSTEK,
        platforms=platforms,
        payload=message,
        corellation_id=correlation_id,
    )

    queue_message = notification.model_dump_json()
    await queue.send(queue_message)


async def check_existing_transtek_device(
    imei: Optional[str] = None, device_id: Optional[str] = None
) -> bool:
    """
    Check if a device with the given IMEI or device ID already exists in the database.
    """
    existing_device = await Transtek.filter(
        (Q(imei=imei) | Q(device_id=device_id))
    ).first()
    if existing_device:
        return True

    return False
