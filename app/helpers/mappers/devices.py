import pendulum

from app.pydantic_model.measures_api import Device
from ciba_iot_etl.models.db.transtek import Transtek


def map_withings_device(device: dict) -> Device:
    """
    Function to map a Withings device element into a Device model.

    Args:
        device (dict): Withings response device element
    Returns:
        Device: Mapped device object
    """
    return Device(
        id=device["deviceid"],
        device_type=device["type"],
        last_synced_at=pendulum.from_timestamp(device["last_session_date"]),
    )


def map_transtek_device(device: Transtek):
    return Device(
        id=device.device_id,
        device_type=device.device_type,
        last_synced_at=device.updated_at,
    )
