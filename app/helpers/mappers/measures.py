from typing import List

import pendulum
from ciba_iot_etl.extract.withings_api.common import MeasureGetMeasGroup
from ciba_iot_etl.helpers.unit_transformation import parse_kg_to_lb
from ciba_iot_etl.models.db.weight import Weight
from ciba_iot_etl.models.db.blood_pressure import BloodPressure

from app.pydantic_model.measures_api import Measure

WEIGHT_DECIMAL_POSITIONS = 1
WEIGHT_UNIT = "lbs"
SYS_PRESSURE_UNIT = "mmHg (systolic)"
DIA_PRESSURE_UNIT = "mmHg (diastolic)"


def map_withings_weight_group(
    measure_group: MeasureGetMeasGroup,
) -> List[Measure]:
    """
    Function to map withings weight group into a list of measures.

    Args:
        measure_group (MeasureGetMeasGroup): Withings API response weight group
    Returns:
        List[Measure]: Mapped weight measures
    """
    return [
        Measure(
            created_at=pendulum.from_timestamp(measure_group.created),
            unit=WEIGHT_UNIT,
            value=float(
                parse_kg_to_lb(
                    measure.value * pow(10, measure.unit),
                    WEIGHT_DECIMAL_POSITIONS,
                )
            ),
        )
        for measure in measure_group.measures
    ]


def map_transtek_weight(weight: Weight) -> Measure:
    return Measure(
        created_at=weight.created_at, unit=WEIGHT_UNIT, value=weight.value
    )


def map_transtek_blood_pressure(pressure: BloodPressure) -> list[Measure]:
    sys = Measure(
        created_at=pressure.created_at,
        unit=SYS_PRESSURE_UNIT,
        value=pressure.systolic_value,
    )
    dia = Measure(
        created_at=pressure.created_at,
        unit=DIA_PRESSURE_UNIT,
        value=pressure.diastolic_value,
    )
    return [sys, dia]
