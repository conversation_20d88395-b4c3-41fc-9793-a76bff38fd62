from itertools import groupby

from ciba_iot_etl.helpers.measurement import get_date_from_measure

from app.pydantic_model.measures_api import (
    GlucoseEventType,
    GlucoseLevelEventEntry,
    GroupOption,
)

HYPOGLYCEMIA_LIMIT = 70
HYPERGLYCEMIA_LIMIT = 180


def get_glucose_events_count(measures: list[dict]) -> (int, int):
    """
    Method to get the hyperglycemia and hypoglycemia events count
    """
    hyperglycemia_count = 0
    hypoglycemia_count = 0

    for measure in measures:
        if measure["value"] <= HYPOGLYCEMIA_LIMIT:
            hypoglycemia_count += 1
        if measure["value"] >= HYPERGLYCEMIA_LIMIT:
            hyperglycemia_count += 1

    return hyperglycemia_count, hypoglycemia_count


def get_glucose_level_events(
    measures: list[dict],
) -> list[GlucoseLevelEventEntry]:
    """
    Method to get a list of glucose level critical events
    """
    if not measures:
        return []

    events = []

    # TODO: update when the limits for very high and very low are defined
    for measure in measures:
        if measure["value"] <= HYPOGLYCEMIA_LIMIT:
            events.append(
                GlucoseLevelEventEntry(
                    createdAt=get_date_from_measure(measure),
                    value=measure["value"],
                    type=GlucoseEventType.LOW,
                )
            )
        if measure["value"] >= HYPERGLYCEMIA_LIMIT:
            events.append(
                GlucoseLevelEventEntry(
                    createdAt=get_date_from_measure(measure),
                    value=measure["value"],
                    type=GlucoseEventType.HIGH,
                )
            )

    return events


def group_measures(
    measures: list[dict], group_by: GroupOption = GroupOption.DAY
) -> list[dict]:
    """
    Method to group the measures by the group_by option
    """
    if not measures:
        return []

    adjustment_values = {"minute": 0, "second": 0, "microsecond": 0}

    if group_by == GroupOption.DAY:
        adjustment_values.update({"hour": 0})

    grouped_measures = {
        hour: list(values)
        for hour, values in groupby(
            measures,
            key=lambda item: item["created_at"].replace(**adjustment_values),
        )
    }

    parsed_measures = []

    for key, value in grouped_measures.items():
        group_values = [measure["value"] for measure in value]
        parsed_measures.append(
            {
                "created_at": key,
                "value": round(sum(group_values) / len(group_values)),
            }
        )

    return parsed_measures
