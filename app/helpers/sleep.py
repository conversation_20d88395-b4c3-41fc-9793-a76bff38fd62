from datetime import datetime
from typing import Optional

import pendulum
from ciba_iot_etl.models.db.sleep import Sleep

from app.pydantic_model.measures_api import (
    Sleep as SleepDataModel,
    SleepSummary,
    SleepScoreTable,
)


def convert_to_sleep_day(
    measures: list[Sleep], days_in_range: list[str]
) -> (list[SleepDataModel], SleepSummary):
    """
    Convert a list of sleep measures to a list of SleepDataModel.
    """
    sleep_days = fill_sleep_days(days_in_range)

    for measure in measures:
        sleep_score_table = calculate_sleep_score(
            actual_sleep_duration=measure.duration / 60,
            target_sleep_duration=8,
            time_in_deep=measure.deep_time / 60,
            time_in_rem=measure.rem_time / 60,
            total_sleep_time=measure.asleep_time / 60,
            restfulness_factor=measure.efficiency / 100,
        )

        sleep_day = SleepDataModel(
            sleep_date=measure.start_time.date().isoformat(),
            start_time=measure.start_time.time().isoformat(),
            end_time=measure.end_time.time().isoformat(),
            asleep_time=measure.asleep_time,
            duration=measure.duration,
            efficiency=measure.efficiency if measure.efficiency else 0,
            sleep_score_table=sleep_score_table,
            bed_time=measure.bed_time,
            deep_time=measure.deep_time,
            light_time=measure.light_time,
            rem_time=measure.rem_time,
            wake_time=measure.wake_time,
            start_time_in_bed=measure.start_time,
            end_time_in_bed=pendulum.instance(measure.start_time).add(
                minutes=measure.bed_time
            ),
        )
        update_day_measure(
            sleep_days, measure.end_time.date().strftime("%Y-%m-%d"), sleep_day
        )

    today = get_summary(
        sleep_days,
        pendulum.now(),
        measures[-1].created_at if measures else None,
    )

    return list(sleep_days.values()), today


def fill_sleep_days(days_in_range: list[str]) -> dict[str, SleepDataModel]:
    """
    Fill sleep days structure with default values for all the dates in the requested range.
    """
    sleep_score_table = SleepScoreTable()
    return {
        day: SleepDataModel(
            sleep_date=day,
            start_time="",
            end_time="",
            asleep_time=0,
            duration=0,
            efficiency=0,
            bed_time=0,
            deep_time=0,
            sleep_score_table=sleep_score_table,
            light_time=0,
            rem_time=0,
            wake_time=0,
            start_time_in_bed=pendulum.parse(day),
            end_time_in_bed=pendulum.parse(day),
        )
        for day in days_in_range
    }


def update_day_measure(
    sleep_days: dict[str, SleepDataModel],
    day_key: str,
    new_measure: SleepDataModel,
):
    """
    Update the sleep day values based on the additional day measures.
    """
    if day_key not in sleep_days or not sleep_days[day_key].start_time:
        sleep_days[day_key] = new_measure
        return

    if sleep_days[day_key].duration < new_measure.duration:
        sleep_days[day_key].start_time = new_measure.start_time
        sleep_days[day_key].end_time = new_measure.end_time
        sleep_days[day_key].start_time_in_bed = new_measure.start_time_in_bed
        sleep_days[day_key].end_time_in_bed = new_measure.end_time_in_bed
        sleep_days[day_key].efficiency = new_measure.efficiency

    sleep_days[day_key].asleep_time += new_measure.asleep_time
    sleep_days[day_key].duration += new_measure.duration
    sleep_days[day_key].bed_time += new_measure.bed_time
    sleep_days[day_key].deep_time += new_measure.deep_time
    sleep_days[day_key].light_time += new_measure.light_time
    sleep_days[day_key].rem_time += new_measure.rem_time
    sleep_days[day_key].wake_time += new_measure.wake_time


def get_summary(
    sleep_days: dict[str, SleepDataModel],
    requested_day: datetime,
    last_update: Optional[datetime],
) -> SleepSummary | None:
    """
    Get the most recent sleep summary from sleep days
    """
    if not sleep_days or not last_update:
        return None

    day_key = requested_day.strftime("%Y-%m-%d")
    if day_key in sleep_days:
        sleep_day = sleep_days[day_key]
        sleep_score = calculate_sleep_score(
            actual_sleep_duration=sleep_day.duration / 60,
            target_sleep_duration=8,
            time_in_deep=sleep_day.deep_time / 60,
            time_in_rem=sleep_day.rem_time / 60,
            total_sleep_time=sleep_day.asleep_time / 60,
            restfulness_factor=sleep_day.efficiency / 100,
        )
        return SleepSummary(
            date=requested_day.date(),
            duration=sleep_day.duration,
            asleep_time=sleep_day.asleep_time,
            lastUpdate=last_update,
            timelineStart=sleep_day.start_time_in_bed,
            timelineEnd=sleep_day.end_time_in_bed,
            sleep_score=sleep_score.sleep_score,
        )

    return SleepSummary(
        date=requested_day.date(),
        duration=0,
        asleep_time=0,
        lastUpdate=last_update,
        sleep_score=0,
    )


def calculate_sleep_score(
    actual_sleep_duration,
    target_sleep_duration,
    time_in_deep,
    time_in_rem,
    total_sleep_time,
    restfulness_factor,
) -> SleepScoreTable:
    """
    Calculate an approximate Fitbit Sleep Score.

    Parameters:
    - actual_sleep_duration (float): Actual sleep duration in hours.
    - target_sleep_duration (float): Target sleep duration in hours.
    - time_in_deep (float): Time spent in deep sleep in hours.
    - time_in_rem (float): Time spent in REM sleep in hours.
    - total_sleep_time (float): Total sleep time in hours.
    - restfulness_factor (float): A value between 0 and 1 representing restfulness (1 = very restful, 0 = very restless).

    Returns:
    - sleep_score (SleepScoreTable): Approximate sleep qualities scores out of 100.
    """

    score_table = SleepScoreTable()

    # Calculate Duration Score (max 50 points)
    if not target_sleep_duration:
        duration_score = 0
    else:
        duration_score = (
            actual_sleep_duration / target_sleep_duration
        ) * score_table.duration_max
        duration_score = min(
            duration_score, score_table.duration_max
        )  # Cap at 50 points
    score_table.duration_score = round(duration_score)

    # Calculate Quality Score (max 25 points)
    if not total_sleep_time:
        quality_score = 0
    else:
        quality_score = (
            (time_in_deep + time_in_rem) / total_sleep_time
        ) * score_table.quality_max
        quality_score = min(
            quality_score, score_table.quality_max
        )  # Cap at 25 points
    score_table.quality_score = round(quality_score)

    # Calculate Restoration Score (max 25 points)
    restoration_score = restfulness_factor * score_table.restoration_max
    restoration_score = min(
        restoration_score, score_table.restoration_max
    )  # Cap at 25 points
    score_table.restoration_score = round(restoration_score)

    # Total Sleep Score
    sleep_score = duration_score + quality_score + restoration_score
    score_table.sleep_score = round(sleep_score)

    return score_table
