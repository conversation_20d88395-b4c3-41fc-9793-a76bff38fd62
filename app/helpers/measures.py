from ciba_iot_etl.helpers.measurement import DeviceType
from ciba_iot_etl.models.pydantic.common import ActivityDevice

from app.common.messages import DEVICE_VENDOR_NOT_SUPPORTED

vendor_mapping = {
    ActivityDevice.FITBIT: DeviceType.FITBIT,
    ActivityDevice.DEXCOM: DeviceType.DEXCOM,
    ActivityDevice.WITHINGS: DeviceType.WITHINGS,
}


def get_vendor_equivalence(device_vendor: ActivityDevice) -> DeviceType:
    """
    Function to get the vendor equivalence.

    Args:
        device_vendor: Device vendor
    Returns:
        Device vendor numeric equivalence
    """
    if device_vendor in vendor_mapping:
        return vendor_mapping[device_vendor]

    raise ValueError(DEVICE_VENDOR_NOT_SUPPORTED)
