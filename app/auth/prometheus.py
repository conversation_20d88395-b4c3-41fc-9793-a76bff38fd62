import secrets

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>asic, HTTPBasicCredentials

from app.settings import get_settings

settings = get_settings()
security = HTTPBasic()


def authorize(credentials: HTTPBasicCredentials = Depends(security)) -> None:
    """
    HTTPBasic authorizer for Prometheus
    """
    is_user_ok = secrets.compare_digest(
        credentials.username,
        settings.METRICS_USERNAME,
    )
    is_pass_ok = secrets.compare_digest(
        credentials.password, settings.METRICS_PASSWORD
    )

    if not (is_user_ok and is_pass_ok):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password.",
            headers={"WWW-Authenticate": "Basic"},
        )
