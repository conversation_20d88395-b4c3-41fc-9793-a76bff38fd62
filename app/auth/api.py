from fastapi import HTTPException, Request

from app.auth.constants import X_AUTH_KEY
from app.settings import get_settings

settings = get_settings()


def verify_token(req: Request) -> bool:
    """
    Verify api token for service to service communication
    """
    token = req.headers.get(X_AUTH_KEY)

    if token not in settings.API_KEYS.split(","):
        raise HTTPException(status_code=401, detail="Unauthorized")

    return True
