from enum import Enum
from importlib import import_module
from typing import Any, Dict


class AvailableIntegration(str, Enum):
    WITHINGS = "Withings"
    ORURARING = "Oruraring"


class IntegrationModelSelector:
    import_path: str = "app.models"

    def __init__(self, integration: AvailableIntegration) -> None:
        self.integration = integration
        self.model = self.load_model(self.integration.value)

    def load_model(self, model_name: str) -> Any:
        """
        Dynamically load the integration module and return model class instance
        """
        try:
            class_name = f"{self.import_path}.{model_name.lower()}"
            module = import_module(class_name)
            model = getattr(module, str(model_name))
            return model
        except (ImportError, AttributeError) as error:
            raise ImportError(class_name) from error

    async def get_inegration_tokens(self) -> Dict[Any, Any]:
        """
        Load integration user tokens
        """
        cursor = await self.model.all()
        return cursor
