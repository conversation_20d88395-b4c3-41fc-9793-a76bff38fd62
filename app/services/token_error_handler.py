"""
Standardized error handling utilities for token operations.

DEPRECATED: This module is being phased out in favor of ciba-iot-etl standardized utilities.
New code should use:
- ciba_iot_etl.extract.common.token_refresh.refresh_fitbit_token_with_fallback()
- ciba_iot_etl.extract.common.token_refresh.refresh_withings_token_with_fallback()
- ciba_iot_etl.monitoring.token_metrics.log_token_refresh_metrics()
- ciba_iot_etl.models.pydantic.token_management.TokenRefreshResult

This module is kept for backward compatibility with manager.py router.
"""

import logging
from typing import Optional, Dict, Any
from ciba_iot_etl.models.pydantic.token_management import (
    TokenRefreshResult,
    TokenErrorType,
)


# For backward compatibility, create a FallbackAttemptResult alias
class FallbackAttemptResult:
    def __init__(
        self,
        attempted: bool,
        success: bool,
        reason: Optional[str] = None,
        error: Optional[str] = None,
    ):
        self.attempted = attempted
        self.success = success
        self.reason = reason
        self.error = error


logger = logging.getLogger(__name__)


class TokenErrorHandler:
    """Centralized error handling for token operations."""

    @staticmethod
    def classify_error(error_message: str) -> TokenErrorType:
        """Classify an error message into a TokenErrorType."""
        error_lower = error_message.lower()

        if "invalid_grant" in error_lower or "invalid_token" in error_lower:
            return TokenErrorType.INVALID_GRANT
        elif "expired" in error_lower:
            return TokenErrorType.EXPIRED_TOKEN
        elif (
            "network" in error_lower
            or "timeout" in error_lower
            or "connection" in error_lower
        ):
            return TokenErrorType.NETWORK_ERROR
        elif "rate limit" in error_lower or "too many requests" in error_lower:
            return TokenErrorType.API_ERROR
        elif "validation" in error_lower or "invalid response" in error_lower:
            return TokenErrorType.VALIDATION_ERROR
        else:
            return (
                TokenErrorType.API_ERROR
            )  # Use API_ERROR instead of UNKNOWN_ERROR

    @staticmethod
    def requires_reauth(error_type: TokenErrorType) -> bool:
        """Determine if an error type requires re-authentication."""
        return error_type in [
            TokenErrorType.INVALID_GRANT,
            TokenErrorType.EXPIRED_TOKEN,
        ]

    @staticmethod
    def should_attempt_fallback(
        error_message: str, has_old_token: bool, old_token_expired: bool
    ) -> FallbackAttemptResult:
        """Determine if fallback should be attempted."""
        error_type = TokenErrorHandler.classify_error(error_message)

        if error_type != TokenErrorType.INVALID_GRANT:
            return FallbackAttemptResult(
                attempted=False,
                success=False,
                reason="Error type does not warrant fallback attempt",
            )

        if not has_old_token:
            return FallbackAttemptResult(
                attempted=False,
                success=False,
                reason="No old_refresh_token available",
            )

        if old_token_expired:
            return FallbackAttemptResult(
                attempted=False,
                success=False,
                reason="old_refresh_token has expired",
            )

        return FallbackAttemptResult(
            attempted=True,
            success=True,
            reason="Conditions met for fallback attempt",
        )

    @staticmethod
    def create_error_result(
        platform: str,
        error_message: str,
        used_fallback: bool = False,
        fallback_result: Optional[FallbackAttemptResult] = None,
    ) -> TokenRefreshResult:
        """Create a standardized error result."""
        error_type = TokenErrorHandler.classify_error(error_message)
        requires_reauth = TokenErrorHandler.requires_reauth(error_type)

        # Enhance error message with fallback information
        enhanced_message = error_message
        if (
            fallback_result
            and fallback_result.attempted
            and not fallback_result.success
        ):
            enhanced_message += (
                f" (Fallback attempted but failed: {fallback_result.reason})"
            )

        return TokenRefreshResult(
            success=False,
            error_type=error_type,
            error_message=enhanced_message,
            requires_reauth=requires_reauth,
            used_fallback=used_fallback,
            platform=platform,
        )

    @staticmethod
    def log_token_operation(
        operation: str,
        platform: str,
        connection_id: str,
        success: bool,
        error: Optional[str] = None,
        used_fallback: bool = False,
        additional_context: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Standardized logging for token operations."""
        context = {
            "operation": operation,
            "platform": platform,
            "connection_id": connection_id,
            "success": success,
            "used_fallback": used_fallback,
        }

        if additional_context:
            context.update(additional_context)

        if success:
            if used_fallback:
                logger.info(
                    f"Token {operation} successful using fallback for {platform} {connection_id}",
                    extra=context,
                )
            else:
                logger.info(
                    f"Token {operation} successful for {platform} {connection_id}",
                    extra=context,
                )
        else:
            logger.error(
                f"Token {operation} failed for {platform} {connection_id}: {error}",
                extra=context,
            )

    @staticmethod
    def log_fallback_attempt(
        platform: str,
        connection_id: str,
        attempt_result: FallbackAttemptResult,
        success: bool = False,
        error: Optional[str] = None,
    ) -> None:
        """Log fallback attempt details."""
        context = {
            "platform": platform,
            "connection_id": connection_id,
            "fallback_attempted": attempt_result.attempted,
            "fallback_success": success,
        }

        if attempt_result.attempted:
            if success:
                logger.info(
                    f"Fallback token refresh successful for {platform} {connection_id}",
                    extra=context,
                )
            else:
                logger.warning(
                    f"Fallback token refresh failed for {platform} {connection_id}: {error}",
                    extra=context,
                )
        else:
            logger.debug(
                f"Fallback not attempted for {platform} {connection_id}: {attempt_result.reason}",
                extra=context,
            )


class RetryHandler:
    """Handle retry logic for token operations."""

    @staticmethod
    def should_retry(
        error_type: TokenErrorType, attempt_count: int, max_retries: int = 3
    ) -> bool:
        """Determine if an operation should be retried."""
        if attempt_count >= max_retries:
            return False

        # Only retry for network errors and API errors
        return error_type in [
            TokenErrorType.NETWORK_ERROR,
            TokenErrorType.API_ERROR,
        ]

    @staticmethod
    def get_retry_delay(attempt_count: int) -> float:
        """Get exponential backoff delay for retry attempts."""
        return min(2**attempt_count, 30)  # Cap at 30 seconds
