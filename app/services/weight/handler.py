import json
from typing import Optional

from pandas import Data<PERSON>rame, read_sql_query, to_datetime

from app.common.db import CONNECTION_STRING, get_db
from app.helpers.temp_context import TempContext
from app.pydantic_model.base import EmailList
from app.sql.user_data import WEIGT


class WeightService:
    """Weight service. Get weitght from database with in range date"""

    table_name = "temp_emails"

    async def handle(
        self,
        emails: EmailList,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ) -> EmailList:
        """
        Get weight from database with in range date for email list
        :param emails: EmailList
        :param start_date: start date
        :param end_date: end date
        :return: EmailList with weight
        """
        email_list = [item.dict() for item in emails.emails]
        original_df = DataFrame(email_list)
        db = next(get_db())

        with TempContext(
            db,
            self.table_name,
            original_df,
        ) as _:
            sql_data = read_sql_query(WEIGT, CONNECTION_STRING)
            merged_df = original_df.merge(
                sql_data, on=["email"], how="left", suffixes=("", "_sql")
            )
        merged_df["value"] = merged_df["value_sql"]
        merged_df["date"] = to_datetime(merged_df["date"], unit="s")
        merged_df.sort_values(
            by=["email", "date"], ascending=[True, False], inplace=True
        )
        unique_df = merged_df.groupby("email").head(1)
        unique_df = unique_df[["email", "value"]]
        json_str = unique_df.to_json(orient="records", date_format="iso")
        return json.loads(json_str)
