import typing

import boto3
from botocore.exceptions import NoCredentialsError, PartialCredentialsError

from app.settings import get_settings

settings = get_settings()

# Create SQS client
_sqs = boto3.client(
    "sqs",
    endpoint_url=settings.SQS_ENDPOINT_URL,
    region_name=settings.SQS_REGION,
)


class SendQueueException(Exception):
    """Exception raised for errors in the send to queue."""

    def __init__(self, error: str, message: str = "Send message failed"):
        self.field = error
        self.message = f"{message}: {error}"
        super().__init__(self.message)


class Queue:
    """Queue service class"""

    def __init__(self, client: typing.Any, queue: str) -> None:
        self.client = client
        self.queue = queue

    async def send(self, message: str) -> typing.Any:
        """Send message to setuped queue"""
        try:
            response = self.client.send_message(
                QueueUrl=self.queue, MessageBody=message
            )

            return response
        except NoCredentialsError as exc:
            raise SendQueueException("Credentials not available") from exc
        except PartialCredentialsError as exc:
            raise SendQueueException(
                "Incomplete credentials provided"
            ) from exc
        except Exception as exc:
            raise SendQueueException(f"An error occurred: {exc}") from exc


def get_queue(queue: str) -> Queue:
    """Return queu service"""
    return Queue(_sqs, queue)
