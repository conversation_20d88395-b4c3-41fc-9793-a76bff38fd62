from datetime import date, timed<PERSON>ta
from typing import Optional
from uuid import UUID

from tortoise.expressions import RawSQL
from tortoise.queryset import QuerySet

from ciba_iot_etl.helpers.unit_transformation import parse_kg_to_lb
from ciba_iot_etl.models.db.weight import Weight

from ciba_iot_etl.helpers.measurement import get_date_from_measure
from app.pydantic_model.measures_api import (
    GroupOption,
    MeasuresResponse,
    MeasureType,
    WeightEntry,
    WeightSummary,
    WeightUnit,
)


def get_base_query(
    member_id: UUID, start_date: date, end_date: date
) -> QuerySet:
    return Weight.filter(
        member_id=member_id,
        created_at__gte=start_date,
        created_at__lt=end_date + timedelta(days=1),
    )


def parse_measures_to_lbs(measures: list[dict]):
    """
    Method to parse a list of weight measures
    form kilograms to pounds
    """
    for measure in measures:
        measure["value"] = parse_kg_to_lb(measure["value"])


class WeightService:
    @staticmethod
    async def get_dashboard_summary(
        member_id: UUID,
        start_date: date,
        end_date: date,
        unit: WeightUnit = WeightUnit.KG,
    ) -> Optional[WeightSummary]:
        """
        Method to get the weight dashboard summary.
        """
        base_query = get_base_query(member_id, start_date, end_date)
        measures = await WeightService.get_measures(base_query)

        if measures and unit == WeightUnit.LB:
            parse_measures_to_lbs(measures)

        return WeightService.get_summary(measures)

    @staticmethod
    def get_summary(measures: list[dict]) -> Optional[WeightSummary]:
        """
        Method to calculate the weight summary.
        """
        if not measures:
            return None

        weight_change = measures[-1]["value"] - measures[0]["value"]

        return WeightSummary(
            lastUpdate=get_date_from_measure(measures[-1]),
            currentWeight=measures[-1]["value"],
            weightChange=weight_change,
            averageWeightChange=round(weight_change / len(measures), 2),
        )

    @staticmethod
    async def get_measures(base_query: QuerySet) -> QuerySet[Weight]:
        """
        Method to retrieve weight measures in plain form.
        """
        return await base_query.order_by("created_at").values(
            "created_at", "value"
        )

    @staticmethod
    async def get_grouped_measures(
        base_query: QuerySet, group_by: GroupOption
    ):
        """
        Method to retrieve weight measures in grouped form.
        """
        query = (
            base_query.annotate(
                group_date=RawSQL(
                    f"DATE_TRUNC('{group_by.value}', created_at)"
                ),
                value=RawSQL("ROUND(AVG(value), 2)"),
            )
            .group_by("group_date")
            .order_by("group_date")
        )

        return await query.values("value", "group_date")

    @staticmethod
    async def get_measures_data(
        member_id: UUID,
        start_date: date,
        end_date: date,
        unit: WeightUnit = WeightUnit.KG,
        group_by: GroupOption = None,
    ):
        """
        Method to get weight data in the provided date range.
        """
        base_query = get_base_query(member_id, start_date, end_date)

        measures = (
            await WeightService.get_grouped_measures(base_query, group_by)
            if group_by
            else await WeightService.get_measures(base_query)
        )

        if measures and unit == WeightUnit.LB:
            parse_measures_to_lbs(measures)

        return MeasuresResponse(
            type=MeasureType.WEIGHT,
            unit=unit.value,
            summary=WeightService.get_summary(measures),
            values=(
                [
                    WeightEntry(
                        createdAt=get_date_from_measure(measure),
                        value=measure["value"],
                    )
                    for measure in measures
                ]
                if measures
                else []
            ),
        )
