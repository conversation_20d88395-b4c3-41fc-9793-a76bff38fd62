from datetime import date, timed<PERSON>ta
from typing import Optional
from uuid import UUID

from tortoise.expressions import RawSQL
from tortoise.queryset import QuerySet

from ciba_iot_etl.models.db.blood_pressure import BloodPressure

from ciba_iot_etl.helpers.measurement import get_date_from_measure
from app.pydantic_model.measures_api import (
    BloodPressureEntry,
    BloodPressureSummary,
    BloodPressureSummaryEntry,
    GroupOption,
    MeasuresResponse,
    MeasureType,
)

UNIT = "mmHg"


def get_base_query(member_id: UUID, start_date: date, end_date: date):
    """
    Method to get the Blood Pressure base query
    """
    return BloodPressure.filter(
        member_id=member_id,
        created_at__gte=start_date,
        created_at__lt=end_date + timedelta(days=1),
    )


class BloodPressureService:
    @staticmethod
    async def get_dashboard_summary(
        member_id: UUID, start_date: date, end_date: date
    ) -> Optional[BloodPressureSummary]:
        """
        Method to get the blood pressure dashboard summary
        """
        base_query = get_base_query(member_id, start_date, end_date)
        measures = await BloodPressureService.get_measures(base_query)

        return BloodPressureService.get_summary(measures)

    @staticmethod
    def get_summary(
        measures: list[dict],
    ) -> Optional[BloodPressureSummary]:
        """
        Method to calculate the blood pressure summary.
        """
        if not measures:
            return None

        systolic_values = [measure["systolic_value"] for measure in measures]
        diastolic_values = [measure["diastolic_value"] for measure in measures]

        return BloodPressureSummary(
            lastUpdate=get_date_from_measure(measures[-1]),
            systolic=BloodPressureSummaryEntry(
                average=round(sum(systolic_values) / len(measures)),
                highest=max(systolic_values),
                current=systolic_values[-1],
            ),
            diastolic=BloodPressureSummaryEntry(
                average=round(sum(diastolic_values) / len(measures)),
                highest=max(diastolic_values),
                current=diastolic_values[-1],
            ),
        )

    @staticmethod
    async def get_measures(base_query: QuerySet):
        """
        Method to get blood pressure measures in plain form.
        """
        return await base_query.order_by("created_at").values(
            "systolic_value", "diastolic_value", "created_at"
        )

    @staticmethod
    async def get_grouped_measures(
        base_query: QuerySet, group_by: GroupOption
    ):
        """
        Method to get blood pressure measures in grouped form.
        """
        query = (
            base_query.annotate(
                group_date=RawSQL(
                    f"DATE_TRUNC('{group_by.value}', created_at)"
                ),
                systolic_value=RawSQL("ROUND(AVG(systolic_value))"),
                diastolic_value=RawSQL("ROUND(AVG(diastolic_value))"),
            )
            .group_by("group_date")
            .order_by("group_date")
        )

        return await query.values(
            "group_date", "systolic_value", "diastolic_value"
        )

    @staticmethod
    async def get_measures_data(
        member_id: UUID,
        start_date: date,
        end_date: date,
        group_by: GroupOption = None,
    ) -> MeasuresResponse:
        """
        Method to get blood pressure data in the provided date range.
        """
        base_query = get_base_query(member_id, start_date, end_date)

        measures = (
            await BloodPressureService.get_grouped_measures(
                base_query, group_by
            )
            if group_by
            else await BloodPressureService.get_measures(base_query)
        )

        return MeasuresResponse(
            type=MeasureType.BLOOD_PRESSURE,
            unit=UNIT,
            summary=BloodPressureService.get_summary(measures),
            values=(
                [
                    BloodPressureEntry(
                        createdAt=get_date_from_measure(measure),
                        systolic=measure["systolic_value"],
                        diastolic=measure["diastolic_value"],
                    )
                    for measure in measures
                ]
                if measures
                else []
            ),
        )
