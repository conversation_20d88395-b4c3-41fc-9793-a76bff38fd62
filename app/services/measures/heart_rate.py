from datetime import date, timedelta
from typing import Optional
from uuid import UUID

from tortoise.expressions import RawSQL
from tortoise.queryset import QuerySet

from ciba_iot_etl.models.db.heart_rate import HeartRate

from ciba_iot_etl.helpers.measurement import get_date_from_measure
from app.pydantic_model.measures_api import (
    GroupOption,
    HeartRateEntry,
    HeartRateSummary,
    MeasuresResponse,
    MeasureType,
)

UNIT = "bpm"


def get_base_query(member_id: UUID, start_date: date, end_date: date):
    """
    Method to get the heart rate measure base query
    """
    return HeartRate.filter(
        member_id=member_id,
        created_at__gte=start_date,
        created_at__lt=end_date + timedelta(days=1),
    )


class HeartRateService:
    @staticmethod
    async def get_dashboard_summary(
        member_id: UUID, start_date: date, end_date: date
    ) -> Optional[HeartRateSummary]:
        """
        Method to get the heart rate dashboard summary
        """
        base_query = get_base_query(member_id, start_date, end_date)
        measures = await HeartRateService.get_measures(base_query)

        return HeartRateService.get_summary(measures)

    @staticmethod
    def get_summary(measures: list[dict]) -> Optional[HeartRateSummary]:
        """
        Method to calculate heart rate summary.
        """
        if not measures:
            return None

        values = [measure["value"] for measure in measures]

        return HeartRateSummary(
            lastUpdate=get_date_from_measure(measures[-1]),
            current=values[-1],
            maxRpm=max(values),
            minRpm=min(values),
            averageRpm=round(sum(values) / len(measures)),
        )

    @staticmethod
    async def get_measures(base_query: QuerySet):
        """
        Method to retrieve heart rate measures from database in plain form.
        """
        return await base_query.order_by("created_at").values(
            "created_at", "value"
        )

    @staticmethod
    async def get_grouped_measures(
        base_query: QuerySet, group_by: GroupOption
    ):
        """
        Method to retrieve heart rate measures from database in grouped form.
        """
        query = (
            base_query.annotate(
                group_date=RawSQL(
                    f"DATE_TRUNC('{group_by.value}', created_at)"
                ),
                value=RawSQL("ROUND(AVG(value))"),
            )
            .group_by("group_date")
            .order_by("group_date")
        )

        return await query.values("group_date", "value")

    @staticmethod
    async def get_measures_data(
        member_id: UUID,
        start_date: date,
        end_date: date,
        group_by: GroupOption = None,
    ) -> MeasuresResponse:
        """
        Method to get heart rate data in the provided date range.
        """
        base_query = get_base_query(member_id, start_date, end_date)

        measures = (
            await HeartRateService.get_grouped_measures(base_query, group_by)
            if group_by
            else await HeartRateService.get_measures(base_query)
        )

        return MeasuresResponse(
            unit=UNIT,
            type=MeasureType.HEART_RATE,
            summary=HeartRateService.get_summary(measures),
            values=(
                [
                    HeartRateEntry(
                        createdAt=get_date_from_measure(measure),
                        value=measure["value"],
                    )
                    for measure in measures
                ]
                if measures
                else []
            ),
        )
