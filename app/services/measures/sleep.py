from datetime import date, timed<PERSON>ta
from typing import Optional
from uuid import UUID

from ciba_iot_etl.models.db.sleep import Sleep
from pydantic_core import ValidationError
from tortoise.queryset import QuerySet

from app.common.exceptions import MeasuresException
from app.helpers.dates import get_dates_range
from app.helpers.sleep import convert_to_sleep_day
from app.pydantic_model.measures_api import (
    Sleep as SleepDataModel,
    MeasuresResponse,
    MeasureType,
    SleepSummary,
)

UNIT = "minutes"


def get_base_query(
    member_id: UUID, start_date: date, end_date: date
) -> QuerySet:
    return Sleep.filter(
        member_id=member_id,
        created_at__gte=start_date,
        created_at__lt=end_date + timedelta(days=1),
    )


class SleepService:
    """
    Service to summarize and consolidate sleep data.
    """

    @staticmethod
    async def get_dashboard_summary(
        member_id: UUID, start_date: date, end_date: date
    ) -> Optional[SleepDataModel]:
        """
        Method to get a dashboard summary for the given date range.
        """
        base_query = get_base_query(member_id, start_date, end_date)

        try:
            _, today = await SleepService.get_measures(
                base_query, get_dates_range(start_date, end_date)
            )
        except ValidationError as error:
            raise MeasuresException(str(error))

        return today

    @staticmethod
    async def get_measures(
        base_query: QuerySet, days_in_range: list[str]
    ) -> (list[SleepDataModel], SleepSummary):
        """
        Method to retrieve weight measures in plain form.
        """
        measures = await base_query.order_by("created_at")

        sleep_days, today = convert_to_sleep_day(measures, days_in_range)

        return sleep_days, today

    @staticmethod
    async def get_measures_data(
        member_id: UUID,
        start_date: date,
        end_date: date,
    ) -> MeasuresResponse:
        """
        Method to retrieve sleep measures for the given date range.
        """
        base_query = get_base_query(member_id, start_date, end_date)

        try:
            measures, today = await SleepService.get_measures(
                base_query, get_dates_range(start_date, end_date)
            )

            return MeasuresResponse(
                type=MeasureType.SLEEP,
                unit=UNIT,
                summary=today,
                values=measures,
            )
        except ValidationError as error:
            raise MeasuresException(str(error))
