# from uuid import UUID
#
# from app.models.ihealth import Ihealth
#
#
# class IhealthService:
#     """Service to add user for apple data"""
#
#     def __init__(self, mail: str) -> None:
#         self.mail = mail
#
#     async def add_user(self) -> UUID:
#         """
#         :return: id of user
#         """
#         ihealth_creds = Ihealth(
#             email=self.mail,
#         )
#         await ihealth_creds.save()
#         return ihealth_creds.id
