from typing import List, Optional
from uuid import UUID

import pendulum
from tortoise.queryset import QuerySet

from app.services.data_fetch.base import DataFetchBase
from app.pydantic_model.measures_api import Device, Measure
from app.helpers.mappers.devices import map_transtek_device
from app.helpers.mappers.measures import (
    map_transtek_weight,
    map_transtek_blood_pressure,
)
from app.services.measures.weight import (
    WeightService,
    get_base_query as get_weight_base_query,
)
from app.services.measures.blood_pressure import (
    BloodPressureService,
    get_base_query as get_blood_base_query,
)

from ciba_iot_etl.models.db.transtek import Transtek, TranstekDeviceType


class TranstekDataFetcher(DataFetchBase):
    """Transtek devices data fetcher"""

    def __init__(self, member_id: UUID, transtek_devices: QuerySet[Transtek]):
        self.member_id = member_id
        self.connection = transtek_devices

    async def get_devices(self) -> List[Device]:
        return [map_transtek_device(device) for device in self.connection]

    async def get_measures(self, start_date: Optional[int]) -> List[Measure]:
        for device in self.connection:
            transtek_device: Transtek = device

            if start_date:
                start_datetime = pendulum.from_timestamp(start_date)
            else:
                start_datetime = pendulum.now()

            if transtek_device.device_type == TranstekDeviceType.SCALE:
                weights = await WeightService.get_measures(
                    get_weight_base_query(
                        member_id=self.member_id,
                        start_date=start_datetime.date(),
                        end_date=pendulum.now().date(),
                    )
                )
                return [map_transtek_weight(w) for w in weights]
            elif transtek_device.device_type == TranstekDeviceType.BPM:
                blood_pressures = await BloodPressureService.get_measures(
                    get_blood_base_query(
                        member_id=self.member_id,
                        start_date=start_datetime.date(),
                        end_date=pendulum.now().date(),
                    )
                )

                measures = []
                for bp in blood_pressures:
                    pressures: List[Measure] = map_transtek_blood_pressure(bp)
                    measures.extend(pressures)

                return measures
