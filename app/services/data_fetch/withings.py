from itertools import chain
from typing import List, Optional
from uuid import UUID

import pendulum
from ciba_iot_etl.extract.withings_api.core import WithingsLoader
from ciba_iot_etl.models.db.withings import Withings
from ciba_iot_etl.models.pydantic.common import ActivityDevice

from app import get_settings
from app.common.messages import (
    REFRESH_TOKEN_FAILED,
    FETCH_DEVICES_FAILED,
    INVALID_FETCH_START_DATE,
    FETCH_MEASURES_FAILED,
)
from app.helpers.mappers.devices import map_withings_device
from app.helpers.mappers.measures import map_withings_weight_group
from app.helpers.weight import get_last_sync_by_vendor
from app.log.logging import logger
from app.pydantic_model.measures_api import Device, Measure
from app.services.data_fetch.base import DataFetchBase

settings = get_settings()


class WithingsDataFetcher(DataFetchBase):
    """Withings devices data fetcher"""

    def __init__(self, member_id: UUID, connection: Withings):
        self.member_id = member_id
        self.connection = connection
        self.client = WithingsLoader(
            client_id=settings.WITHINGS_CLIENT_ID,
            client_secret=settings.WITHINGS_CUSTOMER_SECRET,
            redirect_uri=settings.WITHINGS_REDIRECT_URI,
            callback_uri=settings.WITHINGS_CALLBACK_URI,
            notification_callback_uri=settings.WITHINGS_NOTIFICATION_CALLBACK_URI,
            scope=settings.WITHINGS_SCOPE,
        )

    async def refresh_tokens(self):
        """
        Method to refresh tokens for Withings connections with fallback support.
        Uses standardized ciba-iot-etl token refresh utilities.
        """
        from ciba_iot_etl.extract.common.token_refresh import (
            refresh_withings_token_with_fallback,
        )
        from ciba_iot_etl.monitoring.token_metrics import (
            log_token_refresh_metrics,
        )

        connection_id = str(self.connection.id)

        # Use standardized token refresh from ciba-iot-etl
        result = await refresh_withings_token_with_fallback(
            withings_service=self.client,
            withings_id=connection_id,
            withings_connection=self.connection,
        )

        # Log metrics using standardized monitoring
        log_token_refresh_metrics(
            result=result,
            platform="withings",
            connection_id=connection_id,
            additional_context={"service": "WithingsDataFetcher"},
        )

        if not result.success:
            self.connection.healthy = False
            await self.connection.save()
            raise RuntimeError(REFRESH_TOKEN_FAILED)

        # The standardized function already updated the tokens in the database
        # Refresh our connection object to get the updated tokens
        self.connection = await Withings.get(id=self.connection.id)

    async def get_devices(self) -> List[Device]:
        """
        Method that fetches user devices from Withings API
        using the already configured member connection.

        Returns:
            List[Device]: List of the user's devices.
        """
        if self.connection.is_access_token_expired():
            await self.refresh_tokens()

        response = await self.client.get_user_devices(
            self.connection.access_token
        )

        if response.get("status") != 0:
            logger.error(
                f"Devices fetch for {self.member_id} failed: {response.get('error')}"
            )
            raise RuntimeError(FETCH_DEVICES_FAILED)

        # If API call was successful, expire old refresh token
        await Withings.expire_old_refresh_token(str(self.connection.id))
        logger.info(
            f"Expired old_refresh_token for Withings {self.connection.id} after successful devices fetch"
        )

        return [
            map_withings_device(device)
            for device in response.get("body", {}).get("devices", [])
        ]

    async def get_measures(
        self, start_date: Optional[int] = None
    ) -> List[Measure]:
        """
        Method that fetches measures from Withings API
        using the already configured member connection.

        Args:
            start_date (Optional[int]): Start date in unix timestamp for fetching measures
        Returns:
            List[Measure]: List of user's measures.
        """
        from_date = await self.get_fetch_from_date(start_date)

        if self.connection.is_access_token_expired():
            await self.refresh_tokens()

        response = await self.client.get_user_measure(
            self.connection.access_token,
            {
                "meastype": 1,
                "startdate": from_date,
                "enddate": int(pendulum.now("UTC").timestamp()),
                "offset": 0,
            },
        )

        if response.status != 0:
            logger.error(
                f"Measures fetch for {self.member_id} failed: {response}"
            )
            raise RuntimeError(FETCH_MEASURES_FAILED)

        # If API call was successful, expire old refresh token
        await Withings.expire_old_refresh_token(str(self.connection.id))
        logger.info(
            f"Expired old_refresh_token for Withings {self.connection.id} after successful measures fetch"
        )

        return list(
            chain.from_iterable(
                map_withings_weight_group(group)
                for group in response.body.measuregrps
            )
        )

    async def get_fetch_from_date(self, start_date: Optional[int]) -> int:
        """
        Method to determine the start date to fetch measures
        when no dates are configured an error is raised.

        Args:
            start_date (Optional[int]): Provided start date in unix timestamp for fetching measures
        Returns:
            int: Start date in unix timestamp for fetching measures
        Raises:
            ValueError: If no start date could be determined
        """
        last_sync = await get_last_sync_by_vendor(
            self.member_id, ActivityDevice.WITHINGS
        )

        if not last_sync and not start_date:
            logger.error(f"No Withings fetch start date for {self.member_id}")
            raise ValueError(INVALID_FETCH_START_DATE)
        if last_sync is None:
            return start_date
        if start_date is None:
            return last_sync

        return max(last_sync, start_date)
