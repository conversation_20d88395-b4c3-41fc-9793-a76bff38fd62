import urllib
import uuid

import requests

from ciba_iot_etl.models.db.dexcom import Dexcom
from ciba_iot_etl.models.db.member import Member

from app.services.device import DeviceService


class DexcomException(Exception):
    pass


class DexcomService(DeviceService):
    """Dexcom service class"""

    oauth_url: str = "https://sandbox-api.dexcom.com/v2/oauth2"
    api_url: str = "https://sandbox-api.dexcom.com"
    # api_url: str = "https://api.dexcom.com"
    timeout: float = 4.5
    scope: str = "offline_access"

    """
    Documentation:
    https://developer.dexcom.com/docs/dexcom/authentication/
    """

    def parse_response(self, resp: dict) -> dict:
        """
        Parse Dexcom API response model
        """
        return resp

    async def get_auth_code(self, redirect_uri: str, state: str) -> str:
        """
        OAuth 2.0 - Get your authorization code
        Use the authorize method which will construct a URL and redirect to it.
        It will ask the user for permissions.
        The user will be able to sign up for a new account
        or to sign in with their existing account.
        """
        payload = {
            "response_type": "code",
            "client_id": self.client_id,
            "redirect_uri": redirect_uri,
            "scope": self.scope,
            "state": state,
        }
        uri = urllib.parse.urlencode(payload)

        return f"{self.oauth_url}/login?" + uri

    def get_token(self, code: str, redirect_uri: str) -> dict:
        """
        After you obtain the authorization code you can request
        an access_token.
        """
        payload = {
            "grant_type": "authorization_code",
            "client_id": self.client_id,
            "code": code,
            "client_secret": self.client_secret,
            "redirect_uri": redirect_uri,
        }

        headers = {"Content-Type": "application/x-www-form-urlencoded"}

        tokens_request = requests.post(
            f"{self.oauth_url}/token",
            data=payload,
            timeout=self.timeout,
            headers=headers,
        )

        if not tokens_request.ok:
            raise DexcomException("An error occurred obtaining the user token")

        tokens = tokens_request.json()

        return self.parse_response(tokens)

    async def subscribe_to_user_devices(
        self, code: str, redirect_uri: str, member: Member
    ) -> uuid.UUID:
        """
        Persists user Dexcom auth tokens
        """
        tokens = self.get_token(code, redirect_uri)
        access_token = str(tokens.get("access_token", ""))
        refresh_token = str(tokens.get("refresh_token", ""))

        dexcom_creds = Dexcom(
            access_token=access_token,
            member=member,
            expires_in=tokens.get("expires_in"),
            refresh_token=refresh_token,
        )
        await dexcom_creds.save()
        return dexcom_creds.id
