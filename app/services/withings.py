import pendulum
import time
from ciba_iot_etl.extract.withings_api.const import (
    MeasureType,
    NotificationCategory,
)
from ciba_iot_etl.models.pydantic.common import (
    ActivityDevice,
    Platform,
    PullDataNotification,
)
from ciba_iot_etl.models.pydantic.withings import WithingsPayload
from ciba_iot_etl.extract.withings_api.core import WithingsLoader

from app.log.logging import logger
from ciba_iot_etl.models.db.member import Member
from ciba_iot_etl.models.db.member_state import MemberState
from ciba_iot_etl.models.db.withings import Withings
from app.services.queue import SendQueueException, get_queue
from app.settings import get_settings

settings = get_settings()


class InvalidStateException(Exception):
    def __init__(
        self, state: str, message: str = "The request state is not valid!"
    ) -> None:
        self.state = state
        self.message = message
        super().__init__(self.message)


class TokenValidationException(Exception):
    def __init__(
        self, state: str, message: str = "the token parameter is not valid:"
    ) -> None:
        self.message = f"{message}: {state}"
        super().__init__(self.message)


class AuthorisationException(Exception):
    def __init__(
        self, state: str, message: str = "the token parameter is not valid:"
    ) -> None:
        self.message = f"{message}: {state}"
        super().__init__(self.message)


class WithingsServiceException(Exception):
    pass


class MemberWithings:
    """
    Member Withings classs

    Handles withings for member
    """

    def __init__(  # pylint: disable=too-many-arguments disable=R0913
        self, client: WithingsLoader, member: Member
    ) -> None:
        self.member = member
        self.client = client

    async def __check_request_state(self, state: str) -> bool:
        """
        Check if handshake state is valid
        """
        previous_state = await self.__previous_state()

        if previous_state != state:
            raise InvalidStateException(previous_state)

        return True

    async def handle_subscribe_to_account(
        self,
        state: str = "",
        code: str = "",
        email: str = "",
        site: str = "",
    ) -> dict:
        """Process withings user auth and init subscription to notify"""
        response = {}
        try:
            withings_id = await self.subcribe_to_user_devices(
                state=state,
                code=code,
                site=site,
                email=email,
            )
            subscribed = await self.subcribe_to_notification(
                withings_id=withings_id
            )
            if not subscribed:
                response["error"] = (
                    "account didn't subscribe to the notification"
                )

            response["token"] = withings_id
        except Exception as exc:  # pylint: disable=broad-exception-caught
            message = f"subscribe to withings account: {email} failed"
            logger.error(message, exc_info=exc)
            response["error"] = message

        return response

    async def subcribe_to_user_devices(
        self,
        state: str,
        code: str,
        email: str,
        site: str,
    ) -> str:
        """
        Returns a user devices and make event hook subscription.
        """
        logger.debug(  # pylint: disable=logging-fstring-interpolation
            f"Site is {site}"
        )  # pylint: disable=logging-fstring-interpolation

        tokens = await self.client.get_token(code)
        if "error" in tokens:
            logger.error(
                "withigs get_token failed code: %s, uri: %s. error: %s",
                code,
                self.client.redirect_uri,
                tokens["error"],
            )
            raise AuthorisationException(
                f"withigs get_token failed: {tokens['error']}"
            )
        tokens = tokens["body"]

        access_token = str(tokens.get("access_token", ""))
        refresh_token = str(tokens.get("refresh_token", ""))
        user_id = tokens.get("userid")
        expires_in = tokens.get("expires_in")
        try:
            stored_user_device = await Withings.filter(
                user_id=user_id
            ).get_or_none()
            if stored_user_device:
                linked_member_id = stored_user_device.member_id
                if linked_member_id != self.member.id:
                    stored_user_device.member = self.member
                    stored_user_device.access_token = access_token
                    stored_user_device.refresh_token = refresh_token
                    stored_user_device.expires_in = expires_in
                    await stored_user_device.save()
                    return stored_user_device.id
                elif linked_member_id == self.member.id:
                    withings_id = await self.member.get_withings_id()
                    return withings_id
            else:
                withings_creds = Withings(
                    access_token=access_token,
                    user_id=user_id,
                    expires_in=expires_in,
                    member=self.member,
                    refresh_token=refresh_token,
                )
                await withings_creds.save()
                return withings_creds.id
        except Exception as exc:
            logger.error("withigs database error", exc_info=exc)
            raise WithingsServiceException(
                f"store withings device for account: {email} failed"
            ) from exc

    def __check_subscription_response(self, response: dict) -> bool:
        if response["status"] != 0 and "error" in response:
            logger.info("action to notification failed: %s", response["error"])
            return False

        return True

    async def subcribe_to_notification(
        self,
        withings_id: str,
    ) -> bool:
        """init subscribe to notification action"""
        withings = await Withings.get_or_none(id=withings_id)
        if withings is None:
            raise WithingsServiceException("withings device not found")

        try:
            await self.delete_subscription(
                access_token=withings.access_token,
                withings_id=str(withings.id),
            )
        except Exception as e:
            logger.error(f"Withings subscription deletion: {e}")

        try:
            subscriptions = [
                await self.client.create_subscription(
                    str(NotificationCategory.WEIGHT), withings.access_token
                ),
                await self.client.create_subscription(
                    str(NotificationCategory.PRESSURE), withings.access_token
                ),
            ]

            all_successful = all(
                self.__check_subscription_response(response)
                for response in subscriptions
            )

            # If subscriptions were successful, expire old refresh token
            if all_successful:
                await Withings.expire_old_refresh_token(str(withings.id))
                logger.info(
                    f"Expired old_refresh_token for Withings {withings.id} after successful subscription creation"
                )

            return all_successful
        except Exception as e:
            logger.error(f"Withings subscription creation: {e}")

    async def create_state(self, state: str, redirect_url: str = "") -> str:
        """
        Generate unique state and store it
        """
        member_state = await MemberState.create(
            member=self.member, state=state, redirect_uri=redirect_url
        )
        return str(member_state.state)

    async def __previous_state(self) -> str:
        """
        Returns last state for Member
        """
        member_state = (
            await MemberState.filter(member=self.member)
            .order_by("-created_at")
            .limit(1)
            .first()
        )
        if member_state is None:
            return ""
        return str(member_state.state)

    async def handle_notification(
        self,
        userid: int,
        appli: int,
        startdate: int,
        enddate: int,
        correlation_id: str,
    ) -> bool:
        """
        Process notification
        """
        if self.member is None:
            logger.info(
                "skip handling withings notification, user with withings ID: %s not exists",  # noqa
                userid,
            )
            return False

        platforms = await self.member.get_platforms()
        sending_result = await self.send_pull_notification(
            startdate, enddate, platforms, correlation_id
        )
        logger.info("handling a withings' notification done")

        return sending_result

    async def send_pull_notification(
        self,
        startdate: int,
        enddate: int,
        platforms: list[Platform],
        correlation_id: str,
    ) -> bool:
        """Build and send withings notification to the queue"""
        withings_id = await self.member.get_withings_id()
        if withings_id is None:
            logger.debug(
                "sending notification skipped, Withings device for member: %s not exists",
                self.member.id,
            )
            raise WithingsServiceException(
                "sending notification skipped, device not exists"
            )

        measure_types = [
            MeasureType.WEIGHT,
            MeasureType.SYSTOLIC_BLOOD_PRESSURE,
            MeasureType.DIASTOLIC_BLOOD_PRESSURE,
            MeasureType.HEART_RATE,
        ]

        try:
            withings_payload = WithingsPayload(
                withings_id=withings_id,
                meastypes=measure_types,
                category=1,
                start_date=startdate,
                end_date=enddate,
            )

            notification_payload = PullDataNotification(
                member_id=str(self.member.id),
                activity_device=ActivityDevice.WITHINGS,
                platforms=platforms,
                payload=withings_payload,
                corellation_id=correlation_id,
            )

            message = notification_payload.model_dump_json()
            logger.info(  # pylint: disable=logging-fstring-interpolation
                f"message for {notification_payload.member_id} ready to sending"
            )
            logger.debug(  # pylint: disable=logging-fstring-interpolation
                f"message: {message}"
            )  # pylint: disable=logging-fstring-interpolation
            queue = get_queue(settings.SQS_DATA_NOTIFICATION_QUEUE)

            await queue.send(message)

        except SendQueueException as exception:
            error_message = "sending notification to the queue failed"
            logger.exception(error_message, exc_info=exception)
            raise WithingsServiceException(error_message) from exception

        return True

    async def sync_device(
        self, correlation_id: str, start_date: int = 0
    ) -> bool:
        """Subscribe member to notification and
        trigger Lambada to pull data from start_date until now"""
        if self.member is None:
            raise WithingsServiceException("member is None")

        # send message to we queue to pull last data from API
        platforms = await self.member.get_platforms()
        return await self.send_pull_notification(
            startdate=start_date,
            enddate=int(time.time()),
            platforms=platforms,
            correlation_id=correlation_id,
        )

    async def delete_subscription(
        self, access_token: str, withings_id: str | None = None
    ) -> bool:
        subscriptions = [
            await self.client.delete_subscription(
                appli=str(NotificationCategory.WEIGHT),
                access_token=access_token,
            ),
            await self.client.delete_subscription(
                appli=str(NotificationCategory.PRESSURE),
                access_token=access_token,
            ),
        ]
        all_successful = all(
            self.__check_subscription_response(response)
            for response in subscriptions
        )

        # If deletion was successful and we have a withings_id, expire old refresh token
        if all_successful and withings_id:
            await Withings.expire_old_refresh_token(withings_id)
            logger.info(
                f"Expired old_refresh_token for Withings {withings_id} after successful subscription deletion"
            )

        return all_successful

    async def disconnect_device(self) -> bool:
        """Revoke withings notification subscription and delete member device"""
        try:
            if self.member is None:
                raise WithingsServiceException("member is None")

            withings_id = await self.member.get_withings_id()
            withings_connection = await Withings.filter(id=withings_id).first()
            if not withings_connection:
                logger.info("User withings connection not found")
                raise WithingsServiceException(
                    f"User: {self.member.email} withings connection not found"
                )

            access_token = withings_connection.access_token
            refresh_token = withings_connection.refresh_token
            expires_in = withings_connection.expires_in

            # refresh the token to make sure what access token valid
            expires_at = pendulum.instance(withings_connection.updated_at).add(
                seconds=expires_in
            )

            if expires_at < pendulum.now():
                # Use fallback-enabled refresh
                refresh_result = await self.refresh_token_with_fallback(
                    withings_connection
                )

                if not refresh_result["success"]:
                    error = refresh_result.get("error", "Unknown error")
                    logger.error(f"Error refreshing user token: {error}")
                    await Withings.filter(id=withings_id).update(healthy=False)
                    logger.error(f"refreshing token failed: {error}")
                    raise WithingsServiceException(
                        f"Error fetching Withings token for withings {withings_id}: {error}. Terminating processing"
                    )

                user_response = refresh_result["token_response"]
                user = user_response["body"]
                access_token = user.get("access_token")
                refresh_token = user.get("refresh_token")
                expires_in = user.get("expires_in")
                if not access_token or not refresh_token or expires_in is None:
                    raise ValueError(f"Invalid token response: {user}")

                # Use proper update_tokens method to preserve old_refresh_token
                await Withings.update_tokens(
                    withings_id=str(withings_id),
                    access_token=access_token,
                    refresh_token=refresh_token,
                    expires_in=int(expires_in),
                )

                if refresh_result.get("used_fallback"):
                    logger.info(
                        f"Token refreshed for withings {withings_id} using old_refresh_token fallback"
                    )
                else:
                    logger.info(f"Token refreshed for withings {withings_id}")

            await self.delete_subscription(
                access_token=access_token, withings_id=str(withings_id)
            )

        except Exception as exc:
            logger.error("revoke the notification failed", exc_info=exc)

        # delete withings device for member
        await self.member.delete_withings()
        logger.info(
            "withings device for member: %s disconnected", self.member.id
        )
        return True

    async def refresh_token_with_fallback(
        self, withings_connection: Withings
    ) -> dict:
        """
        Attempt to refresh token with fallback to old_refresh_token if current fails.
        Uses standardized ciba-iot-etl token refresh utilities.

        Args:
            withings_connection: The Withings connection object

        Returns:
            dict with success status and token data or error information
        """
        from ciba_iot_etl.extract.common.token_refresh import (
            refresh_withings_token_with_fallback,
        )
        from ciba_iot_etl.monitoring.token_metrics import (
            log_token_refresh_metrics,
        )

        connection_id = str(withings_connection.id)

        # Use standardized token refresh from ciba-iot-etl
        result = await refresh_withings_token_with_fallback(
            withings_service=self.client,
            withings_id=connection_id,
            withings_connection=withings_connection,
        )

        # Log metrics using standardized monitoring
        log_token_refresh_metrics(
            result=result,
            platform="withings",
            connection_id=connection_id,
            additional_context={"service": "MemberWithings"},
        )

        # Convert standardized result to legacy format for backward compatibility
        if result.success:
            # For Withings, we need to wrap the token data in the expected format
            return {
                "success": True,
                "used_fallback": result.used_fallback,
                "token_response": {"body": result.token_data},
                "error_type": None,
                "requires_reauth": False,
            }
        else:
            return {
                "success": False,
                "error": result.error_message,
                "error_type": result.error_type
                if result.error_type
                else "api_error",
                "requires_reauth": result.requires_reauth,
                "used_fallback": result.used_fallback,
            }

    async def renew_credential(self):
        """Call refresh token and update access and refresh tokens"""
        refresh_token = await self.member.get_withings_refresh_token()
        if refresh_token is None:
            raise WithingsServiceException("unauthorized, token is empty")

        # Get withings connection for fallback logic
        withings_id = await self.member.get_withings_id()
        if withings_id is None:
            raise WithingsServiceException("withings connection not found")

        withings_connection = await Withings.get(id=withings_id)

        # Use fallback-enabled refresh
        refresh_result = await self.refresh_token_with_fallback(
            withings_connection
        )

        if not refresh_result["success"]:
            raise WithingsServiceException(
                f"Token refresh failed: {refresh_result.get('error', 'Unknown error')}"
            )

        token_response = refresh_result["token_response"]
        token = token_response["body"]

        # Validate token response
        access_token = token.get("access_token")
        new_refresh_token = token.get("refresh_token")
        expires_in = token.get("expires_in")

        if not access_token or not new_refresh_token or expires_in is None:
            raise WithingsServiceException(
                "Invalid token response from Withings API"
            )

        # Validate what userid == member.withings.user_id
        # if not await self.member.withings_user_valid(token.get("userid")):
        #     raise Exception("refreshing token failed, user id did not match")

        await Withings.update_tokens(
            withings_id=str(withings_id),
            access_token=access_token,
            refresh_token=new_refresh_token,
            expires_in=int(expires_in),
        )

        if refresh_result["used_fallback"]:
            logger.info(
                "withings token for member: %s renewed using old_refresh_token fallback",
                self.member.id,
            )
        else:
            logger.info(
                "withings token for member: %s renewed", self.member.id
            )
