from typing import Union, Optional

from ciba_iot_etl.models.db.member import Member
from ciba_iot_etl.models.db.member_state import MemberState

from app.services.withings import MemberWithings
from app.settings import get_settings
from starlette.responses import RedirectResponse
from app.pydantic_model.base import TokenResp
from app.helpers.url import append_query_string
from app.log.logging import logger
import pendulum
from ciba_iot_etl.models.pydantic.common import ActivityDevice
from app.routers.requests.api_request import SyncRequets
from app.routers.api import sync_user
from app.services.fitbit import MemberFitbit
from ciba_iot_etl.extract.withings_api.core import WithingsLoader
from ciba_iot_etl.extract.fitbit_api.core import FitbitLoader

settings = get_settings()
ERROR_PARAM = "connectionError"


def append_error_to_url(url: str, error: Optional[str]) -> str:
    """
    Appends a connection error parameter to the given URL if an error exists
    and the URL doesn't already contain a connection error.

    Args:
        url: The base URL to append the error to.
        error: The error message to append.
    Returns:
        The URL with the connection error parameter appended, if applicable.
    """
    if error and ERROR_PARAM not in url:
        return append_query_string(url, ERROR_PARAM, error)

    return url


class CommonService:
    """Service to get the auth_url from devices by type_device"""

    def __init__(self, member: Member, type_device: str, site: str) -> None:
        self.type_device = type_device
        self.member = member
        self.site = site
        self.uri = f"email={self.member.email}"
        self.return_uri = ""

    async def get_service(
        self,
    ) -> Union[MemberWithings]:
        """
        Get service by type_device and generate object
        :return: service
        """
        pass

    async def get_code(self) -> str:
        """
        OAuth 2.0 - Get your authorization code to different devices.
        Use the authorize method which will construct a URL and redirect to it.
        It will ask the user for permissions.
        The user will be able to sign up for a new account
        or to sign in with their existing account.
        """
        service = await self.get_service()
        return await service.get_auth_code(self.return_uri, self.uri)

    async def sync_device(self, start_date: int) -> bool:
        """
        sync data for device
        """
        service = await self.get_service()
        return await service.sync_device(start_date)

    async def disconnect_device(self) -> bool:
        """
        disconnect device
        """
        service = await self.get_service()
        return await service.disconnect_device()


async def process_member_state(
    token_resp: TokenResp, state: str, site: str, default_days: int = 30
):
    """
    Getting data sync start date and url, where user should be redirected after
    account subscription is done
    """

    try:
        member_state_entry = await MemberState.filter(
            state=state
        ).get_or_none()
    except Exception as e:
        logger.error(f"Error fetching member state: {e}")
        token_resp.error = "Failed to fetch member state"
        member_state_entry = None

    if member_state_entry and member_state_entry.sync_start_date:
        start_date = pendulum.instance(
            member_state_entry.sync_start_date
        ).int_timestamp
    else:
        start_date = pendulum.now().subtract(days=default_days).int_timestamp

    site = (
        member_state_entry.redirect_uri
        if member_state_entry and member_state_entry.redirect_uri
        else site
    )
    if site:
        site = append_query_string(site, "connectionError", token_resp.error)

    return site, start_date


async def subscribe_to_account_helper(
    client: Union[WithingsLoader, FitbitLoader],
    member_device_handler: Union[MemberWithings, MemberFitbit],
    email: str,
    code: str,
    state: str,
    site: str,
    error: str,
    activity_device: ActivityDevice,
):
    """
    General helper for subscribing to an account (either Fitbit or Withings).
    """
    token_resp = TokenResp()

    # Fetch member based on the provided arguments (state has higher priority)
    if state:
        member = await MemberState.get_member_by_state(state)
    elif email:
        member = await Member.get_or_none(email=email)
    else:
        token_resp.error = "Neither email nor state provided"
        return token_resp

    if not member:
        token_resp.error = "Member not found"
        return token_resp

    try:
        member_platforms = await Member.get_platforms(member)
    except Exception as e:
        logger.error(f"Error fetching member platforms: {e}")
        token_resp.error = "Failed to fetch member platforms"
        return token_resp

    site, start_date = await process_member_state(
        token_resp=token_resp, state=state, site=site
    )

    # Handle the subscription flow
    if code and not error:
        try:
            member_device = member_device_handler(member=member, client=client)
            token = await member_device.handle_subscribe_to_account(
                state=state, code=code, email=email, site=site
            )
            token_resp = TokenResp.model_validate(token)
            for platform in member_platforms:
                sync_request = SyncRequets(
                    member_id=platform["id"],
                    type_device=activity_device,
                    member_type=platform["type"],
                    start_date=start_date,
                )
                await sync_user(
                    request_data=sync_request,
                    correlation_id=str(pendulum.now().int_timestamp),
                )

        except Exception as e:
            logger.error(f"Error during subscription handling: {e}")
            token_resp.error = "Failed to handle subscription"
    else:
        token_resp.error = error if error else "No code provided"

    if site:
        return RedirectResponse(append_error_to_url(site, token_resp.error))

    return token_resp
