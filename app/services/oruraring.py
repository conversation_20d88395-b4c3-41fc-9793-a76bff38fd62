# import uuid
#
# import requests
#
# from app.models.oruraring import Oruraring
# from app.services.device import DeviceService
#
#
# class InvalidStateException(Exception):
#     def __init__(
#         self, state: str, message: str = "The request state is not valid!"
#     ) -> None:
#         self.state = state
#         self.message = message
#         super().__init__(self.message)
#
#
# class OruraringService(DeviceService):
#     """Oruraring service class"""
#
#     oauth_url: str = "https://cloud.ouraring.com"
#     api_url: str = "https://api.ouraring.com"
#     timeout: float = 4.5
#     """
#     Documentation:
#     https://cloud.ouraring.com/v2/docs#tag/Personal-Info-Routes
#     """
#
#     def __init__(self, client_id: str, client_secret: str, state: str) -> None:
#         super().__init__(client_id, client_secret)
#         self.state = state
#
#     def check_request_state(self, state: str) -> bool:
#         """
#         Check if handshake state is valid
#         """
#         if self.state != state:
#             raise InvalidStateException(state)
#
#         return True
#
#     async def get_auth_code(self, redirect_uri: str, state: str) -> str:
#         """
#         OAuth 2.0 - Get your authorization code
#         Use the authorize method which will construct a URL and redirect to it.
#         It will ask the user for permissions.
#         The user will be able to sign up for a new account
#         or to sign in with their existing account.
#         """
#         payload = {
#             "response_type": "code",
#             "client_id": self.client_id,
#             "redirect_uri": redirect_uri,
#             "state": state,
#         }
#         auth_response = requests.get(
#             f"{self.oauth_url}/oauth/authorize",
#             params=payload,
#             timeout=self.timeout,
#         )
#         return str(auth_response.url)
#
#     def get_token(self, code: str, redirect_uri: str) -> dict:
#         """
#         After you obtain the authorization code you can request
#         an access_token.
#         """
#         payload = {
#             "code": code,
#             "redirect_uri": redirect_uri,
#             "grant_type": "authorization_code",
#             "client_id": self.client_id,
#             "client_secret": self.client_secret,
#         }
#         token = requests.post(
#             f"{self.api_url}/oauth/token", data=payload, timeout=self.timeout
#         ).json()
#
#         return token
#
#     def get_user_personal_info(self, access_token: str) -> dict:
#         """
#         Returns the list of user linked devices.
#         """
#         return requests.get(
#             f"{self.api_url}/v1/userinfo",
#             params={"access_token": access_token},
#             timeout=self.timeout,
#         ).json()
#
#     async def subcribe_to_user_devices(
#         self, code: str, auth_callback_uri: str, query: dict
#     ) -> uuid.UUID:
#         """
#         Returns a user token and meta info.
#         """
#         # self.check_request_state(state)
#         tokens = self.get_token(code, auth_callback_uri)
#         access_token = str(tokens.get("access_token", ""))
#         personal_info = self.get_user_personal_info(access_token)
#         if "email" in query:
#             email = query["email"]
#         else:
#             email = personal_info.get("email", "")
#         oruraring_creds = Oruraring(
#             access_token=tokens.get("access_token"),
#             refresh_token=tokens.get("refresh_token"),
#             expires_in=tokens.get("expires_in"),
#             user_id=personal_info.get("user_id"),
#             email=email,
#         )
#         await oruraring_creds.save()
#         return oruraring_creds.id
#
#     async def sync_device(self, start_date: int):
#         """Sync device"""
#         raise NotImplementedError(
#             "The sync method is not yet implemented for Oruraring"
#         )
#
#     async def disconnect_device(self):
#         """Disconnect device"""
#         raise NotImplementedError(
#             "The dysconnect method is not yet implemented for Oruraring"
#         )
