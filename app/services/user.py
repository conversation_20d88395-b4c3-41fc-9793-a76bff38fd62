import sys

from typing import List, Optional
import pendulum
from tortoise.fields import <PERSON><PERSON><PERSON><PERSON>

from app.log.logging import logger
from ciba_iot_etl.models.db.dexcom import <PERSON><PERSON>
from ciba_iot_etl.models.db.fitbit import Fitbit
from ciba_iot_etl.models.db.transtek import Transtek, TranstekStatus
from ciba_iot_etl.models.db.member import Member
from ciba_iot_etl.models.db.member_platform import MemberPlatform
from ciba_iot_etl.models.db.withings import Withings
from app.pydantic_model.device_api import DeviceStatus, DeviceStatusEnum
from ciba_iot_etl.models.pydantic.common import ActivityDevice


class User:
    """User class for check some data fron user"""

    def __init__(self, email: str = "", device: str = ""):
        self.email = email.strip().replace(" ", "+")
        self.device = device.title()

    async def map_to_platform(
        self, external_type: str, external_id: str
    ) -> Member:
        """Map RPM member with external platform"""
        member = await Member.get_by_platform(external_type, external_id)
        if member is None:
            logger.info("member for platform not exists")
            member, _ = await Member.get_or_create(email=self.email)
            await MemberPlatform(
                platform_type=external_type,
                platform_id=external_id,
                member=member,
            ).save()
            logger.info("member for platform created, id: %s", member.id)

        return member

    @staticmethod
    async def fetch_member(
        member_type: str, member_id: str
    ) -> Optional[Member]:
        """Fetch the member from the platform without raising exceptions."""
        try:
            return await Member.get_by_platform(
                platform_type=member_type, platform_id=member_id
            )
        except Exception as e:
            logger.error(
                f"An error occurred while fetching member: {e}", exc_info=True
            )
            return None

    async def related_device(
        self,
    ) -> (Withings | Fitbit | Dexcom | Transtek) | None:
        """
        Check if user exist in database
        :return: user id or None
        """

        try:
            cls = get_class(self.device)
        except ValueError as e:
            logger.error(f"Error getting class: {e}")
            return None

        return await cls.filter(member__email=self.email).get_or_none()


def get_class(
    class_name: str,
) -> Withings | Fitbit | Dexcom | Transtek:
    """Generate class from string"""
    """Retrieve a class by name, raising an error if not found."""
    cls = getattr(sys.modules.get(__name__), class_name, None)
    if cls is None:
        raise ValueError(
            f"Class '{class_name}' not found in module '{__name__}'"
        )
    return cls


class UserDeviceService:
    """Service class to manage device statuses."""

    @classmethod
    def default_device_status(cls, device: ActivityDevice) -> DeviceStatus:
        """Return device status for the account."""
        return DeviceStatus(
            device=device, status=DeviceStatusEnum.NOT_CONNECTED
        )

    @classmethod
    async def get_device_status(
        cls, device_type: ActivityDevice, member: Optional[Member] = None
    ) -> DeviceStatus:
        """
        Determine the status of a specific device for a member.

        Args:
            device (ActivityDevice): The device to check status for.
            member (Optional[User]): User to check device status for.

        Returns:
            DeviceStatus: Status of the specified device.
        """
        device_status = cls.default_device_status(device_type)

        if member is None:
            return device_status

        try:
            user = User(email=member.email, device=device_type.value)
            device = await user.related_device()
            if device is None:
                device_status.status = DeviceStatusEnum.NOT_CONNECTED
                return device_status

            device_status.id = device.id
            if device.field_exists("healthy") and device.healthy is True:
                device_status.status = DeviceStatusEnum.CONNECTED
            elif device_type == ActivityDevice.TRANSTEK:
                transtek_device: Transtek = device
                healthy = transtek_device.status in [
                    TranstekStatus.PAIRED,
                    TranstekStatus.ACTIVE,
                    TranstekStatus.SHIPPED,
                ]

                last_status: JSONField | None = (
                    transtek_device.last_status_report
                )

                has_recent_status = False
                no_error = True

                if last_status:
                    has_recent_status = pendulum.from_timestamp(
                        last_status.get("createdAt", 0)
                    ) > pendulum.now().subtract(days=5)
                    no_error = "_err" not in last_status.get("data_type", "")

                if healthy and has_recent_status and no_error:
                    ...  # When we want to send detailed status data

                device_status.status = (
                    DeviceStatusEnum.CONNECTED
                    if healthy
                    else DeviceStatusEnum.NOT_CONNECTED
                )
            else:
                device_status.status = DeviceStatusEnum.RECONNECT

            if device.field_exists("updated_at"):
                device_status.last_updated = device.updated_at
        except Exception as e:
            logger.error(
                f"An error occurred while fetching device status: {e}",
                exc_info=True,
            )

        return device_status

    @classmethod
    async def get_transtek_device_statuses(
        cls, member: Optional[Member] = None
    ) -> List[DeviceStatus]:
        """
        Get device statuses for all Transtek devices associated with a member.

        Args:
            member (Optional[Member]): Member to fetch Transtek device statuses for.

        Returns:
            List[DeviceStatus]: List of device statuses for all Transtek devices.
        """
        default_status = cls.default_device_status(ActivityDevice.TRANSTEK)
        if member is None:
            return [default_status]

        try:
            transtek_devices = await Transtek.filter(member_id=member.id).all()

            if not transtek_devices:
                return [default_status]

            device_statuses = []
            for transtek_device in transtek_devices:
                device_status = cls.default_device_status(
                    ActivityDevice.TRANSTEK
                )
                device_status.id = transtek_device.id
                healthy = transtek_device.status in [
                    TranstekStatus.PAIRED,
                    TranstekStatus.ACTIVE,
                    TranstekStatus.SHIPPED,
                ]

                last_status: JSONField | None = (
                    transtek_device.last_status_report
                )
                has_recent_status = False
                no_error = True
                battery = signal = None

                if last_status:
                    created_at = last_status.get("createdAt", 0)
                    data_type = last_status.get("data_type", "")

                    battery = last_status.get("status", {}).get("bat")
                    signal = last_status.get("status", {}).get("sig")

                    has_recent_status = pendulum.from_timestamp(
                        created_at
                    ) > pendulum.now().subtract(days=5)
                    no_error = "_err" not in data_type

                if healthy and has_recent_status and no_error:
                    ...  # When we want to send detailed status data

                device_status.status = (
                    DeviceStatusEnum.CONNECTED
                    if healthy
                    else DeviceStatusEnum.NOT_CONNECTED
                )

                device_status.device_metadata = {
                    "device_id": transtek_device.device_id,
                    "imei": transtek_device.imei,
                    "model": transtek_device.model,
                    "device_type": transtek_device.device_type.value,
                    "battery": battery,
                    "signal": signal,
                }

                if transtek_device.field_exists("updated_at"):
                    device_status.last_updated = transtek_device.updated_at

                device_statuses.append(device_status)

            return device_statuses

        except Exception as e:
            logger.error(
                f"An error occurred while fetching Transtek device statuses: {e}",
                exc_info=True,
            )
            return [cls.default_device_status(ActivityDevice.TRANSTEK)]

    @classmethod
    async def fetch_device_statuses(
        cls, member: Optional[Member] = None
    ) -> List[DeviceStatus]:
        """
        Fetch device statuses for a given member.

        Args:
            member (Optional[Member]): Member to fetch device statuses for.

        Returns:
            List[DeviceStatus]: List of device statuses.
        """
        all_devices: List[ActivityDevice] = list(ActivityDevice)
        all_devices.remove(ActivityDevice.OURARING)

        device_statuses = []
        for device_type in all_devices:
            if device_type == ActivityDevice.TRANSTEK:
                # Handle Transtek specially to support multiple devices
                transtek_statuses = await cls.get_transtek_device_statuses(
                    member
                )
                device_statuses.extend(transtek_statuses)
            else:
                # Handle other device types normally (one device per type)
                device_status = await cls.get_device_status(
                    device_type, member
                )
                device_statuses.append(device_status)

        return device_statuses
