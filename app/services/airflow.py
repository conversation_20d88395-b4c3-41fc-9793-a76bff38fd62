import airflow_client
import airflow_client.client
from airflow_client.client.api import config_api, dag_run_api


class InvalidStateException(Exception):
    def __init__(
        self, state: str, message: str = "The request state is not valid!"
    ) -> None:
        self.salary = state
        self.message = message
        super().__init__(self.message)


class AirflowService:
    """Airflow API service class"""

    def __init__(
        self, airflow_url: str, airflow_login: str, airflow_password: str
    ) -> None:
        self.airflow_url = airflow_url
        self.airflow_login = airflow_login
        self.airflow_password = airflow_password

        self.airflow_configuration = airflow_client.client.Configuration(
            host=self.airflow_url,
            username=self.airflow_login,
            password=self.airflow_password,
        )
        self.airflow_client = airflow_client.client.ApiClient(
            self.airflow_configuration
        )

    def get_config(self) -> dict:
        """
        Returns Airflow running configuration
        """
        airflow_config_instance = config_api.ConfigApi(self.airflow_client)
        return airflow_config_instance.get_config()

    def run_dag(self, dag_id: str, dag_run: dict) -> dict:
        """
        Run Airflow dag
        """
        airflow_dag_instance = dag_run_api.DAGRunApi(self.airflow_client)
        result = airflow_dag_instance.post_dag_run(
            dag_id, dag_run, async_req=True
        )
        return result
