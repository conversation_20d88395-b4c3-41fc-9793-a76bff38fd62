import pendulum
from fastapi import APIRouter, Form, HTTPException, Request
from starlette.responses import RedirectResponse, Response

from ciba_iot_etl.models.pydantic.common import ActivityDevice
from ciba_iot_etl.extract.withings_api.core import WithingsLoader

from app.log.logging import logger
from ciba_iot_etl.models.db.member import Member
from app.pydantic_model.base import AuthResp, TokenResp
from app.services.user import User
from app.services.withings import MemberWithings
from app.settings import get_settings
from ciba_iot_etl.models.db.withings import Withings
from app.services.common import subscribe_to_account_helper


settings = get_settings()

withings_client = WithingsLoader(
    client_id=settings.WITHINGS_CLIENT_ID,
    client_secret=settings.WITHINGS_CUSTOMER_SECRET,
    redirect_uri=settings.WITHINGS_REDIRECT_URI,
    callback_uri=settings.WITHINGS_CALLBACK_URI,
    scope=settings.WITHINGS_SCOPE,
    notification_callback_uri=settings.WITHINGS_NOTIFICATION_CALLBACK_URI,
)


router = APIRouter(prefix="/withings", tags=["withings-router"])


@router.head("/user_metrics_notification")
def notification_handler_head() -> Response:
    """
    Withings use HEAD request to validate callback
    """
    logger.info("withings confirm notification callback url")
    return Response("ok")


@router.post("/user_metrics_notification")
async def notification_handler_post(
    request: Request,
    userid: int = Form(),
    appli: int = Form(),
    startdate: int = Form(),
    enddate: int = Form(),
) -> Response:
    """
    Callback route for notifications.
    Notifications will be send when health data are created,
    updated, deleted or when special events happen
    """
    correlation_id = request.headers.get(
        "X-Request-ID", str(pendulum.now().int_timestamp)
    )
    logger.info("start processing withings notification")

    member = await Member.filter(withings__user_id=userid).get_or_none()
    if not member:
        withings_device = Withings.filter(user_id=userid).get_or_none()
        if withings_device:
            withings_device.delete()
        raise HTTPException(status_code=404, detail="Member not found")

    member_withings = MemberWithings(member=member, client=withings_client)

    if await member_withings.handle_notification(
        userid, appli, startdate, enddate, correlation_id
    ):
        return Response("ok")

    raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/get_token",
    response_model=TokenResp,
    responses={302: {"description": "Redirect to the site"}},
)
async def subscribe_to_account(
    email: str = "",
    code: str = "",
    state: str = "",
    site: str = "",
    error: str = "",
) -> RedirectResponse | TokenResp:
    """
    Callback route for Withings account subscription.
    """
    return await subscribe_to_account_helper(
        client=withings_client,
        member_device_handler=MemberWithings,
        email=email,
        code=code,
        state=state,
        site=site,
        error=error,
        activity_device=ActivityDevice.WITHINGS,
    )


@router.get("/get_code_email/", response_model=None)
async def get_code_email(
    mail: str, site: str = ""
) -> RedirectResponse | AuthResp:
    """
    Route to get the permission from an user to take his data.
    This endpoint redirects to a Withings' login page on which
    the user has to identify and accept to share his data
    """
    token = await User(
        email=mail, device=ActivityDevice.WITHINGS
    ).related_device()
    if token:
        return RedirectResponse(site + "/?token=" + str(token.id))
    withings_client.redirect_uri = site or ""
    auth_response = await withings_client.get_auth_page_url()

    return AuthResp(auth_url=auth_response["auth_url"])
