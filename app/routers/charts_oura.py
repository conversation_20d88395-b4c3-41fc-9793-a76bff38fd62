# import json
#
# import pandas as pd
# from fastapi import APIRouter, Security
# from fastapi.security import APIKeyHeader
#
# from app.auth.constants import X_AUTH_KEY
# from app.common.db import engine
#
# router = APIRouter(
#     prefix="/charts",
#     dependencies=[
#         Security(APIKeyHeader(name=X_AUTH_KEY)),
#     ],
# )
#
#
# @router.get("/readiness_oruraring")
# async def readiness_oruraring() -> dict:
#     """
#     Callback route when ask for weight data
#     table name = readiness_oruraring
#     """
#     data = pd.read_sql_table("readiness_oruraring", engine)
#
#     data["time"] = pd.to_datetime(data["timestamp"])
#     df2 = data[["time", "score", "body_temperature"]]
#     # get data from pandas dataframe
#     json_str = df2.to_json(orient="records", date_format="iso")
#     return json.loads(json_str)
#
#
# @router.get("/sleep_oruraring")
# async def sleep_oruraring() -> dict:
#     """
#     Callback route when ask for weight data
#     table name = sleep_oruraring
#     """
#     data = pd.read_sql_table("sleep_oruraring", engine)
#     data["time"] = pd.to_datetime(data["timestamp"])
#     df2 = data[["time", "score"]]
#     # get data from pandas dataframe
#     json_str = df2.to_json(orient="records", date_format="iso")
#     return json.loads(json_str)
#
#
# @router.get("/activity_oruraring")
# async def activity_oruraring() -> dict:
#     """
#     Callback route when ask for weight data
#     table name = activity_oruraring
#     """
#     data = pd.read_sql_table("activity_oruraring", engine)
#     data["time"] = pd.to_datetime(data["timestamp"])
#     df2 = data[["time", "score"]]
#     # get data from pandas dataframe
#     json_str = df2.to_json(orient="records", date_format="iso")
#     return json.loads(json_str)
