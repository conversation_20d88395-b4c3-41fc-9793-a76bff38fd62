import urllib
from http import HTT<PERSON>tatus

from fastapi import APIRouter, HTTPException
from starlette.responses import RedirectResponse

from ciba_iot_etl.models.db.member_state import MemberState
from ciba_iot_etl.models.pydantic.common import ActivityDevice

from app.helpers.url import append_query_string
from app.pydantic_model.base import AuthResp, TokenResp
from app.services.dexcom import DexcomService, DexcomException
from app.services.user import User
from app.settings import get_settings

settings = get_settings()

dexcom_service = DexcomService(
    settings.DEXCOM_CLIENT_ID, settings.DEXCOM_CLIENT_SECRET
)

router = APIRouter(prefix="/dexcom", tags=["dexcom-router"])
dag_id_call: str = "dexcom_update"


@router.get("/get_token", response_model=None)
async def subscribe_to_account(
    code: str = "", state: str = "", site: str = ""
) -> RedirectResponse | TokenResp:
    """
    Callback route when the user has accepted to share his data.
    Once the auth has arrived Fitbit servers come back with
    an authentication code and the state code provided in the
    initial call
    """
    member = await MemberState.get_member_by_state(state=state)
    error = None
    token = None

    if not member:
        raise HTTPException(
            status_code=HTTPStatus.NOT_FOUND, detail="User not found"
        )

    try:
        token = await dexcom_service.subscribe_to_user_devices(
            code, settings.DEXCOM_REDIRECT_URI, member
        )
    except DexcomException as exception:
        error = str(exception)

    redirect_site = site if site else await MemberState.get_redirect_uri(state)

    if redirect_site:
        redirect_site = append_query_string(
            redirect_site, "connectionError", error
        )
        return RedirectResponse(redirect_site)

    return TokenResp(token=token)


@router.get("/get_code_email/", response_model=None)
async def get_code_email(
    mail: str, site: str = ""
) -> RedirectResponse | AuthResp:
    """
    Route to get the permission from an user to take his data.
    This endpoint redirects to a Fitbit' login page on which
    the user has to identify and accept to share his data
    """
    query = {"email": mail, "site": site}
    token = await User(
        email=mail, device=ActivityDevice.DEXCOM
    ).related_device()
    if token:
        return RedirectResponse(site + "/?token=" + str(token.id))

    uri = urllib.parse.urlencode(query)
    auth_response = await dexcom_service.get_auth_code(
        settings.DEXCOM_REDIRECT_URI, uri
    )

    return AuthResp(auth_url=auth_response)
