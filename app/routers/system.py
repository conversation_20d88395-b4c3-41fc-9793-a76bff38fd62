import logging

from fastapi import APIRouter, Depends, Request, Response
from starlette_exporter import handle_metrics
from tortoise import Tortoise

from app.auth.prometheus import authorize

logger = logging.getLogger("main")
router = APIRouter(tags=["system-router"])


@router.get("/health")
async def healthcheck() -> bool:
    """Health check endpoint."""
    conn = Tortoise.get_connection("default")
    await conn.execute_query("SELECT 1;")
    return True


@router.get("/metrics", dependencies=[Depends(authorize)])
async def metrics(request: Request) -> Response:
    """Metrics  endpoint."""
    return handle_metrics(request)
