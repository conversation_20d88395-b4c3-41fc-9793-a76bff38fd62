from json import dumps
from typing import Optional

import pendulum
from fastapi import (
    APIRouter,
    Request,
    Response,
    status,
    HTTPException,
    Depends,
    Query,
)

from app.pydantic_model.transtek import (
    CarrierListResponse,
    UpdateTrackingDataResponse,
    TranstekResponse,
)
from ciba_iot_etl.models.pydantic.transtek import (
    TranstekStatusMessage,
    TranstekStatusTestMessage,
    TranstekTelemetryMessage,
    TranstekTelemetryTestMessage,
)
from ciba_iot_etl.repositories.transtek_repository import TranstekRepository
from app.routers.requests.api_request import UpdateTrackingDataRequest
from ciba_iot_etl.models.pydantic.common import TRACKING_URLS
from app.helpers.transtek import (
    get_device_info,
    get_member_by_request,
    save_device_pairing,
    update_device_status,
    update_device_telemetry,
    check_existing_transtek_device,
)
from app.log.logging import logger
from app.routers.requests.api_request import PairDeviceRequest
from app.services.queue import SendQueueException, get_queue
from app.settings import get_settings
from ciba_iot_etl.extract.transtek_api.core import MioConnectClient
from ciba_iot_etl.models.db.member import Member


router = APIRouter(prefix="/transtek", tags=["transtek-router"])

settings = get_settings()
HEADER_NAME = "x-ciba-transtek-api-key"
TRANSTEK_CIBA_KEY = settings.TRANSTEK_CIBA_KEY


def get_mio_connect_client() -> MioConnectClient:
    settings = get_settings()
    return MioConnectClient(api_key=settings.TRANSTEK_API_KEY)


@router.post("/pair_device")
async def pair_device(
    request_data: PairDeviceRequest,
    client: MioConnectClient = Depends(get_mio_connect_client),
):
    """Pair a device to a member"""
    logger.info(
        f"Starting device pairing process for member: {request_data.member_id}"
    )

    try:
        exists = await check_existing_transtek_device(
            imei=request_data.imei,
            device_id=request_data.serial_number,
        )
        if exists:
            raise HTTPException(
                status_code=409,
                detail="Device is already paired with another user.",
            )

        member = await get_member_by_request(request_data)
        device = await get_device_info(request_data, client)

        await save_device_pairing(member, device)

        logger.info(
            f"Device pairing completed successfully: "
            f"member_id={member.id}, device_id={device['serialNumber']}"
        )

        return {
            "paired": True,
            "device_id": device["serialNumber"],
            "member_id": str(member.id),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during device pairing: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred during device pairing",
        )


@router.post("/forwardtelemetry", status_code=status.HTTP_201_CREATED)
async def forward_telemetry(
    request: Request,
    message: (
        TranstekStatusMessage
        | TranstekTelemetryMessage
        | TranstekStatusTestMessage
        | TranstekTelemetryTestMessage
    ),
    response: Response,
):
    if (
        HEADER_NAME not in request.headers
        or request.headers[HEADER_NAME] != TRANSTEK_CIBA_KEY
    ):
        json_body = await request.json()
        body = dumps(
            json_body,
            indent=4,
            sort_keys=True,
        )
        logger.error(f"Unauthorized request to Transtek Endpoint: {body}")
        response.status_code = status.HTTP_401_UNAUTHORIZED
        return

    json_message = dumps(
        message, default=lambda x: x.__dict__, indent=4, sort_keys=True
    )

    if isinstance(
        message, (TranstekStatusTestMessage, TranstekTelemetryTestMessage)
    ):
        logger.info(f"Test Message from MioConnect: \n{json_message}")
    elif isinstance(message, TranstekStatusMessage):
        logger.info(f"Status from device: {json_message}")
        await update_device_status(message)
    elif isinstance(message, TranstekTelemetryMessage):
        logger.info(f"Telemetry from device: {json_message}")

        queue = get_queue(settings.SQS_DATA_NOTIFICATION_QUEUE)
        correlation_id = request.headers.get(
            "X-Request-ID", str(pendulum.now().int_timestamp)
        )

        try:
            await update_device_telemetry(message, queue, correlation_id)
        except SendQueueException as exception:
            error_message = f"sending notification to the queue failed for queue {settings.SQS_DATA_NOTIFICATION_QUEUE}"
            logger.exception(error_message, exc_info=exception)
    else:
        logger.error("Unhandled transtek message type")


@router.post(
    "/tracking-data",
    response_model=UpdateTrackingDataResponse,
    status_code=status.HTTP_200_OK,
)
async def update_tracking_data(
    request_data: UpdateTrackingDataRequest,
) -> UpdateTrackingDataResponse:
    """Update tracking data for a Transtek device and generate tracking URL.

    This endpoint updates the tracking information (tracking number and carrier) for a
    Transtek device and automatically sets the device status to 'shipped'. The device
    can be identified by either its serial_number or IMEI. If a member_type
    is provided, the response will include the corresponding platform member ID.

    Args:
        request_data (UpdateTrackingDataRequest): Request containing device identification
            and tracking information with the following fields:
            - serial_number (Optional[str]): Device serial number for identification
            - imei (Optional[str]): Device IMEI for identification
            - tracking_number (str): Shipping tracking number from carrier
            - carrier (str): Shipping carrier name (e.g., 'ups', 'usps', 'fedex', 'dhl')
            - member_type (Optional[PlatformType]): Platform type ('participant' or 'patient')
              for member lookup

    Returns:
        UpdateTrackingDataResponse: Response containing:
            - tracking_url (str): Generated tracking URL using carrier template
            - member_id (Optional[str]): Platform-specific member ID if member_type provided
            - member_type (Optional[str]): Platform type if member_type provided
            - message (str): Success message (default: "Tracking data updated successfully")

    Raises:
        HTTPException:
            - 400 Bad Request: If validation fails (e.g., neither serial_number nor imei
              provided, device not found, invalid carrier)
            - 500 Internal Server Error: If unexpected error occurs during processing
    """
    try:
        device = await TranstekRepository.update_tracking_data(
            device_id=request_data.serial_number,
            imei=request_data.imei,
            tracking_number=request_data.tracking_number,
            carrier=request_data.carrier,
        )

        if request_data.member_type and device.member:
            platform = next(
                (
                    p
                    for p in device.member.platforms
                    if p.platform_type == request_data.member_type.value
                ),
                None,
            )

            if platform:
                return UpdateTrackingDataResponse(
                    tracking_url=device.tracking_url,
                    member_id=str(platform.platform_id),
                    member_type=request_data.member_type.value,
                )

        return UpdateTrackingDataResponse(
            tracking_url=device.tracking_url,
            member_id=None,
            member_type=None,
        )

    except ValueError as e:
        logger.error(f"Validation error updating tracking data: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error updating tracking data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update tracking data",
        )


@router.get(
    "/carriers",
    response_model=CarrierListResponse,
    status_code=status.HTTP_200_OK,
)
async def get_carriers() -> CarrierListResponse:
    """
    Returns a list of supported carriers and their tracking link templates.
    """
    try:
        return CarrierListResponse(
            carriers=TRACKING_URLS,
        )
    except Exception as e:
        logger.error(f"Error retrieving carrier list: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve carrier list",
        )


@router.get(
    "/device",
    response_model=TranstekResponse,
    status_code=status.HTTP_200_OK,
)
async def get_device(
    member_id: Optional[str] = Query(
        None, description="Filter by platform member ID"
    ),
    member_type: Optional[str] = Query(
        None,
        description="Platform type (participant or patient), required if member_id is provided",
    ),
    device_id: Optional[str] = Query(None, description="Filter by device ID"),
    imei: Optional[str] = Query(None, description="Filter by IMEI"),
):
    """
    Get Transtek device by one of: member_id (with member_type), device_id, or imei.
    For member_id lookup, member_type is required and must be 'participant' or 'patient'.
    """
    if bool(member_id) != bool(member_type):
        raise HTTPException(
            status_code=400,
            detail="member_id and member_type must be provided together",
        )

    search_params = [member_id, device_id, imei]
    provided_count = sum(1 for param in search_params if param is not None)

    if provided_count != 1:
        raise HTTPException(
            status_code=400,
            detail="One parameter is required: member_id (with member_type), device_id, or imei",
        )

    try:
        device = None

        if member_id is not None:
            # First step: Get the member using platform information
            member = await Member.get_by_platform(
                platform_type=member_type, platform_id=member_id
            )
            if member is None:
                raise HTTPException(status_code=404, detail="Member not found")

            # Second step: Get the Transtek device for this member
            device = await TranstekRepository.get_device_by_member_id(
                str(member.id)
            )
        elif device_id is not None:
            device = await TranstekRepository.get_device_by_device_id(
                device_id
            )
        elif imei is not None:
            device = await TranstekRepository.get_device_by_imei(imei)

        if device is None:
            raise HTTPException(status_code=404, detail="Device not found")

        response_data = {
            **device.__dict__,
            "tracking_url": device.tracking_url,
        }
        return TranstekResponse.model_validate(response_data)

    except HTTPException:
        # Re-raise HTTPExceptions (like 404) without modification
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Internal server error: {str(e)}"
        )
