"""Manager API Router.

This module provides API endpoints for managing users and their connected devices.
It includes functionality for:
- Retrieving user information
- Managing device connections (Withings, Fitbit)
- Syncing and refreshing device data
- Checking device health status
"""

# Standard library imports
import json

from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

# Third-party imports
import pendulum
from fastapi import APIRouter, HTTPException, Request, Security
from fastapi.responses import HTMLResponse
from fastapi.security import APIKeyHeader
from fastapi.templating import Jinja2Templates

# Local application imports
from app.auth.constants import X_AUTH_KEY
from app.log.logging import logger
from app.routers.api import disconnect_user, sync_user
from app.routers.requests.api_request import DisconnectRequets, SyncRequets
from app.settings import get_settings
from ciba_iot_etl.extract.fitbit_api.core import FitbitLoader
from ciba_iot_etl.extract.fitbit_api.processor import fetch_fitbit_data
from ciba_iot_etl.extract.withings_api.core import WithingsLoader
from ciba_iot_etl.extract.withings_api.processor import (
    WithingsToTortoiseConverter,
    process_user_message,
)
from ciba_iot_etl.models.db.activity import Activity
from ciba_iot_etl.models.db.blood_pressure import BloodPressure
from ciba_iot_etl.models.db.fitbit import Fitbit
from ciba_iot_etl.models.db.heart_rate import HeartRate
from ciba_iot_etl.models.db.member import Member
from ciba_iot_etl.models.db.member_platform import MemberPlatform
from ciba_iot_etl.models.db.sleep import Sleep
from ciba_iot_etl.models.db.weight import Weight
from ciba_iot_etl.models.db.withings import Withings
from ciba_iot_etl.models.pydantic.common import (
    ActivityDevice,
    Platform,
    PlatformType,
    PullDataNotification,
)
from ciba_iot_etl.models.pydantic.fitbit import FitbitPayload
from ciba_iot_etl.models.pydantic.withings import WithingsPayload

# Initialize settings and templates
settings = get_settings()
cur_dir = Path(__file__).parent
templates_dir = cur_dir.parent / "templates"
templates = Jinja2Templates(directory=str(templates_dir))

# Define routers
router = APIRouter(
    prefix="/manager",
    dependencies=[
        Security(APIKeyHeader(name=X_AUTH_KEY)),
    ],
)
ui_router = APIRouter(prefix="/manager/ui", tags=["manager-ui"])


@router.get("/users", tags=["manager"])
async def get_users(
    activity_device: Optional[ActivityDevice] = None,
) -> List[Dict[str, Any]]:
    """Get a list of users, optionally filtered by activity device.

    Args:
        activity_device: Optional filter for users with a specific device type

    Returns:
        List of user dictionaries with id and email
    """
    devices = []
    if activity_device == ActivityDevice.WITHINGS:
        devices = await Withings.all().values("id", "member_id")
    elif activity_device == ActivityDevice.FITBIT:
        devices = await Fitbit.all().values("id", "member_id")

    if activity_device and devices:
        member_ids = [device["member_id"] for device in devices]
        members = await Member.filter(id__in=member_ids).values("id", "email")
    else:
        members = await Member.all().values("id", "email")

    return members


@router.get("/users/{email}", tags=["manager"])
async def get_user(email: str, activities: bool = False) -> Dict[str, Any]:
    """Get detailed information about a specific user.

    Args:
        email: The email address of the user to retrieve
        activities: Whether to include recent activities data

    Returns:
        Dictionary with user details and connected devices
    """
    member = await Member.get(email=email)
    if not member:
        raise HTTPException(status_code=404, detail=f"User not found: {email}")

    withings = await Withings.filter(member_id=member.id).first()
    fitbit = await Fitbit.filter(member_id=member.id).first()
    member_platforms = await MemberPlatform.filter(member_id=member.id).all()

    activity_models = {
        "patient": [Activity, Sleep, Weight, HeartRate, BloodPressure],
        # "participant": [ParticipantActivity]
    }

    activities_data = {}
    if activities:
        for platform in member_platforms:
            if platform.platform_type == PlatformType.participant.value:
                continue

            for activity_model in activity_models.get(
                platform.platform_type, []
            ):
                activities_data[
                    f"{platform.platform_type}_{activity_model.__name__}"
                ] = (
                    await activity_model.filter(member_id=member.id)
                    .order_by("-created_at")
                    .limit(5)
                )

    return {
        "id": member.id,
        "email": member.email,
        "withings": withings,
        "fitbit": fitbit,
        "member_platforms": member_platforms,
        "activities": activities_data,
    }


async def get_user_data(
    email: str,
    device: ActivityDevice,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
) -> Dict[str, Any]:
    """Fetch user data from a specific device for a given time period.

    Args:
        email: User's email address
        device: Device type to fetch data from (Withings or Fitbit)
        start_date: Start date for data retrieval (defaults to 10 days ago)
        end_date: End date for data retrieval (defaults to current time)

    Returns:
        Dictionary containing the retrieved data

    Raises:
        HTTPException: If user not found or device is invalid
    """
    # Set default date range if not provided
    start_date = (
        pendulum.now().subtract(days=10)
        if not start_date
        else pendulum.instance(start_date)
    )
    end_date = pendulum.now() if not end_date else pendulum.instance(end_date)

    # Get member
    member = await Member.get(email=email)
    if not member:
        raise HTTPException(status_code=404, detail=f"User not found: {email}")

    # Process based on device type
    if device == ActivityDevice.WITHINGS:
        return await _get_withings_data(member, start_date, end_date)
    elif device == ActivityDevice.FITBIT:
        return await _get_fitbit_data(member, start_date, end_date)
    else:
        raise HTTPException(
            status_code=400, detail=f"Invalid device: {device}"
        )


async def _get_withings_data(
    member: Member, start_date: pendulum.DateTime, end_date: pendulum.DateTime
) -> Dict[str, Any]:
    """Helper function to fetch Withings data for a user.

    Args:
        member: Member object
        start_date: Start date for data retrieval
        end_date: End date for data retrieval

    Returns:
        Processed Withings data
    """
    # Get Withings device
    withings = await Withings.filter(member_id=member.id).first()
    if not withings:
        raise HTTPException(
            status_code=404,
            detail=f"Withings device not found for user: {member.email}",
        )

    # Initialize Withings client
    withings_client = WithingsLoader(
        client_id=settings.WITHINGS_CLIENT_ID,
        client_secret=settings.WITHINGS_CUSTOMER_SECRET,
        redirect_uri=settings.WITHINGS_REDIRECT_URI,
        callback_uri=settings.WITHINGS_CALLBACK_URI,
        notification_callback_uri=settings.WITHINGS_NOTIFICATION_CALLBACK_URI,
        scope=settings.WITHINGS_SCOPE,
    )

    # Create payload
    withings_payload = WithingsPayload(
        withings_id=str(withings.id),
        meastypes=[1, 10, 9, 11],
        category=1,
        start_date=start_date.int_timestamp,
        end_date=end_date.int_timestamp,
    )

    # Create notification message
    message = PullDataNotification(
        member_id=str(member.id),
        activity_device=ActivityDevice.WITHINGS,
        platforms=[Platform(id=str(member.id), type=PlatformType.patient)],
        payload=withings_payload,
        corellation_id="-",
    )

    # Process data
    withings_data = await process_user_message(
        withings_service=withings_client,
        message=message,
        withings_id=str(withings.id),
    )

    # Convert to database format
    return await WithingsToTortoiseConverter(member.id).convert(withings_data)


async def _get_fitbit_data(
    member: Member, start_date: pendulum.DateTime, end_date: pendulum.DateTime
) -> Dict[str, Any]:
    """Helper function to fetch Fitbit data for a user.

    Args:
        member: Member object
        start_date: Start date for data retrieval
        end_date: End date for data retrieval

    Returns:
        Processed Fitbit data
    """
    # Get Fitbit device
    fitbit = await Fitbit.filter(member_id=member.id).first()
    if not fitbit:
        raise HTTPException(
            status_code=404,
            detail=f"Fitbit device not found for user: {member.email}",
        )

    # Initialize Fitbit service
    fitbit_service = FitbitLoader(
        client_id=settings.FITBIT_CLIENT_ID,
        client_secret=settings.FITBIT_CLIENT_SECRET,
        redirect_uri=settings.FITBIT_REDIRECT_URI,
    )

    # Check if token refresh is needed
    if fitbit.is_access_token_expired():
        fitbit = await _refresh_fitbit_token(fitbit, fitbit_service)

    # Create payload
    fitbit_payload = FitbitPayload(
        start_date=start_date.int_timestamp,
        end_date=end_date.int_timestamp,
        fitbit_id=str(fitbit.id),
        scope=settings.FITBIT_SCOPE.split(" "),
    )

    # Fetch data
    return await fetch_fitbit_data(
        access_token=fitbit.access_token,
        fitbit_service=fitbit_service,
        fitbit_payload=fitbit_payload,
    )


async def _refresh_fitbit_token(
    fitbit: Fitbit, fitbit_service: FitbitLoader
) -> Fitbit:
    """Refresh Fitbit access token if expired.

    Args:
        fitbit: Fitbit device object
        fitbit_service: Initialized FitbitLoader service

    Returns:
        Updated Fitbit object with refreshed token

    Raises:
        HTTPException: If token refresh fails
    """
    logger.info(
        f"Access token expired for Fitbit user {fitbit.id}, refreshing..."
    )

    try:
        # Use standardized token refresh with fallback from ciba-iot-etl
        from ciba_iot_etl.extract.common.token_refresh import (
            refresh_fitbit_token_with_fallback,
        )
        from ciba_iot_etl.monitoring.token_metrics import (
            log_token_refresh_metrics,
        )

        result_obj = await refresh_fitbit_token_with_fallback(
            fitbit_service=fitbit_service,
            fitbit_id=str(fitbit.id),
            fitbit_connection=fitbit,
        )

        # Log metrics using standardized monitoring
        log_token_refresh_metrics(
            result=result_obj,
            platform="fitbit",
            connection_id=str(fitbit.id),
            additional_context={"endpoint": "refresh_fitbit_token"},
        )

        # Convert to legacy format for compatibility
        if result_obj.success:
            # Create a mock object with the expected attributes
            class MockRefreshResult:
                def __init__(self, token_data):
                    self.access_token = token_data.get("access_token")
                    self.refresh_token = token_data.get("refresh_token")
                    self.expires_in = token_data.get("expires_in")
                    self.error = None

            refresh_result = MockRefreshResult(result_obj.token_data)
        else:
            # Create a mock object with error
            class MockRefreshResult:
                def __init__(self, error_message):
                    self.access_token = None
                    self.refresh_token = None
                    self.expires_in = None
                    self.error = error_message

            refresh_result = MockRefreshResult(result_obj.error_message)

        if refresh_result.error:
            from app.services.token_error_handler import TokenErrorHandler

            error_message = str(refresh_result.error)
            error_type = TokenErrorHandler.classify_error(error_message)
            requires_reauth = TokenErrorHandler.requires_reauth(error_type)

            logger.error(f"Error refreshing token: {error_message}")
            await Fitbit.filter(id=fitbit.id).update(healthy=False)

            TokenErrorHandler.log_token_operation(
                operation="refresh",
                platform="Fitbit",
                connection_id=str(fitbit.id),
                success=False,
                error=error_message,
                additional_context={"endpoint": "refresh_fitbit_token"},
            )

            if requires_reauth:
                error_msg = "Refresh token is invalid. User needs to re-authenticate with Fitbit."
                logger.error(f"{error_msg} - Fitbit user {fitbit.id}")
                raise HTTPException(status_code=401, detail=error_msg)
            else:
                raise HTTPException(
                    status_code=500,
                    detail=f"Error refreshing Fitbit token: {error_message}",
                )

        # Validate token response
        if (
            not refresh_result.access_token
            or not refresh_result.refresh_token
            or refresh_result.expires_in is None
        ):
            raise HTTPException(
                status_code=500,
                detail="Invalid token response from Fitbit API",
            )

        # Update the tokens in the database
        await Fitbit.update_tokens(
            fitbit_id=str(fitbit.id),
            access_token=refresh_result.access_token,
            refresh_token=refresh_result.refresh_token,
            expires_in=refresh_result.expires_in,
        )

        # Get the updated Fitbit record
        updated_fitbit = await Fitbit.get(id=fitbit.id)
        logger.info(
            f"Token refreshed successfully for Fitbit user {fitbit.id}"
        )
        return updated_fitbit

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Failed to refresh Fitbit token: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to refresh Fitbit token: {str(e)}"
        )


@router.get("/users/{email}/data/{device}", tags=["manager"])
async def get_user_data_api(
    email: str,
    device: ActivityDevice,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
) -> Dict[str, Any]:
    """API endpoint to fetch user data from a specific device.

    Args:
        email: User's email address
        device: Device type to fetch data from
        start_date: Optional start date for data retrieval
        end_date: Optional end date for data retrieval

    Returns:
        Dictionary containing the retrieved data
    """
    return await get_user_data(email, device, start_date, end_date)


@ui_router.get("/users", response_class=HTMLResponse, tags=["manager-ui"])
async def ui_users(request: Request):
    # Call your existing API logic. If get_users is asynchronous and returns a list of user dictionaries:
    users = await get_users()
    return templates.TemplateResponse(
        "users.html", {"request": request, "users": users}
    )


@ui_router.get(
    "/users/{email}", response_class=HTMLResponse, tags=["manager-ui"]
)
async def ui_user_detail(request: Request, email: str):
    # Set activities=True if you want to fetch recent activity data as well
    user_detail = await get_user(email=email, activities=True)
    return templates.TemplateResponse(
        "user_detail.html", {"request": request, "user": user_detail}
    )


@ui_router.get("/users/{email}/disconnect", tags=["manager-ui"])
async def ui_disconnect_user(
    request: Request, email: str, device: ActivityDevice
):
    member = await Member.get(email=email)
    member_platform = await MemberPlatform.filter(member_id=member.id).all()
    for platform in member_platform:
        request = DisconnectRequets(
            member_id=str(platform.platform_id),
            type_device=device,
            member_type=platform.platform_type,
        )
        await disconnect_user(request)

    return {"disconnected": True}


@ui_router.get("/users/{email}/sync", tags=["manager-ui"])
async def ui_sync_user(
    email: str, device: ActivityDevice, start_date: datetime = None
):
    start_date = (
        pendulum.now().subtract(days=10)
        if not start_date
        else pendulum.instance(start_date)
    )

    member = await Member.get(email=email)
    member_platform = await MemberPlatform.filter(member_id=member.id).all()
    for platform in member_platform:
        sync_request = SyncRequets(
            member_id=str(platform.platform_id),
            type_device=device,
            member_type=platform.platform_type,
            start_date=start_date.int_timestamp,
        )
        await sync_user(sync_request, "-")
    return {"synced": True}


@ui_router.get("/users/{email}/refresh-token", tags=["manager-ui"])
async def ui_refresh_user_token(email: str, device: ActivityDevice):
    """Refresh the access token for a specific user and device."""
    member = await Member.get(email=email)
    if not member:
        return {"success": False, "error": f"Member not found: {email}"}

    result = {"success": False}

    if device == ActivityDevice.FITBIT:
        fitbit = await Fitbit.filter(member_id=member.id).first()
        if not fitbit:
            return {
                "success": False,
                "error": f"Fitbit connection not found for member: {email}",
            }

        # Initialize Fitbit service
        fitbit_service = FitbitLoader(
            client_id=settings.FITBIT_CLIENT_ID,
            client_secret=settings.FITBIT_CLIENT_SECRET,
            redirect_uri=settings.FITBIT_REDIRECT_URI,
        )

        try:
            # Use standardized token refresh with fallback from ciba-iot-etl
            from ciba_iot_etl.extract.common.token_refresh import (
                refresh_fitbit_token_with_fallback,
            )
            from ciba_iot_etl.monitoring.token_metrics import (
                log_token_refresh_metrics,
            )

            result_obj = await refresh_fitbit_token_with_fallback(
                fitbit_service=fitbit_service,
                fitbit_id=str(fitbit.id),
                fitbit_connection=fitbit,
            )

            # Log metrics using standardized monitoring
            log_token_refresh_metrics(
                result=result_obj,
                platform="fitbit",
                connection_id=str(fitbit.id),
                additional_context={"endpoint": "ui_refresh_user_token"},
            )

            # Convert to legacy format for compatibility
            if result_obj.success:
                # Create a mock object with the expected attributes
                class MockRefreshResult:
                    def __init__(self, token_data):
                        self.access_token = token_data.get("access_token")
                        self.refresh_token = token_data.get("refresh_token")
                        self.expires_in = token_data.get("expires_in")
                        self.error = None

                refresh_result = MockRefreshResult(result_obj.token_data)
            else:
                # Create a mock object with error
                class MockRefreshResult:
                    def __init__(self, error_message):
                        self.access_token = None
                        self.refresh_token = None
                        self.expires_in = None
                        self.error = error_message

                refresh_result = MockRefreshResult(result_obj.error_message)

            if refresh_result.error:
                from app.services.token_error_handler import TokenErrorHandler

                error_message = str(refresh_result.error)
                error_type = TokenErrorHandler.classify_error(error_message)
                requires_reauth = TokenErrorHandler.requires_reauth(error_type)

                logger.error(f"Error refreshing token: {error_message}")
                await Fitbit.filter(id=fitbit.id).update(healthy=False)

                TokenErrorHandler.log_token_operation(
                    operation="refresh",
                    platform="Fitbit",
                    connection_id=str(fitbit.id),
                    success=False,
                    error=error_message,
                    additional_context={"endpoint": "ui_refresh_user_token"},
                )

                if requires_reauth:
                    result["error"] = (
                        "Refresh token is invalid. User needs to re-authenticate with Fitbit."
                    )
                    result["requires_reauth"] = True
                else:
                    result["error"] = (
                        f"Error refreshing token: {error_message}"
                    )
                    result["requires_reauth"] = False

                return result

            # Validate token response
            if (
                not refresh_result.access_token
                or not refresh_result.refresh_token
                or refresh_result.expires_in is None
            ):
                result["error"] = "Invalid token response from Fitbit API"
                return result

            # Update the tokens in the database
            updated_fitbit = await Fitbit.update_tokens(
                fitbit_id=str(fitbit.id),
                access_token=refresh_result.access_token,
                refresh_token=refresh_result.refresh_token,
                expires_in=refresh_result.expires_in,
            )

            # Mark as healthy since token refresh was successful
            await Fitbit.filter(id=fitbit.id).update(healthy=True)

            result["success"] = True
            result["message"] = "Fitbit token refreshed successfully"
            result["expires_at"] = str(updated_fitbit.access_token_expires_at)
            logger.info(
                f"Token refreshed successfully for Fitbit user {fitbit.id}"
            )
        except Exception as e:
            logger.error(f"Failed to refresh Fitbit token: {str(e)}")
            result["error"] = f"Failed to refresh token: {str(e)}"

    elif device == ActivityDevice.WITHINGS:
        withings = await Withings.filter(member_id=member.id).first()
        if not withings:
            return {
                "success": False,
                "error": f"Withings connection not found for member: {email}",
            }

        # Initialize Withings service
        withings_service = WithingsLoader(
            client_id=settings.WITHINGS_CLIENT_ID,
            client_secret=settings.WITHINGS_CUSTOMER_SECRET,
            redirect_uri=settings.WITHINGS_REDIRECT_URI,
            callback_uri=settings.WITHINGS_CALLBACK_URI,
            notification_callback_uri=settings.WITHINGS_NOTIFICATION_CALLBACK_URI,
            scope=settings.WITHINGS_SCOPE,
        )

        try:
            # Attempt to refresh the token
            refresh_result = await withings_service.refresh_token(
                withings.refresh_token
            )

            if "error" in refresh_result:
                logger.error(
                    f"Error refreshing Withings token: {refresh_result['error']}"
                )
                await Withings.filter(id=withings.id).update(healthy=False)

                # Check if this is an invalid_grant error (invalid refresh token)
                if (
                    "invalid_grant"
                    in str(refresh_result.get("error", "")).lower()
                ):
                    result["error"] = (
                        "Refresh token is invalid. User needs to re-authenticate with Withings."
                    )
                    result["requires_reauth"] = True
                else:
                    result["error"] = (
                        f"Error refreshing token: {refresh_result['error']}"
                    )

                return result

            # Extract new tokens
            new_access_token = refresh_result.get("access_token")
            new_refresh_token = refresh_result.get("refresh_token")

            if not new_access_token or not new_refresh_token:
                result["error"] = "Invalid token response from Withings API"
                return result

            # Update the tokens in the database
            updated_withings = await Withings.update_tokens(
                withings_id=str(withings.id),
                access_token=new_access_token,
                refresh_token=new_refresh_token,
                expires_in=refresh_result.get(
                    "expires_in", 10800
                ),  # Default to 3 hours if not provided
            )

            # Mark as healthy since token refresh was successful
            await Withings.filter(id=withings.id).update(healthy=True)

            result["success"] = True
            result["message"] = "Withings token refreshed successfully"
            result["expires_at"] = str(
                updated_withings.access_token_expires_at
            )
            logger.info(
                f"Token refreshed successfully for Withings user {withings.id}"
            )
        except Exception as e:
            logger.error(f"Failed to refresh Withings token: {str(e)}")
            result["error"] = f"Failed to refresh token: {str(e)}"

    else:
        result["error"] = f"Unsupported device type: {device}"

    return result


@ui_router.get("/users/{email}/data/{device}", tags=["manager-ui"])
async def ui_get_user_data(
    email: str,
    device: ActivityDevice,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
) -> Dict[str, Any]:
    """UI endpoint to fetch user data from a specific device.

    Args:
        email: User's email address
        device: Device type to fetch data from
        start_date: Optional start date for data retrieval
        end_date: Optional end date for data retrieval

    Returns:
        Dictionary containing the retrieved data
    """
    return await get_user_data(email, device, start_date, end_date)


@ui_router.get("/users/{email}/device-info/{device}", tags=["manager-ui"])
async def ui_get_device_info(
    email: str, device: ActivityDevice
) -> Dict[str, Any]:
    """Get detailed information about the user's physical device.

    Args:
        email: User's email address
        device: Device type to get information for

    Returns:
        Dictionary containing device information or error details
    """
    member = await Member.get(email=email)
    if not member:
        return {"success": False, "error": f"Member not found: {email}"}

    result: Dict[str, Any] = {"success": False}

    if device == ActivityDevice.WITHINGS:
        withings = await Withings.filter(member_id=member.id).first()
        if not withings:
            return {
                "success": False,
                "error": f"Withings connection not found for member: {email}",
            }

        # Initialize Withings service
        withings_service = WithingsLoader(
            client_id=settings.WITHINGS_CLIENT_ID,
            client_secret=settings.WITHINGS_CUSTOMER_SECRET,
            redirect_uri=settings.WITHINGS_REDIRECT_URI,
            callback_uri=settings.WITHINGS_CALLBACK_URI,
            notification_callback_uri=settings.WITHINGS_NOTIFICATION_CALLBACK_URI,
            scope=settings.WITHINGS_SCOPE,
        )

        try:
            # Call the getdevice API
            device_info = await withings_service._call_api_with_rate_limit_handling(
                url=f"{withings_service.API_URL}/{withings_service.PATH_V2_USER}",
                method="POST",
                headers=withings_service.get_auth_headers(
                    withings.access_token
                ),
                payload={"action": "getdevice"},
            )

            if device_info.get("status") == 0:  # Success status
                result["success"] = True

                # If API call was successful, expire old refresh token
                await Withings.expire_old_refresh_token(str(withings.id))
                logger.info(
                    f"Expired old_refresh_token for Withings {withings.id} after successful getdevice API call"
                )

                # Get the devices list
                devices = device_info.get("body", {}).get("devices", [])

                # Format timestamp fields to human-readable dates
                for i, device_info in enumerate(devices):
                    # Convert first_session_date and last_session_date to human-readable format
                    if (
                        "first_session_date" in device_info
                        and device_info["first_session_date"]
                    ):
                        try:
                            timestamp = int(device_info["first_session_date"])
                            devices[i]["first_session_date"] = (
                                pendulum.from_timestamp(
                                    timestamp
                                ).to_datetime_string()
                            )
                        except (ValueError, TypeError):
                            # Keep original value if conversion fails
                            pass

                    if (
                        "last_session_date" in device_info
                        and device_info["last_session_date"]
                    ):
                        try:
                            timestamp = int(device_info["last_session_date"])
                            devices[i]["last_session_date"] = (
                                pendulum.from_timestamp(
                                    timestamp
                                ).to_datetime_string()
                            )
                        except (ValueError, TypeError):
                            # Keep original value if conversion fails
                            pass

                result["devices"] = devices
            else:
                error_code = device_info.get("status")
                error_message = (
                    f"Error getting device info. Status code: {error_code}"
                )

                # Check for common error codes
                if error_code == 401:
                    error_message = (
                        "Authentication error. Token may be expired."
                    )
                elif error_code == 601:
                    error_message = "Rate limit exceeded."

                result["error"] = error_message

        except Exception as e:
            logger.error(f"Failed to get Withings device info: {str(e)}")
            result["error"] = f"Failed to get device info: {str(e)}"

    elif device == ActivityDevice.FITBIT:
        fitbit = await Fitbit.filter(member_id=member.id).first()
        if not fitbit:
            return {
                "success": False,
                "error": f"Fitbit connection not found for member: {email}",
            }

        # Initialize Fitbit service
        fitbit_service = FitbitLoader(
            client_id=settings.FITBIT_CLIENT_ID,
            client_secret=settings.FITBIT_CLIENT_SECRET,
            redirect_uri=settings.FITBIT_REDIRECT_URI,
        )

        try:
            # Call the Fitbit devices API
            device_info = (
                await fitbit_service._call_api_with_rate_limit_handling(
                    method="GET",
                    url=f"{fitbit_service.API_URL}/1/user/-/devices.json",
                    headers={"Authorization": f"Bearer {fitbit.access_token}"},
                )
            )

            # Log the response for debugging
            logger.info(f"Fitbit device info response: {device_info}")

            if "error" in device_info:
                error_code = device_info.get("error", {}).get(
                    "type", "unknown_error"
                )
                error_message = device_info.get("error", {}).get(
                    "message", "Unknown error"
                )

                # Check for common error codes
                if (
                    error_code == "expired_token"
                    or error_code == "invalid_token"
                ):
                    error_message = (
                        "Authentication error. Token may be expired."
                    )
                elif error_code == "rate_limit_exceeded":
                    error_message = "Rate limit exceeded."

                result["error"] = error_message
            else:
                result["success"] = True
                # Fitbit API returns an array directly, so we need to wrap it in a devices property
                # to match the Withings format
                if isinstance(device_info, list):
                    result["devices"] = device_info
                else:
                    # Ensure we always return a list of devices
                    result["devices"] = []

        except Exception as e:
            logger.error(f"Failed to get Fitbit device info: {str(e)}")
            result["error"] = f"Failed to get device info: {str(e)}"

    else:
        result["error"] = f"Unsupported device type: {device}"

    return result


async def check_token_health(
    activity_device: ActivityDevice,
) -> Dict[str, bool]:
    """Check the health of device tokens by attempting to fetch data.

    Args:
        activity_device: The type of device to check (Withings or Fitbit)

    Returns:
        Dictionary mapping user emails to token health status (True if healthy)
    """
    end_date = pendulum.now()
    start_date = end_date.subtract(days=1)
    device_data: Dict[str, bool] = {}

    if activity_device == ActivityDevice.WITHINGS:
        withings_devices = await Withings.all()
        for device in withings_devices:
            member = await device.member

            try:
                await get_user_data(
                    member.email, activity_device, start_date, end_date
                )
                device_data[member.email] = True
            except Exception as e:
                logger.error(f"Error fetching data for {member.email} - {e}")
                device_data[member.email] = False

    elif activity_device == ActivityDevice.FITBIT:
        fitbit_devices = await Fitbit.all()
        fitbit_service = FitbitLoader(
            client_id=settings.FITBIT_CLIENT_ID,
            client_secret=settings.FITBIT_CLIENT_SECRET,
            redirect_uri=settings.FITBIT_REDIRECT_URI,
        )

        for device in fitbit_devices:
            member = await device.member
            try:
                # Check if token is expired and refresh if needed
                if device.is_access_token_expired():
                    logger.info(
                        f"Access token expired for Fitbit user {device.id}, refreshing..."
                    )
                    try:
                        # Attempt to refresh the token
                        refresh_result = await fitbit_service.refresh_token(
                            device.refresh_token
                        )

                        if refresh_result.error:
                            logger.error(
                                f"Error refreshing token: {refresh_result.error}"
                            )
                            await Fitbit.filter(id=device.id).update(
                                healthy=False
                            )
                            device_data[member.email] = False
                            continue

                        # Update the tokens in the database
                        if (
                            refresh_result.access_token
                            and refresh_result.refresh_token
                            and refresh_result.expires_in
                        ):
                            await Fitbit.update_tokens(
                                fitbit_id=str(device.id),
                                access_token=refresh_result.access_token,
                                refresh_token=refresh_result.refresh_token,
                                expires_in=refresh_result.expires_in,
                            )
                            logger.info(
                                f"Token refreshed successfully for Fitbit user {device.id}"
                            )
                        else:
                            logger.error("Incomplete token refresh response")
                            device_data[member.email] = False
                            continue

                    except Exception as e:
                        logger.error(
                            f"Failed to refresh Fitbit token: {str(e)}"
                        )
                        device_data[member.email] = False
                        continue

                # Try to fetch data with the current or refreshed token
                await get_user_data(
                    member.email, activity_device, start_date, end_date
                )
                device_data[member.email] = True
            except Exception as e:
                logger.error(f"Error fetching data for {member.email} - {e}")
                device_data[member.email] = False

    # Save results to file for debugging
    with open("fitbit.json", "w") as file_j:
        file_j.write(json.dumps(device_data))

    return device_data


async def get_devices_health(
    activity_device: ActivityDevice,
) -> Dict[str, bool]:
    """Get a list of devices with unhealthy tokens.

    Args:
        activity_device: The type of device to check (Withings or Fitbit)

    Returns:
        Dictionary mapping user emails to token health status (always False for unhealthy devices)
    """
    device_data: Dict[str, bool] = {}

    if activity_device == ActivityDevice.WITHINGS:
        withings_devices = await Withings.filter(healthy=False).all()
        for device in withings_devices:
            member = await device.member
            device_data[member.email] = False

    elif activity_device == ActivityDevice.FITBIT:
        fitbit_devices = await Fitbit.filter(healthy=False).all()
        for device in fitbit_devices:
            member = await device.member
            device_data[member.email] = False

    return device_data


@router.get("/{device}/health/token", tags=["manager"])
async def check_health(device: ActivityDevice):
    token_data = await check_token_health(device)
    return token_data


@router.get("/{device}/health", tags=["manager"])
async def check_devices_health(device: ActivityDevice):
    token_data = await get_devices_health(device)
    return token_data


@router.get("/test-refresh/{email}", tags=["manager"])
async def test_token_refresh(email: str) -> Dict[str, Any]:
    """Test endpoint to refresh Fitbit token for a specific user.

    Args:
        email: User's email address

    Returns:
        Dictionary with token refresh status and details

    Raises:
        HTTPException: If user or Fitbit device not found
    """
    member = await Member.get(email=email)
    if not member:
        raise HTTPException(
            status_code=404, detail=f"Member not found: {email}"
        )

    fitbit = await Fitbit.filter(member_id=member.id).first()
    if not fitbit:
        raise HTTPException(
            status_code=404,
            detail=f"Fitbit connection not found for member: {email}",
        )

    result: Dict[str, Any] = {
        "fitbit_id": str(fitbit.id),
        "access_token_expires_at": str(fitbit.access_token_expires_at),
        "is_access_token_expired": fitbit.is_access_token_expired(),
        "token_refreshed": False,
    }

    # Initialize Fitbit service
    fitbit_service = FitbitLoader(
        client_id=settings.FITBIT_CLIENT_ID,
        client_secret=settings.FITBIT_CLIENT_SECRET,
        redirect_uri=settings.FITBIT_REDIRECT_URI,
    )

    # Check if token is expired and refresh if needed
    if fitbit.is_access_token_expired():
        logger.info(
            f"Access token expired for Fitbit user {fitbit.id}, refreshing..."
        )
        try:
            # Attempt to refresh the token
            refresh_result = await fitbit_service.refresh_token(
                fitbit.refresh_token
            )

            if refresh_result.error:
                logger.error(f"Error refreshing token: {refresh_result.error}")
                await Fitbit.filter(id=fitbit.id).update(healthy=False)
                result["error"] = (
                    f"Error refreshing token: {refresh_result.error}"
                )
                return result

            # Update the tokens in the database
            if (
                refresh_result.access_token
                and refresh_result.refresh_token
                and refresh_result.expires_in
            ):
                updated_fitbit = await Fitbit.update_tokens(
                    fitbit_id=str(fitbit.id),
                    access_token=refresh_result.access_token,
                    refresh_token=refresh_result.refresh_token,
                    expires_in=refresh_result.expires_in,
                )

                result["token_refreshed"] = True
                result["new_access_token_expires_at"] = str(
                    updated_fitbit.access_token_expires_at
                )
                result["is_access_token_expired_after_refresh"] = (
                    updated_fitbit.is_access_token_expired()
                )
                logger.info(
                    f"Token refreshed successfully for Fitbit user {fitbit.id}"
                )
            else:
                logger.error("Incomplete token refresh response")
                result["error"] = "Incomplete token refresh response"
                await Fitbit.filter(id=fitbit.id).update(healthy=False)
        except Exception as e:
            logger.error(f"Failed to refresh Fitbit token: {str(e)}")
            result["error"] = f"Failed to refresh token: {str(e)}"
    else:
        result["message"] = "Access token is still valid, no refresh needed"

    return result


@ui_router.get("/devices_health/{activity_device}", tags=["manager-ui"])
async def ui_get_devices_health(
    activity_device: ActivityDevice,
) -> Dict[str, bool]:
    """UI endpoint to get devices with unhealthy tokens.

    Args:
        activity_device: The type of device to check

    Returns:
        Dictionary mapping user emails to token health status
    """
    return await get_devices_health(activity_device)
