from typing import Optional
from pydantic import BaseModel

from ciba_iot_etl.models.pydantic.common import ActivityDevice, PlatformType


class SyncRequets(BaseModel):
    type_device: ActivityDevice
    member_type: PlatformType
    member_id: str
    start_date: int = 0

    model_config = {"arbitrary_types_allowed": True}


class DisconnectRequets(BaseModel):
    type_device: ActivityDevice
    member_type: PlatformType
    member_id: str
    device_id: Optional[str] = None
    model_config = {"arbitrary_types_allowed": True}


class StatusRequest(BaseModel):
    members: list[str] = []
    member_type: PlatformType

    model_config = {"arbitrary_types_allowed": True}


class PairDeviceRequest(BaseModel):
    member_id: str
    member_type: PlatformType
    imei: Optional[str] = None
    serial_number: Optional[str] = None
    email: str

    model_config = {"arbitrary_types_allowed": True}


class UpdateTrackingDataRequest(BaseModel):
    imei: Optional[str] = None
    serial_number: Optional[str] = None
    tracking_number: str
    carrier: str
    member_type: Optional[PlatformType] = None

    model_config = {"arbitrary_types_allowed": True}


class GetDeviceRequest(BaseModel):
    serial_number: Optional[str] = None
    imei: Optional[str] = None

    model_config = {"arbitrary_types_allowed": True}
