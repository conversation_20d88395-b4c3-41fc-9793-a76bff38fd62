import logging

from fastapi import APIRouter, Security
from fastapi.security import <PERSON><PERSON>eyHeader

from app.auth.constants import X_AUTH_KEY
from app.pydantic_model.base import EmailList
from app.services.activity.handler import ActivityService
from app.services.weight.handler import WeightService
from app.settings import get_settings

logger = logging.getLogger("main")

settings = get_settings()

router = APIRouter(
    prefix="/user_data",
    tags=["api-router"],
    dependencies=[
        # Depends(verify_token),
        Security(APIKeyHeader(name=X_AUTH_KEY)),
    ],
)


@router.post("/daily_weight")
async def daily_weight(
    email_list: EmailList,
) -> EmailList:
    """Fetch and return daily weight"""
    response = await WeightService().handle(
        email_list,
    )
    return response


@router.post("/daily_activity")
async def daily_activity(
    email_list: EmailList,
) -> EmailList:
    """Fetch and return daily activity"""
    response = await ActivityService().handle(
        email_list,
    )
    return response
