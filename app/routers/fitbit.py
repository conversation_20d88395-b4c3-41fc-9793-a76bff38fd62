import pendulum
from fastapi import APIRouter, HTTPException, Request, Query
from starlette.responses import RedirectResponse, PlainTextResponse

from ciba_iot_etl.models.pydantic.common import ActivityDevice
from ciba_iot_etl.models.db.member import Member

from app.pydantic_model.base import AuthResp, TokenResp
from app.services.user import User
from app.settings import get_settings
from ciba_iot_etl.extract.fitbit_api.core import (
    FitbitLoader,
    FitbitServiceException,
)
from ciba_iot_etl.models.pydantic.fitbit import SubscriptionNotification
from app.log.logging import logger
from http import HTTPStatus
from app.services.fitbit import MemberFitbit
from datetime import datetime
from app.services.common import subscribe_to_account_helper


settings = get_settings()


router = APIRouter(prefix="/fitbit", tags=["fitbit-router"])

fitbit_client = FitbitLoader(
    client_id=settings.FITBIT_CLIENT_ID,
    client_secret=settings.FITBIT_CLIENT_SECRET,
    redirect_uri=settings.FITBIT_REDIRECT_URI,
)


@router.get(
    "/get_token",
    response_model=TokenResp,
    responses={302: {"description": "Redirect to the site"}},
)
async def subscribe_to_account(
    email: str = "",
    code: str = "",
    state: str = "",
    site: str = "",
    error: str = "",
) -> RedirectResponse | TokenResp:
    """
    Callback route for Fitbit account subscription.
    """
    return await subscribe_to_account_helper(
        client=fitbit_client,
        member_device_handler=MemberFitbit,
        email=email,
        code=code,
        state=state,
        site=site,
        error=error,
        activity_device=ActivityDevice.FITBIT,
    )


@router.get("/get_code_email/", response_model=None)
async def get_code_email(
    mail: str, site: str = ""
) -> RedirectResponse | AuthResp:
    """
    Route to get the permission from an user to take his data.
    This endpoint redirects to a Fitbit' login page on which
    the user has to identify and accept to share his data
    """
    token = await User(
        email=mail, device=ActivityDevice.FITBIT
    ).related_device()
    if token:
        return RedirectResponse(site + "/?token=" + str(token.id))

    auth_response = await fitbit_client.get_auth_page_url()

    return AuthResp(auth_url=auth_response)


@router.get("/notifications")
async def verify_notifications(verify: str = Query(...)):
    if verify == settings.FITBIT_VERIFY:
        return PlainTextResponse(status_code=HTTPStatus.NO_CONTENT, content="")
    else:
        return PlainTextResponse(
            status_code=HTTPStatus.NOT_FOUND, content="Not Found"
        )


@router.post("/notifications")
async def notifications(
    request: Request, data: list[SubscriptionNotification]
):
    now = pendulum.now()
    correlation_id = request.headers.get(
        "X-Request-ID", str(now.int_timestamp)
    )
    logger.info(f"Handling notifications for {len(data)} members")
    end_date_ts = now.int_timestamp
    for members_notification in data:
        logger.info(
            f"Handling notification for member {members_notification.member_id()}"
        )
        member = await Member.get_or_none(id=members_notification.member_id())
        if not member:
            raise HTTPException(
                status_code=HTTPStatus.NOT_FOUND, detail="Member not found"
            )

        start_date = await member.get_latest_fitbit_update()
        if isinstance(start_date, datetime):
            start_date_ts = pendulum.instance(start_date).int_timestamp
        else:
            start_date_ts = now.subtract(days=1).int_timestamp

        member_platforms = await member.get_platforms()
        member_fitbit = MemberFitbit(client=fitbit_client, member=member)
        try:
            await member_fitbit.send_pull_notification(
                startdate=start_date_ts,
                enddate=end_date_ts,
                correlation_id=correlation_id,
                platforms=member_platforms,
            )
        except FitbitServiceException as e:
            logger.error(
                f"Error handling notifications for member {member.id}: {e}"
            )
        logger.info(f"Notification fitbit handled for member {member.id}")

    return PlainTextResponse(status_code=HTTPStatus.NO_CONTENT, content="")
