# import json
#
# import numpy as np
# import pandas as pd
# from fastapi import APIRouter, Security
# from fastapi.security import API<PERSON>eyHeader
#
# from app.auth.constants import X_AUTH_KEY
# from app.common.db import engine
#
# router = APIRouter(
#     prefix="/charts",
#     dependencies=[
#         Security(APIKeyHeader(name=X_AUTH_KEY)),
#     ],
# )
#
#
# @router.get("/get_weigth")
# async def get_weigth() -> dict:
#     """
#     Callback route when ask for weight data
#     table name = weight_withings
#     table name = WT_withings
#     """
#     data = pd.read_sql_table("WT_withings", engine)
#     data = data.head(500)
#     data["time"] = pd.to_datetime(data["date"], unit="s")
#     df2 = data[["unit", "time", "value"]]
#     bins = np.linspace(min(df2["value"]), max(df2["value"]), 4)
#     group_names = ["low", "med", "high"]
#     # Create a new column 'group' based on the 'value'
#     df2["group"] = pd.cut(
#         df2["value"], bins, labels=group_names, include_lowest=True
#     )
#
#     # get data from pandas dataframe
#     json_str = df2.to_json(orient="records", date_format="iso")
#     return json.loads(json_str)
#
#
# @router.get("/get_heart_pulse")
# async def get_heart_pulse() -> dict:
#     """
#     Callback route when ask for weight data
#     table name = heart_pulse_withings
#     table name = HP_withings
#     """
#     data = pd.read_sql_table("HP_withings", engine)
#     data["time"] = pd.to_datetime(data["date"], unit="s")
#     df2 = data[["unit", "time", "value"]]
#     # get data from pandas dataframe
#     json_str = df2.to_json(orient="records", date_format="iso")
#     return json.loads(json_str)
#
#
# @router.get("/get_systolic_bp")
# async def get_systolic_bp() -> dict:
#     """
#     Callback route when ask for weight data
#     table name = systolic_bp_withings
#     table name = SBP_withings
#     """
#     data = pd.read_sql_table("SBP_withings", engine)
#     data["time"] = pd.to_datetime(data["date"], unit="s")
#     df2 = data[["unit", "time", "value"]]
#     # get data from pandas dataframe
#     json_str = df2.to_json(orient="records", date_format="iso")
#     return json.loads(json_str)
#
#
# @router.get("/diastolic_bp")
# async def diastolic_bp() -> dict:
#     """
#     Callback route when ask for weight data
#     table name = diastolic_bp
#     table name = diastolic_bp
#     """
#     data = pd.read_sql_table("diastolic_bp", engine)
#     data["time"] = pd.to_datetime(data["date"], unit="s")
#     df2 = data[["unit", "time", "value"]]
#     # get data from pandas dataframe
#     json_str = df2.to_json(orient="records", date_format="iso")
#     return json.loads(json_str)
#
#
# @router.get("/fat_free_mass")
# async def fat_free_mass() -> dict:
#     """
#     Callback route when ask for weight data
#     table name = fat_free_mass
#     table name = fat_free_mass
#     """
#     data = pd.read_sql_table("fat_free_mass", engine)
#     data["time"] = pd.to_datetime(data["date"], unit="s")
#     df2 = data[["unit", "time", "value"]]
#     # get data from pandas dataframe
#     json_str = df2.to_json(orient="records", date_format="iso")
#     return json.loads(json_str)
#
#
# @router.get("/fat_ratio")
# async def fat_ratio() -> dict:
#     """
#     Callback route when ask for weight data
#     table name = fat_ratio
#     table name = fat_ratio
#     """
#     data = pd.read_sql_table("fat_ratio", engine)
#     data["time"] = pd.to_datetime(data["date"], unit="s")
#     df2 = data[["unit", "time", "value"]]
#     # get data from pandas dataframe
#     json_str = df2.to_json(orient="records", date_format="iso")
#     return json.loads(json_str)
