# import urllib
#
# from fastapi import APIRouter
# from starlette.responses import RedirectResponse
#
# from src.ciba_iot_etl.models.pydantic.common import ActivityDevice
# from app.helpers.trigger_dag import call_dag
# from app.pydantic_model.base import Auth<PERSON>esp, ErrorResp, TokenResp
# # from app.services.oruraring import OruraringService
# from app.services.user import User
# from app.settings import get_settings
# from app.log.logging import logger
#
# settings = get_settings()
#
# oruraring_service = OruraringService(
#     settings.OURARING_CLIENT_ID,
#     settings.OURARING_CUSTOMER_SECRET,
#     settings.OURARING_STATE,
# )
#
#
# router = APIRouter(prefix="/oruraring", tags=["oruraring-router"])
# dag_id_call: str = "ouring_update"
#
#
# @router.get("/get_token", response_model=None)
# async def subscribe_to_account(
#     code: str = "", state: str = "", scope: str = "", error: str = ""
# ) -> RedirectResponse | ErrorResp | TokenResp:
#     """
#     Callback route when the user has accepted to share his data.
#     Once the auth has arrived Oruraring servers come back with
#     an authentication code and the state code provided in the
#     initial call
#     """
#
#     logger.info(  # pylint: disable=logging-fstring-interpolation
#         f"code: {code}, state: {state}, scope: {scope}, error: {error}"
#     )
#
#     data_dict = urllib.parse.parse_qs(state)
#     # Converting values to regular strings instead of lists
#     query = {key: value[0] for key, value in data_dict.items()}
#
#     if code:
#         token = await oruraring_service.subcribe_to_user_devices(
#             code, settings.OURARING_REDIRECT_URI, query
#         )
#         call_dag(dag_id_call)
#         return TokenResp(token=token)
#     if "site" in query:
#         site = query["site"]
#         return RedirectResponse(site)
#     if error:
#         return ErrorResp(error=error)
#
#
# @router.get("/get_code_email/", response_model=None)
# async def get_code_email(
#     mail: str, site: str = ""
# ) -> RedirectResponse | AuthResp:
#     """
#     Route to get the permission from an user to take his data.
#     This endpoint redirects to a Oruraring' login page on which
#     the user has to identify and accept to share his data
#     """
#     query = {"email": mail, "site": site}
#     token = await User(
#         email=mail, device=ActivityDevice.ORURARING
#     ).related_device()
#     if token:
#         return RedirectResponse(site + "/?token=" + str(token.id))
#
#     uri = urllib.parse.urlencode(query)
#     auth_response = await oruraring_service.get_auth_code(
#         settings.OURARING_REDIRECT_URI, state=uri
#     )
#
#     return AuthResp(auth_url=auth_response)
