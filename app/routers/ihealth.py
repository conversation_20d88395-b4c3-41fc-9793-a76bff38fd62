# from fastapi import APIRouter, Security
# from fastapi.security import API<PERSON>eyHeader
#
# from app.auth.constants import X_AUTH_KEY
# from app.pydantic_model.base import UserResp
# from app.services.ihealth import IhealthService
# from app.services.user import User
#
# router = APIRouter(
#     prefix="/i-devices",
#     tags=["api-router"],
#     dependencies=[
#         # Depends(verify_token),
#         Security(APIKeyHeader(name=X_AUTH_KEY)),
#     ],
# )
# TYPE_DEVICE = "ihealth"
#
#
# @router.post("/add", response_model=UserResp)
# async def add(mail: str):
#     """Add user to ihealth table"""
#     # after test it has to remove
#     mail = mail.replace(" ", "+")
#     user = await User(email=mail, device=TYPE_DEVICE).related_device()
#
#     user_id = await IhealthService(mail).add_user() if not user else user.id
#     return UserResp(user_id=user_id)
