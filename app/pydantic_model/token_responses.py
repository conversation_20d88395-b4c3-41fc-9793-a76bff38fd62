"""
Standardized response models for token operations.
"""

from typing import Optional, Dict, Any
from pydantic import BaseModel
from enum import Enum


class TokenErrorType(str, Enum):
    """Enumeration of token error types."""

    INVALID_GRANT = "invalid_grant"
    EXPIRED_TOKEN = "expired_token"
    NETWORK_ERROR = "network_error"
    API_ERROR = "api_error"
    VALIDATION_ERROR = "validation_error"
    UNKNOWN_ERROR = "unknown_error"


class TokenRefreshResult(BaseModel):
    """Standardized result for token refresh operations."""

    success: bool
    error_type: Optional[TokenErrorType] = None
    error_message: Optional[str] = None
    requires_reauth: bool = False
    used_fallback: bool = False
    token_data: Optional[Dict[str, Any]] = None
    expires_at: Optional[str] = None

    @classmethod
    def success_result(
        cls,
        token_data: Dict[str, Any],
        expires_at: Optional[str] = None,
        used_fallback: bool = False,
    ) -> "TokenRefreshResult":
        """Create a successful token refresh result."""
        return cls(
            success=True,
            token_data=token_data,
            expires_at=expires_at,
            used_fallback=used_fallback,
        )

    @classmethod
    def error_result(
        cls,
        error_type: TokenErrorType,
        error_message: str,
        requires_reauth: bool = False,
    ) -> "TokenRefreshResult":
        """Create an error token refresh result."""
        return cls(
            success=False,
            error_type=error_type,
            error_message=error_message,
            requires_reauth=requires_reauth,
        )

    @classmethod
    def invalid_grant_error(cls, platform: str) -> "TokenRefreshResult":
        """Create an invalid grant error result."""
        return cls.error_result(
            error_type=TokenErrorType.INVALID_GRANT,
            error_message=f"Refresh token is invalid. User needs to re-authenticate with {platform}.",
            requires_reauth=True,
        )

    @classmethod
    def from_exception(cls, exception: Exception) -> "TokenRefreshResult":
        """Create an error result from an exception."""
        error_message = str(exception)

        # Determine error type based on exception message
        if "invalid_grant" in error_message.lower():
            error_type = TokenErrorType.INVALID_GRANT
            requires_reauth = True
        elif "expired" in error_message.lower():
            error_type = TokenErrorType.EXPIRED_TOKEN
            requires_reauth = True
        elif (
            "network" in error_message.lower()
            or "timeout" in error_message.lower()
        ):
            error_type = TokenErrorType.NETWORK_ERROR
            requires_reauth = False
        elif (
            "validation" in error_message.lower()
            or "invalid" in error_message.lower()
        ):
            error_type = TokenErrorType.VALIDATION_ERROR
            requires_reauth = False
        else:
            error_type = TokenErrorType.UNKNOWN_ERROR
            requires_reauth = False

        return cls.error_result(
            error_type=error_type,
            error_message=error_message,
            requires_reauth=requires_reauth,
        )


class TokenOperationResult(BaseModel):
    """Standardized result for general token operations."""

    success: bool
    message: Optional[str] = None
    error: Optional[str] = None
    requires_reauth: bool = False
    data: Optional[Dict[str, Any]] = None

    @classmethod
    def success_result(
        cls, message: str, data: Optional[Dict[str, Any]] = None
    ) -> "TokenOperationResult":
        """Create a successful operation result."""
        return cls(success=True, message=message, data=data)

    @classmethod
    def error_result(
        cls,
        error: str,
        requires_reauth: bool = False,
        data: Optional[Dict[str, Any]] = None,
    ) -> "TokenOperationResult":
        """Create an error operation result."""
        return cls(
            success=False,
            error=error,
            requires_reauth=requires_reauth,
            data=data,
        )


class FallbackAttemptResult(BaseModel):
    """Result of a fallback token attempt."""

    attempted: bool
    success: bool
    reason: Optional[str] = None
    error: Optional[str] = None
