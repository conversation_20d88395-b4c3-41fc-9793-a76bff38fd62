from datetime import datetime
from typing import Dict, Optional, Any
from uuid import UUID

from pydantic import BaseModel, Field

from ciba_iot_etl.models.pydantic.transtek import (
    TranstekDeviceType,
    TranstekStatus,
)
from ciba_iot_etl.models.pydantic.common import Carrier


class UpdateTrackingDataResponse(BaseModel):
    tracking_url: str
    member_id: Optional[str] = None
    member_type: Optional[str] = None
    message: str = "Tracking data updated successfully"


class CarrierListResponse(BaseModel):
    carriers: Dict


class TranstekResponse(BaseModel):
    """Response model for Transtek device with enum validation."""

    id: UUID = Field(..., description="Unique identifier for the device")
    device_id: str = Field(..., max_length=12, description="Device ID")
    imei: str = Field(..., max_length=15, description="IMEI number")
    model: str = Field(..., max_length=20, description="Device model")
    device_type: TranstekDeviceType = Field(..., description="Type of device")
    tracking_number: Optional[str] = Field(
        None, max_length=40, description="Tracking number"
    )
    carrier: Optional[Carrier] = Field(None, description="Carrier")
    tracking_url: Optional[str] = Field(None, description="Tracking link")
    timezone: Optional[str] = Field(
        None, max_length=40, description="Device timezone"
    )
    last_status_report: Optional[Dict[str, Any]] = Field(
        None, description="Last status report JSON"
    )
    status: TranstekStatus = Field(..., description="Device status")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    member_id: Optional[UUID] = Field(None, description="Member ID")

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v),
        }
