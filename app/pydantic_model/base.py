import uuid
from typing import List, Optional

from pydantic import BaseModel


class EmailItem(BaseModel):
    email: str
    value: Optional[float] = None
    id: Optional[uuid.UUID] = None


class EmailList(BaseModel):
    emails: List[EmailItem]


class TokenResp(BaseModel):
    token: Optional[uuid.UUID] = None
    error: Optional[str] = None


class AuthResp(BaseModel):
    auth_url: str


class UserResp(BaseModel):
    user_id: uuid.UUID


class ErrorResp(BaseModel):
    error: str
