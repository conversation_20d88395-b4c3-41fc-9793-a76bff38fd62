from datetime import datetime, date
from enum import Enum
from typing import Optional, List

from pydantic import BaseModel, Field


class MeasureType(str, Enum):
    WEIGHT = "weight"
    BLOOD_PRESSURE = "blood_pressure"
    HEART_RATE = "heart_rate"
    ACTIVITY = "activity"
    SLEEP = "sleep"
    GLUCOSE_LEVEL = "glucose_level"


class WeightUnit(str, Enum):
    KG = "Kg"
    LB = "Lb"


class DistanceUnit(str, Enum):
    KM = "km"
    MI = "mi"


class GroupOption(str, Enum):
    MONTH = "month"
    WEEK = "week"
    DAY = "day"
    HOUR = "hour"


class GlucoseEventType(str, Enum):
    LOW = "low"
    HIGH = "high"
    VERY_LOW = "very_low"
    VERY_HIGH = "very_high"


class ActivityDay(BaseModel):
    exercised: bool
    minutes: int
    date: str
    steps: int


class TodaysSummary(BaseModel):
    steps: int
    active_minutes: int
    distance: float
    distance_unit: DistanceUnit = DistanceUnit.MI


class ActivitySummary(BaseModel):
    today: TodaysSummary
    exercise_days: int
    total_days: int = 7
    activity_details: Optional[List[ActivityDay]]
    lastUpdate: datetime


class SleepSummary(BaseModel):
    duration: int
    date: date
    asleep_time: int
    lastUpdate: datetime
    timelineStart: Optional[datetime] = None
    timelineEnd: Optional[datetime] = None
    sleep_score: int


class SleepScoreTable(BaseModel):
    duration_max: Optional[int] = Field(default=50)
    duration_score: Optional[int] = Field(default=0)
    quality_max: Optional[int] = Field(default=25)
    quality_score: Optional[int] = Field(default=0)
    restoration_max: Optional[int] = Field(default=25)
    restoration_score: Optional[int] = Field(default=0)
    sleep_max: Optional[int] = Field(default=100)
    sleep_score: Optional[int] = Field(default=0)


class Sleep(BaseModel):
    sleep_date: str
    start_time: str
    end_time: str
    duration: int
    efficiency: int
    asleep_time: int
    sleep_score_table: SleepScoreTable
    bed_time: int
    deep_time: int
    light_time: int
    rem_time: int
    wake_time: int
    start_time_in_bed: datetime
    end_time_in_bed: datetime


class SleepData(BaseModel):
    sleep: List[Sleep]
    today: Sleep


class WeightSummary(BaseModel):
    currentWeight: float
    weightChange: float
    averageWeightChange: float
    lastUpdate: datetime


class HeartRateSummary(BaseModel):
    current: int
    maxRpm: int
    minRpm: int
    averageRpm: int
    lastUpdate: datetime


class BloodPressureSummaryEntry(BaseModel):
    average: int
    highest: int
    current: int


class BloodPressureSummary(BaseModel):
    lastUpdate: datetime
    systolic: BloodPressureSummaryEntry
    diastolic: BloodPressureSummaryEntry


class GlucoseLevelSummary(BaseModel):
    isDeviceConnected: bool
    averageGlucoseLevel: int
    daysWithMeasurements: int
    measurementCycle: int
    firstMeasurementDate: datetime
    lastUpdate: datetime


class DashboardGlucoseLevelSummary(GlucoseLevelSummary):
    unit: str
    hyperglycemiaEventsCount: int
    hypoglycemiaEventsCount: int


class DashboardSummary(BaseModel):
    bloodPressure: Optional[BloodPressureSummary]
    heartRate: Optional[HeartRateSummary]
    weight: Optional[WeightSummary]
    sleep: Optional[SleepSummary]
    activity: Optional[ActivitySummary]
    glucoseLevel: Optional[DashboardGlucoseLevelSummary]


class WeightEntry(BaseModel):
    createdAt: datetime
    value: float


class HeartRateEntry(BaseModel):
    createdAt: datetime
    value: int


class BloodPressureEntry(BaseModel):
    createdAt: datetime
    systolic: int
    diastolic: int


class GlucoseLevelEntry(BaseModel):
    createdAt: datetime
    value: int


class GlucoseLevelEventEntry(BaseModel):
    createdAt: datetime
    value: int
    type: GlucoseEventType


class MeasuresResponse(BaseModel):
    unit: str
    type: MeasureType
    summary: (
        Optional[WeightSummary]
        | Optional[HeartRateSummary]
        | Optional[BloodPressureSummary]
        | Optional[ActivitySummary]
        | Optional[SleepSummary]
    ) = None
    values: (
        list[WeightEntry]
        | list[BloodPressureEntry]
        | list[HeartRateEntry]
        | list[ActivityDay]
        | list[Sleep]
    )


class GlucoseMeasuresResponse(BaseModel):
    unit: str
    type: MeasureType
    summary: Optional[GlucoseLevelSummary] = None
    values: list[GlucoseLevelEntry]
    hypoAndHyperEvents: list[GlucoseLevelEventEntry]


class Measure(BaseModel):
    value: float
    unit: str
    created_at: datetime


class Device(BaseModel):
    id: str
    device_type: str
    last_synced_at: datetime


class LatestMeasuresResponse(BaseModel):
    last_ciba_sync: Optional[datetime] = None
    last_device_sync: Optional[datetime] = None
    measures: List[Measure] = []
    devices: List[Device] = []
