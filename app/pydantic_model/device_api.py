from typing import Optional
from uuid import UUID
from enum import StrEnum, auto
from datetime import datetime

from pydantic import BaseModel
from ciba_iot_etl.models.pydantic.common import ActivityDevice


class ConnectionToken(BaseModel):
    token: UUID
    healthy: Optional[bool] = None
    account_id: Optional[str] = None


class Subscription(BaseModel):
    expires_in: int


class ConnectionStatus(ConnectionToken):
    healthy: Optional[bool] = None
    account_id: Optional[str] = None
    subscription: Optional[Subscription] = None
    device_metadata: Optional[dict] = None


class DeviceStatusEnum(StrEnum):
    NOT_CONNECTED = auto()
    CONNECTED = auto()
    RECONNECT = auto()


class DeviceStatus(BaseModel):
    """Device status, useful when querying multiple devices"""

    id: Optional[UUID] = None
    device: ActivityDevice
    status: DeviceStatusEnum
    last_updated: Optional[datetime] = None
    device_metadata: Optional[dict] = None

    model_config = {"arbitrary_types_allowed": True}


class MemberDeviceStatus(BaseModel):
    member_id: str
    rpm_member_id: Optional[UUID] = None
    devices: list[DeviceStatus]
