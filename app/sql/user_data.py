WEIGT = """WITH RankedRecords AS (
            SELECT
            w.email, ww.value, ww.date,
            ROW_NUMBER() OVER (PARTITION BY w.email ORDER BY ww.date DESC)
            as rn
            FROM weight_withings ww
            LEFT JOIN public.withings w ON w.id::text = ww.user_id
            JOIN temp_emails te ON w.email = te.email
            )
            SELECT
                email,
                ROUND(CAST(value AS NUMERIC) * 2.20462, 2) AS value,
                date
            FROM
                RankedRecords
            WHERE
                rn = 1"""


ACTIVITY = """SELECT f.email, ff."vo2Max" as value, ff."dateTime"::date as date
            FROM cardio_fitness_fitbit ff
            LEFT JOIN public.fitbit f ON f.id::text = ff.user_id
            JOIN temp_emails te ON f.email = te.email
            WHERE ff."dateTime"::date  > CURRENT_DATE - INTERVAL '1 day'
            GROUP BY ff."dateTime"::date, f.email, ff."vo2Max";"""
