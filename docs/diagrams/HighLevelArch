workspace "RPM" {
    !identifiers hierarchical

    model {
        user = person "Person" "A user interacting with the RPM system."

        patient = softwareSystem "Patient" {
            pat-web = container "UI" {
                pat-devices-ui = component "Devices Page"
            }
            pat-api = container "API" {
                pat-devices-api = component "Devices api endpoint" "REST API"
            }

        }

        participant = softwareSystem "Participant" {
            part-web = container "UI" {
                part-devices-ui = component "Devices Page"
            }
            part-api = container "API" {
                part-devices-api = component "Devices Page" "REST API"
            }

            participant-db = container "Participant DB" {
                tags "Database"
            }
        }


        rpm = softwareSystem "RPM" "System that handles participant device data" {
            user = container "Get member data class" {
                map_to_platform = component "Maps member to patient/participant platform"
                related_device = component "Gets member device"
            }


            get_code = container "Get auth link" "Used to generate device auth_url" {
                get_auth_page_url = component "Get auth url" "Method of device_client"
                auth_url = component "Device auth_url"

            }

            get_token = container "get_token" "Process data received from device api auth server"

            subscribe_to_account_helper = container "Process received device oauth data"  {
                member = component "Member instance"
                member_platfroms = component "Plaforms linked to member"


            }

            process_member_state = container "Retrieve and process member_state from DB"

            member_state = container "MemberState" "Stores information user metadata, like: redirect_uri,code_verifier,sync_start_date " {
                get = component "Get"
                create = component "Create"
            }

            get_summary = container "Member device measures summary"
            get_measures = container "Member device specifici measure type"
            member_device = container "Authorized member device client" {
                handle_susbscribe_to_account = component "(Re-)subscribes member to device notification"
                sync_device = component "Sends data sync notification to ETL"
            }



            rpm-db = container "RPM DB" {
                tags "Database"
                member_model = component "MemberModel"
                member_state_model = component "MemberStateModel"
            }

            fitbit_loader = container "Fitbit API Client" {
                get_auth_page_url = component "Get the URL to redirect the user to for authentication."
            }
            withgins_loader = container "Withings API Client" {
                get_auth_page_url = component "Get the URL to redirect the user to for authentication."
            }

        }

        withings = softwareSystem "Withings API" {
            w_device_auth = container "Withings user device auth page"

        }
        fitbit = softwareSystem "Fitbit API" {
            f_device_auth = container "Fitbit user device auth page"
        }


        etl_pipeline = softwareSystem "ETL pipeline" "Group of microservices to process devices data" {
            iot_data_puller = container "iot_data_puller lambda" "Pulls data from device API using api endpoints and user auth token" {
                process_sqs_message = component "PullDataNotification-SQS message converter"
                fetch_data = component "Pull data" "Function that get user access token from DB and using it pulls data from device API"
                update_token = component "Refresh access/refresh token" "Function used to update user device access token after each call to device api"
            }
            participant_iot_data_writer = container "participant_iot_data_writer lambda" "Processes received message and pulls data from device api" {
                process_sqs_message = component "Part-SQS message converter"
                transform_sqs_message = component "SQS->device metrics converter"
                process_metrics = component "Filter/Unify/Vaidate metrics"
                save_to_db = component "Prepare&Save DB entries"
            }
            patient_iot_data_writer = container "patient_iot_data_writer lambda" "Processes received message and pulls data from device api" {
                process_sqs_message = component "Pat-SQS message converter"
                transform_sqs_message = component "SQS->device metrics converter"
                process_metrics = component "Filter/Unify/Vaidate metrics"
                save_to_db = component "Prepare&Save DB entries"
            }


            process_to_s3 = container "Converts received data to csv and stores to s3 bucket"
            publish_to_sns = container "Sends message with received raw data to participant/patient platfrom sns"


            data_notification_sqs = container "Sync data notification queue" "Transfers data notification messages from rpm to iot_data_puller"
            patient_sns = container "SNS service for patient"
            patient_sqs = container "SQS service for patient"
            participant_sns = container "SNS service for participant"
            participant_sqs = container "SQS service for participant"


        }


        datalake = softwareSystem "CibaDataLake" {
            s3_bucket = container "Datalake bucket"
        }


        # relationships between people and software systems
        user -> patient "Visit devices page"
        user -> participant "Visit devices page"
        etl_pipeline -> rpm "Processed metrics"
        rpm -> withings "Device access token retrieval"
        rpm -> fitbit "Device access token retrieval"
        etl_pipeline -> withings "Device metrics retrieval"
        etl_pipeline -> fitbit "Device metrics retrieval"
        etl_pipeline -> participant "Saves device metrics"


        # relationships to/from containers
        patient.pat-web -> patient.pat-api
        patient.pat-api -> rpm.get_code "Get device auth_url"
        participant.part-api -> rpm.get_code "Get device auth_url or status"
        patient.pat-api -> rpm.get_token "Get user device access token"
        rpm.rpm-db -> rpm.get_measures "Get specific member device metrics"
        rpm.rpm-db -> rpm.get_summary "Get aggregated member device metrics"
        rpm.get_measures -> patient.pat-api "Forward specific member device metrics"
        rpm.get_summary -> patient.pat-api "Forward aggregated member device metrics"

        participant.part-api -> rpm.get_token "Get user device access token"
        participant.participant-db -> participant.part-api "Withings weight data"

        rpm.get_token -> etl_pipeline.data_notification_sqs "Pull data notification"
        rpm.get_code -> rpm.user "Initiate member"
        rpm.get_code -> rpm.member_state "Get/Create member_state"
        rpm.get_code -> rpm.fitbit_loader "Initiate fitbit api client"
        rpm.get_code -> rpm.withgins_loader "Initiate withings api client"

        # etl containers
        etl_pipeline.data_notification_sqs -> etl_pipeline.iot_data_puller "Deliver notification message"
        etl_pipeline.patient_sns -> etl_pipeline.patient_sqs
        etl_pipeline.participant_sns -> etl_pipeline.participant_sqs
        etl_pipeline.patient_sqs -> etl_pipeline.patient_iot_data_writer
        etl_pipeline.participant_sqs -> etl_pipeline.participant_iot_data_writer







        # relationships to/from components
        participant.part-web.part-devices-ui -> participant.part-api.part-devices-api

        # get_code
        rpm.user.related_device -> rpm.rpm-db.member_model "Get member device from DB"
        rpm.user.map_to_platform -> rpm.rpm-db.member_model "Get/Create member in DB"
        rpm.member_state.create -> rpm.rpm-db.member_state_model "Create member_state"
        rpm.fitbit_loader.get_auth_page_url -> fitbit "Get the URL to redirect the user to for authentication"
        rpm.withgins_loader.get_auth_page_url -> withings "Get the URL to redirect the user to for authentication."
        fitbit -> rpm.member_state.create "Saves code verifier to member state"
        fitbit -> rpm.get_code.get_auth_page_url "Returns generated url"
        withings -> rpm.get_code.get_auth_page_url "Returns generated url"
        rpm.get_code.get_auth_page_url -> rpm.get_code.auth_url "Auth url"
        rpm.get_code.auth_url -> participant.part-api.part-devices-api "Forward auth url"
        rpm.get_code.auth_url -> patient.pat-api.pat-devices-api "Forward auth url"

        # get_token
        withings.w_device_auth -> rpm.get_token "Redirect user auth data"
        fitbit.f_device_auth -> rpm.get_token "Redirect user auth data"
        rpm.get_token -> rpm.subscribe_to_account_helper.member "Calls subscribe helper"
        rpm.subscribe_to_account_helper.member -> rpm.member_state.get "Gets member state from DB"
        rpm.subscribe_to_account_helper.member -> rpm.subscribe_to_account_helper.member_platfroms "Return member linked platforms"
        rpm.member_state.get -> rpm.rpm-db.member_state_model "Get member_state
        rpm.subscribe_to_account_helper -> rpm.process_member_state "Retrieve site and sync start_date"
        rpm.subscribe_to_account_helper.member_platfroms -> rpm.member_device.sync_device "Generate data sync notification for each platform"
        rpm.member_device.sync_device -> etl_pipeline.data_notification_sqs "Send data sync notification"


        #
        etl_pipeline.participant_iot_data_writer.process_sqs_message -> etl_pipeline.participant_iot_data_writer.transform_sqs_message
        etl_pipeline.participant_iot_data_writer.transform_sqs_message -> etl_pipeline.participant_iot_data_writer.process_metrics
        etl_pipeline.participant_iot_data_writer.process_metrics -> etl_pipeline.participant_iot_data_writer.save_to_db
        etl_pipeline.participant_iot_data_writer.save_to_db -> participant.participant-db "Saves processed data"
        etl_pipeline.participant_iot_data_writer.process_metrics -> datalake.s3_bucket "Store processed data"


        etl_pipeline.patient_iot_data_writer.process_sqs_message -> etl_pipeline.patient_iot_data_writer.transform_sqs_message
        etl_pipeline.patient_iot_data_writer.transform_sqs_message -> etl_pipeline.patient_iot_data_writer.process_metrics
        etl_pipeline.patient_iot_data_writer.process_metrics -> etl_pipeline.patient_iot_data_writer.save_to_db
        etl_pipeline.patient_iot_data_writer.save_to_db -> rpm.rpm-db "Saves processed data"
        etl_pipeline.patient_iot_data_writer.process_metrics -> datalake.s3_bucket "Store processed data"





        # etl.puller
        etl_pipeline.iot_data_puller.process_sqs_message -> etl_pipeline.iot_data_puller.update_token
        etl_pipeline.iot_data_puller.update_token -> etl_pipeline.iot_data_puller.fetch_data
        etl_pipeline.iot_data_puller.fetch_data -> datalake.s3_bucket "Store raw data"
        etl_pipeline.iot_data_puller.fetch_data -> etl_pipeline.patient_sns "Sends device raw metrics"
        etl_pipeline.iot_data_puller.fetch_data -> etl_pipeline.participant_sns "Sends device raw metrics"





    }

    views {
        systemlandscape "SystemLandscape" {
            include *
            autoLayout
            title "Device data system overview"
        }
        systemContext patient "Patient" {
            include *
            autolayout lr
            title "Patient: System Context Diagram"
        }
        container patient "Patient-Container" {
            include *
            autolayout lr
            title "Patient: System Containers Diagram"
        }

        systemContext participant "Participant" {
            include *
            autolayout lr
            title "Participant: System Context Diagram"
        }
        container participant "Participant-Container" {
            include *
            autolayout lr
            title "Participant: System Containers Diagram"
        }



        systemContext rpm "RPM-System" {
            include *
            autolayout lr
            title "RPM: System Context Diagram"
        }
        container rpm "RPM-Container" {
            include *
            autolayout lr
            title "RPM: System Containers Diagram"
        }

        component rpm.get_code {
            include *
            autolayout lr
            title "RPM: get_code Component"
        }

        component rpm.subscribe_to_account_helper {
            include *
            autolayout lr
            title "RPM: get_token Component"
        }

        systemContext etl_pipeline "ETL-pipeline" {
            include *
            autolayout lr
            title "ETL System Context Diagram"
        }
        container etl_pipeline "ETL-Container" {
            include *
            autolayout lr
            title "ETL System Containers Diagram"
        }

        component etl_pipeline.patient_iot_data_writer {
            include *
            autolayout lr
            title "ETL: Transform and Load patient device data"
        }
        component etl_pipeline.participant_iot_data_writer {
            include *
            autolayout lr
            title "ETL: Transform and Load participant device data"
        }


        component etl_pipeline.iot_data_puller {
            include *
            autolayout lr
            title "ETL: Pull member device data"
        }


        styles {
            element "Database" {
                shape Cylinder
            }
            element "Container" {
                background #438dd5
                color #ffffff
            }
        }





    }
}
