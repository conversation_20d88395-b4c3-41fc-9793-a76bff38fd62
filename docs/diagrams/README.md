# RPM System Architecture

## Overview

The **Remote Patient Monitoring (RPM) System** is a platform that facilitates the collection, processing, and storage of participant and patient device data. It integrates with external device APIs (e.g., **Fitbit**, **Withings**) and processes data through an **ETL pipeline** before storing it in an **RPM database** and **CibaDataLake**.

## System Architecture

To preview diagram [Architecture](HighLevelArch) use [site](https://structurizr.com/dsl)

The system is composed of multiple subsystems:

### 1. **Patient System**

- **UI (pat-web)**: The user interface where patients can manage their connected devices.
- **API (pat-api)**: A backend service that facilitates device authentication and data retrieval.
- **Devices Page (pat-devices-ui & pat-devices-api)**: Interfaces for device interaction.
- **Interacts with**:
  - **RPM System** for retrieving device authorization links and user data.
  - **ETL Pipeline** for receiving processed device metrics.

### 2. **Participant System**

- **UI (part-web)**: Interface for participants.
- **API (part-api)**: Manages participant interactions with device data.
- **Devices Page (part-devices-ui & part-devices-api)**: Handles device data access and interactions.
- **Participant DB**: Stores participant-related data.
- **Interacts with**:
  - **RPM System** for device authentication and access.
  - **ETL Pipeline** for receiving processed device metrics.

### 3. **RPM System** (Central Processing Unit)

- **Handles device authentication, user state, and data retrieval.**
- **Key Components**:
  - **Get member data class**
    - Maps users to their respective platforms (Patient/Participant).
    - Retrieves member devices.
  - **Authentication Flow**
    - `get_code` (Generates device authentication URL)
    - `get_token` (Processes device API authentication responses)
  - **Subscription Management**
    - Processes OAuth data and retrieves linked platforms.
  - **Member State Management**
    - Stores metadata such as `redirect_uri`, `sync_start_date`.
  - **Device Data Access**
    - Provides APIs to retrieve device measures and summaries.
  - **RPM DB**
    - Stores member models and states.
  - **External API Clients**
    - Fitbit API Client
    - Withings API Client

### 4. **ETL Pipeline**

- **Responsible for pulling, processing, and storing device data.**
- **Key Components**:
  - **Data Pulling (iot_data_puller)**
    - Extracts data from device APIs using user access tokens.
  - **Data Transformation (iot_data_writer)**
    - Converts device API responses into standardized metrics.
  - **Message Processing**
    - Uses SQS for async data processing.
  - **Storage & Notifications**
    - Saves processed data to RPM DB and CibaDataLake.
    - Publishes raw metrics to SNS for further processing.

### 5. **External Systems**

- **Fitbit API**
  - Handles Fitbit user device authentication.
  - Provides access to Fitbit device data.
- **Withings API**
  - Handles Withings user device authentication.
  - Provides access to Withings device data.
- **CibaDataLake**
  - Stores raw and processed device data for long-term analysis.

## System Diagrams

The **Structurizr** workspace defines multiple system views:

1. **System Landscape**: Overview of all components.
1. **System Context Views**:
   - Patient
   - Participant
   - RPM System
   - ETL Pipeline
1. **Container Views**:
   - Patient Containers
   - Participant Containers
   - RPM Containers
   - ETL Pipeline Containers
1. **Component Views**:
   - `get_code`: Authentication URL generation
   - `get_token`: Device token processing
   - ETL transformation for patient & participant data

## Relationships

- **User → Patient/Participant System**: Accesses device pages.
- **RPM System → Fitbit/Withings APIs**: Retrieves device authentication tokens.
- **ETL Pipeline → RPM System**: Receives and processes device metrics.
- **ETL Pipeline → Fitbit/Withings APIs**: Retrieves user device data.
- **ETL Pipeline → CibaDataLake**: Stores processed metrics.

## How to Use

1. **Deploy RPM System**
   - Start `RPM` services.
   - Ensure `Fitbit` and `Withings` API clients are configured.
1. **Connect Devices**
   - Users authenticate their devices via `get_code`.
   - The system retrieves and processes authentication tokens.
1. **Data Processing**
   - `ETL Pipeline` pulls data and processes it.
   - Data is stored in `RPM DB` or `CibaDataLake`.
1. **Access Processed Data**
   - `RPM` provides APIs for retrieving device summaries and specific measures.

## Deployment & Infrastructure

- The RPM System is deployed using **containerized microservices**.
- ETL Pipeline runs on **AWS Lambda** and integrates with **SQS, SNS, and S3**.
- Databases include **RPM DB (Relational DB)** and **Participant DB**.
- External APIs (**Fitbit, Withings**) handle device authentication and data retrieval.

## Future Enhancements

- **Support additional fitness device integrations**.
- **Implement AI-based anomaly detection** on device metrics.
- **Improve real-time data streaming capabilities**.

______________________________________________________________________

This document provides a high-level overview of the **RPM System**. For detailed implementation, refer to the source code and Structurizr documentation.
