# ADR 0001: User's Measures Storage

## Context

We need a database scheme to store user measurements. This schema must be compatible with different brands of devices.

## Decision

Three tables will be used to store weight, blood pressure and heart rate measurements. In each table, the optional
metadata field has been created to store any additional information to the standard fields.

It was agreed in the team that the `created_at` field will store the date when the measurement was
created/collected and the `updated_at` field will store the date when the record was created in our database.

## Consequences

Positive:

- Users can switch devices without causing a serious impact on system behavior.
- Support for device brands can be added or removed more easily.
- A single source of truth is established for each type of measurement.

Negative:

- May require increased maintenance as data flow increases.
