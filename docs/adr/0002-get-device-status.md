# ADR 0002: Get all device status

## Context

The endpoint `devices/get_status` might be called lots of times by `participant-admin`, making this endpoint a single point of failure.

## Decision

Added two new endpoints:

- `devices/get_all_status` to query all devices from a member
- `devices/get_status_by_members` to query all devices from a list of member IDs

## Consequences

Positive:

- Less boilerplate when querying device status for all devices
- Does not leak member existence by sending random values
