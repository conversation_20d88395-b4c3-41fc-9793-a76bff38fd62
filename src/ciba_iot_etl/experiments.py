from ciba_iot_etl.extract.withings_api.core import Withings<PERSON>oader
from config import Config
from ciba_iot_etl.extract.withings_api.const import MeasureType
import pendulum


if __name__ == "__main__":
    user_access_token = ""
    expected_date = pendulum.now().subtract(days=600)

    withings_service = WithingsLoader(
        Config.WITHINGS_CLIENT_ID,
        Config.WITHINGS_CUSTOMER_SECRET,
        Config.WITHINGS_STATE,
    )
    user_measure = withings_service.get_user_measure(
        user_access_token,
        {
            "meastypes": MeasureType.WEIGHT.value,
            "startdate": withings_service.get_request_timestamp(expected_date),
        },
    )
