"""Constants for CIBA IoT ETL operations."""

# API Limits and Retry Configuration
MAX_RETRY_ATTEMPTS = 100
WITHINGS_RATE_LIMIT_PER_MINUTE = 120
FITBIT_RATE_LIMIT_PER_HOUR = 150
DEFAULT_RETRY_DELAY = 1.0
MAX_RETRY_DELAY = 300.0  # 5 minutes

# Date Range Limits
FITBIT_MAX_DAYS_PER_REQUEST = 99
WITHINGS_MAX_DAYS_PER_REQUEST = 365
DEFAULT_FALLBACK_DAYS = 30

# Unit Conversions
LB_TO_KG = 1.0 / 2.20462
KG_TO_LB = 2.20462
INCHES_TO_CM = 2.54
CM_TO_INCHES = 1.0 / 2.54

# AWS Configuration
DEFAULT_AWS_REGION = "us-east-2"
SSM_PARAMETER_PREFIX = "/ciba-iot-etl"

# Database Configuration
DEFAULT_CONNECTION_TIMEOUT = 30  # seconds
DEFAULT_QUERY_TIMEOUT = 60  # seconds

# HTTP Configuration
DEFAULT_HTTP_TIMEOUT = 30  # seconds
DEFAULT_MAX_RETRIES = 3
DEFAULT_BACKOFF_FACTOR = 0.3

# Token Expiration
WITHINGS_TOKEN_EXPIRY_HOURS = 8
FITBIT_TOKEN_EXPIRY_HOURS = 24
TOKEN_REFRESH_BUFFER_MINUTES = 30  # Refresh tokens 30 minutes before expiry


class MetricNames:
    """Standardized metric names for monitoring."""

    # Withings metrics
    WITHINGS_TOKEN_EXPIRED = "withings.tokens.old_refresh_expired"
    WITHINGS_TOKEN_REFRESH_SUCCESS = "withings.tokens.refresh_success"
    WITHINGS_TOKEN_REFRESH_FAILURE = "withings.tokens.refresh_failure"
    WITHINGS_API_ERROR = "withings.api.error"
    WITHINGS_PROCESSING_ERROR = "withings.processing.error"
    WITHINGS_DATA_PROCESSED = "withings.data.processed"

    # Fitbit metrics
    FITBIT_TOKEN_REFRESH_SUCCESS = "fitbit.tokens.refresh_success"
    FITBIT_TOKEN_REFRESH_FAILURE = "fitbit.tokens.refresh_failure"
    FITBIT_API_ERROR = "fitbit.api.error"
    FITBIT_PROCESSING_ERROR = "fitbit.processing.error"
    FITBIT_DATA_PROCESSED = "fitbit.data.processed"
    FITBIT_HEALTH_STATUS_UPDATE = "fitbit.health_status.update"
    FITBIT_HEALTH_STATUS_ERROR = "fitbit.health_status.update_error"

    # Transtek metrics
    TRANSTEK_API_ERROR = "transtek.api.error"
    TRANSTEK_PROCESSING_ERROR = "transtek.processing.error"
    TRANSTEK_DATA_PROCESSED = "transtek.data.processed"

    # General metrics
    TASK_EXECUTION_TIME = "task.execution_time"
    TASK_SUCCESS = "task.success"
    TASK_FAILURE = "task.failure"
    DATABASE_QUERY_TIME = "database.query_time"
    DATABASE_CONNECTION_ERROR = "database.connection.error"


class PlatformNames:
    """Standardized platform names."""

    WITHINGS = "withings"
    FITBIT = "fitbit"
    TRANSTEK = "transtek"


class ErrorCodes:
    """Standardized error codes."""

    # HTTP Status Codes
    HTTP_OK = 200
    HTTP_UNAUTHORIZED = 401
    HTTP_FORBIDDEN = 403
    HTTP_NOT_FOUND = 404
    HTTP_RATE_LIMITED = 429
    HTTP_INTERNAL_SERVER_ERROR = 500
    HTTP_BAD_GATEWAY = 502
    HTTP_SERVICE_UNAVAILABLE = 503
    HTTP_GATEWAY_TIMEOUT = 504

    # Custom Error Codes
    TOKEN_EXPIRED = "TOKEN_EXPIRED"
    TOKEN_INVALID = "TOKEN_INVALID"
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"
    DATA_VALIDATION_ERROR = "DATA_VALIDATION_ERROR"
    DATABASE_CONNECTION_ERROR = "DATABASE_CONNECTION_ERROR"
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR"


class TaskStates:
    """Task execution states."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


class DataTypes:
    """Data measurement types."""

    WEIGHT = "weight"
    BLOOD_PRESSURE = "blood_pressure"
    HEART_RATE = "heart_rate"
    ACTIVITY = "activity"
    SLEEP = "sleep"
    SPO2 = "spo2"
    TEMPERATURE = "temperature"
    GLUCOSE = "glucose"


class ActivityCategories:
    """Activity category constants."""

    STEPS = "steps"
    DISTANCE = "distance"
    CALORIES = "calories"
    ACTIVE_MINUTES = "active_minutes"
    EXERCISE = "exercise"
    SEDENTARY = "sedentary"


class SleepStages:
    """Sleep stage constants."""

    AWAKE = "awake"
    LIGHT = "light"
    DEEP = "deep"
    REM = "rem"


class Units:
    """Measurement units."""

    # Weight
    KILOGRAMS = "kg"
    POUNDS = "lbs"

    # Distance
    METERS = "m"
    KILOMETERS = "km"
    MILES = "mi"
    FEET = "ft"
    INCHES = "in"
    CENTIMETERS = "cm"

    # Time
    SECONDS = "s"
    MINUTES = "min"
    HOURS = "h"
    DAYS = "d"

    # Pressure
    MMHG = "mmHg"

    # Heart Rate
    BPM = "bpm"

    # Temperature
    CELSIUS = "°C"
    FAHRENHEIT = "°F"

    # Blood Glucose
    MG_DL = "mg/dL"
    MMOL_L = "mmol/L"

    # Oxygen Saturation
    PERCENT = "%"


class LogLevels:
    """Logging levels."""

    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class EnvironmentTypes:
    """Environment types."""

    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TEST = "test"


# Validation Constants
MIN_HEART_RATE = 30  # bpm
MAX_HEART_RATE = 220  # bpm
MIN_BLOOD_PRESSURE_SYSTOLIC = 70  # mmHg
MAX_BLOOD_PRESSURE_SYSTOLIC = 250  # mmHg
MIN_BLOOD_PRESSURE_DIASTOLIC = 40  # mmHg
MAX_BLOOD_PRESSURE_DIASTOLIC = 150  # mmHg
MIN_WEIGHT_KG = 20.0  # kg
MAX_WEIGHT_KG = 300.0  # kg
MIN_SPO2 = 70  # %
MAX_SPO2 = 100  # %

# File and Data Limits
MAX_FILE_SIZE_MB = 100
MAX_RECORDS_PER_BATCH = 1000
MAX_MESSAGE_SIZE_KB = 256

# Cache Configuration
DEFAULT_CACHE_TTL_SECONDS = 300  # 5 minutes
TOKEN_CACHE_TTL_SECONDS = 3600  # 1 hour
USER_DATA_CACHE_TTL_SECONDS = 600  # 10 minutes
