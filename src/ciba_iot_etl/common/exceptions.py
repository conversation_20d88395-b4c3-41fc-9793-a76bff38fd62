"""Custom exceptions for CIBA IoT ETL operations."""

import pendulum


class CibaIoTETLException(Exception):
    """Base exception for CIBA IoT ETL operations."""

    def __init__(self, message: str, context: dict = None):
        super().__init__(message)
        self.message = message
        self.context = context or {}


class TokenRefreshError(CibaIoTETLException):
    """Raised when token refresh fails."""

    pass


class APIRateLimitError(CibaIoTETLException):
    """Raised when API rate limit is exceeded."""

    pass


class DataProcessingError(CibaIoTETLException):
    """Raised when data processing fails."""

    pass


class DatabaseError(CibaIoTETLException):
    """Raised when database operations fail."""

    pass


class ConfigurationError(CibaIoTETLException):
    """Raised when configuration is invalid or missing."""

    pass


class ExternalServiceError(CibaIoTETLException):
    """Raised when external service calls fail."""

    def __init__(
        self,
        message: str,
        service_name: str,
        status_code: int = None,
        context: dict = None,
    ):
        super().__init__(message, context)
        self.service_name = service_name
        self.status_code = status_code


class ValidationError(CibaIoTETLException):
    """Raised when data validation fails."""

    pass


class AuthenticationError(CibaIoTETLException):
    """Raised when authentication fails."""

    pass


class AuthorizationError(CibaIoTETLException):
    """Raised when authorization fails."""

    pass


class RetryableError(CibaIoTETLException):
    """Base class for errors that can be retried."""

    def __init__(self, message: str, retry_after: int = None, context: dict = None):
        super().__init__(message, context)
        self.retry_after = retry_after  # Seconds to wait before retry


class NonRetryableError(CibaIoTETLException):
    """Base class for errors that should not be retried."""

    pass


# Specific API errors
class WithingsAPIError(ExternalServiceError):
    """Raised when Withings API calls fail."""

    def __init__(
        self,
        message: str,
        status_code: int = None,
        error_code: str = None,
        context: dict = None,
    ):
        super().__init__(message, "Withings", status_code, context)
        self.error_code = error_code


class FitbitAPIError(ExternalServiceError):
    """Raised when Fitbit API calls fail."""

    def __init__(
        self,
        message: str,
        status_code: int = None,
        error_code: str = None,
        context: dict = None,
    ):
        super().__init__(message, "Fitbit", status_code, context)
        self.error_code = error_code


class TranstekAPIError(ExternalServiceError):
    """Raised when Transtek API calls fail."""

    def __init__(
        self,
        message: str,
        status_code: int = None,
        error_code: str = None,
        context: dict = None,
    ):
        super().__init__(message, "Transtek", status_code, context)
        self.error_code = error_code


# Utility functions for error handling
def is_retryable_error(error: Exception) -> bool:
    """Check if an error is retryable.

    Args:
        error: The exception to check

    Returns:
        bool: True if the error can be retried, False otherwise
    """
    if isinstance(error, RetryableError):
        return True

    if isinstance(error, NonRetryableError):
        return False

    # Check for specific HTTP status codes that are retryable
    if hasattr(error, "status_code"):
        retryable_codes = {429, 500, 502, 503, 504}  # Rate limit, server errors
        return error.status_code in retryable_codes

    # Default to non-retryable for unknown errors
    return False


def get_retry_delay(error: Exception, attempt: int, base_delay: float = 1.0) -> float:
    """Get the delay before retrying an operation.

    Args:
        error: The exception that occurred
        attempt: The current attempt number (1-based)
        base_delay: Base delay in seconds

    Returns:
        float: Delay in seconds before retry
    """
    if isinstance(error, RetryableError) and error.retry_after:
        return error.retry_after

    # Exponential backoff with jitter
    import random

    delay = base_delay * (2 ** (attempt - 1))
    jitter = random.uniform(0.1, 0.3) * delay
    return delay + jitter


def create_error_context(
    operation: str, user_id: str = None, platform: str = None, **kwargs
) -> dict:
    """Create a standardized error context dictionary.

    Args:
        operation: The operation that failed
        user_id: Optional user ID
        platform: Optional platform name (withings, fitbit, etc.)
        **kwargs: Additional context fields

    Returns:
        dict: Error context dictionary
    """
    context = {
        "operation": operation,
        "timestamp": str(pendulum.now()),
    }

    if user_id:
        context["user_id"] = user_id

    if platform:
        context["platform"] = platform

    context.update(kwargs)
    return context
