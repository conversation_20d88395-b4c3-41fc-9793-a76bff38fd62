"""
Notification system for handling device connection issues.

This module provides functionality to notify users and administrators
when device connections need attention, such as when tokens expire
and require re-authentication.
"""

from enum import Enum
from typing import Dict, Any, Optional, Union
import json
from datetime import datetime

from loguru import logger


class NotificationType(Enum):
    """Types of notifications that can be sent."""

    TOKEN_EXPIRED = "token_expired"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    CONNECTION_ERROR = "connection_error"
    DEVICE_DISCONNECTED = "device_disconnected"
    GENERAL_ERROR = "general_error"


class DeviceType(Enum):
    """Types of devices that can generate notifications."""

    FITBIT = "fitbit"
    WITHINGS = "withings"
    APPLE_HEALTH = "apple_health"
    GOOGLE_FIT = "google_fit"
    OTHER = "other"


class NotificationManager:
    """
    Manages notifications for device connection issues.

    This class provides methods to send notifications when device connections
    need attention, such as when tokens expire and require re-authentication.
    """

    def __init__(self, notification_service=None):
        """
        Initialize the notification manager.

        Args:
            notification_service: Optional service to handle sending notifications.
                                 If None, notifications will be logged but not sent.
        """
        self.notification_service = notification_service

    async def notify_token_expired(
        self,
        device_type: DeviceType,
        device_id: Union[str, int],
        user_id: Union[str, int],
        details: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Send a notification that a device's tokens have expired and require re-authentication.

        Args:
            device_type: The type of device (Fitbit, Withings, etc.)
            device_id: The ID of the device connection
            user_id: The ID of the user who owns the device
            details: Optional additional details about the issue

        Returns:
            bool: True if notification was sent successfully, False otherwise
        """
        notification_data = {
            "type": NotificationType.TOKEN_EXPIRED.value,
            "device_type": device_type.value,
            "device_id": str(device_id),
            "user_id": str(user_id),
            "timestamp": datetime.now().isoformat(),
            "message": f"Your {device_type.value} device connection requires re-authentication.",
            "details": details or {},
            "action_required": True,
        }

        return await self._send_notification(notification_data)

    async def notify_connection_issue(
        self,
        device_type: DeviceType,
        device_id: Union[str, int],
        user_id: Union[str, int],
        issue_type: NotificationType,
        message: str,
        details: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Send a notification about a device connection issue.

        Args:
            device_type: The type of device (Fitbit, Withings, etc.)
            device_id: The ID of the device connection
            user_id: The ID of the user who owns the device
            issue_type: The type of issue
            message: A human-readable message describing the issue
            details: Optional additional details about the issue

        Returns:
            bool: True if notification was sent successfully, False otherwise
        """
        notification_data = {
            "type": issue_type.value,
            "device_type": device_type.value,
            "device_id": str(device_id),
            "user_id": str(user_id),
            "timestamp": datetime.now().isoformat(),
            "message": message,
            "details": details or {},
            "action_required": issue_type == NotificationType.TOKEN_EXPIRED,
        }

        return await self._send_notification(notification_data)

    async def _send_notification(self, notification_data: Dict[str, Any]) -> bool:
        """
        Send a notification using the configured notification service.

        Args:
            notification_data: The data to include in the notification

        Returns:
            bool: True if notification was sent successfully, False otherwise
        """
        try:
            # Log the notification
            logger.info(f"Notification: {json.dumps(notification_data)}")

            # If a notification service is configured, use it to send the notification
            if self.notification_service:
                return await self.notification_service.send(notification_data)

            # For now, just log that we would send a notification
            logger.info("No notification service configured. Would send notification.")
            return True

        except Exception as e:
            logger.error(f"Error sending notification: {e}")
            return False


# Create a singleton instance of the notification manager
notification_manager = NotificationManager()
