from tortoise import Tortoise
from ciba_iot_etl.settings import get_settings

settings = get_settings()


def get_tortoise_orm_config(db_url: str) -> dict:
    """Init tortoise orm config."""
    return {
        "connections": {"default": db_url},
        "apps": {
            "models": {
                "models": [
                    "aerich.models",
                    "ciba_iot_etl.models.db.ihealth",
                    "ciba_iot_etl.models.db.oruraring",
                    "ciba_iot_etl.models.db.blood_pressure",
                    "ciba_iot_etl.models.db.dexcom",
                    "ciba_iot_etl.models.db.fitbit",
                    "ciba_iot_etl.models.db.heart_rate",
                    "ciba_iot_etl.models.db.member",
                    "ciba_iot_etl.models.db.member_device",
                    "ciba_iot_etl.models.db.member_platform",
                    "ciba_iot_etl.models.db.member_state",
                    "ciba_iot_etl.models.db.weight",
                    "ciba_iot_etl.models.db.withings",
                    "ciba_iot_etl.models.db.sleep",
                    "ciba_iot_etl.models.db.activity",
                    "ciba_iot_etl.models.db.glucose_level",
                    "ciba_iot_etl.models.db.transtek",
                ],
                "default_connection": "default",
            },
        },
    }


async def init_db():
    await Tortoise.init(get_tortoise_orm_config(settings.default_db_url))


async def close_db():
    await Tortoise.close_connections()
