"""Centralized logging utilities for CIBA IoT ETL operations."""

import json
import traceback
from typing import Any, Dict
from loguru import logger

from ciba_iot_etl.common.constants import MetricNames


class MetricLogger:
    """Standardized metric logging for Datadog parsing."""

    @staticmethod
    def log_metric(
        metric_name: str,
        value: Any = 1,
        tags: Dict[str, Any] | None = None,
        extra: Dict[str, Any] | None = None,
    ) -> None:
        """Log a metric in standardized format for Datadog parsing.

        Args:
            metric_name: The name of the metric to log
            value: The metric value (default: 1)
            tags: Optional tags to include with the metric
            extra: Optional extra data to include in the log
        """
        log_data = {
            "metric_name": metric_name,
            "metric_value": value,
            "metric_type": "count",
            "tags": tags or {},
        }

        if extra:
            log_data.update(extra)

        logger.info(f"DATADOG_METRIC: {json.dumps(log_data)}")

    @staticmethod
    def log_token_refresh_success(
        platform: str,
        user_id: str,
        method: str = "primary",
        execution_time: float | None = None,
    ) -> None:
        """Log successful token refresh.

        Args:
            platform: Platform name (withings, fitbit, etc.)
            user_id: User ID for the platform
            method: Refresh method used (primary, fallback, etc.)
            execution_time: Optional execution time in seconds
        """
        metric_name = getattr(MetricNames, f"{platform.upper()}_TOKEN_REFRESH_SUCCESS")
        tags = {
            "platform": platform,
            "user_id": user_id,
            "method": method,
        }

        extra = {}
        if execution_time is not None:
            extra["execution_time"] = execution_time

        MetricLogger.log_metric(metric_name, tags=tags, extra=extra)

    @staticmethod
    def log_token_refresh_failure(
        platform: str,
        user_id: str,
        error_type: str,
        error_message: str,
        method: str = "primary",
    ) -> None:
        """Log failed token refresh.

        Args:
            platform: Platform name (withings, fitbit, etc.)
            user_id: User ID for the platform
            error_type: Type of error that occurred
            error_message: Error message
            method: Refresh method used (primary, fallback, etc.)
        """
        metric_name = getattr(MetricNames, f"{platform.upper()}_TOKEN_REFRESH_FAILURE")
        tags = {
            "platform": platform,
            "user_id": user_id,
            "error_type": error_type,
            "method": method,
        }

        extra = {
            "error_message": error_message,
        }

        MetricLogger.log_metric(metric_name, tags=tags, extra=extra)

    @staticmethod
    def log_api_error(
        platform: str,
        user_id: str,
        error_type: str,
        status_code: int | None = None,
        error_message: str | None = None,
    ) -> None:
        """Log API errors.

        Args:
            platform: Platform name (withings, fitbit, etc.)
            user_id: User ID for the platform
            error_type: Type of error that occurred
            status_code: Optional HTTP status code
            error_message: Optional error message
        """
        metric_name = getattr(MetricNames, f"{platform.upper()}_API_ERROR")
        tags = {
            "platform": platform,
            "user_id": user_id,
            "error_type": error_type,
        }

        if status_code is not None:
            tags["status_code"] = str(status_code)

        extra = {}
        if error_message:
            extra["error_message"] = error_message

        MetricLogger.log_metric(metric_name, tags=tags, extra=extra)

    @staticmethod
    def log_processing_error(
        platform: str,
        user_id: str,
        operation: str,
        error_type: str,
        error_message: str,
        context: Dict[str, Any] | None = None,
    ) -> None:
        """Log data processing errors.

        Args:
            platform: Platform name (withings, fitbit, etc.)
            user_id: User ID for the platform
            operation: Operation that failed
            error_type: Type of error that occurred
            error_message: Error message
            context: Optional additional context
        """
        metric_name = getattr(MetricNames, f"{platform.upper()}_PROCESSING_ERROR")
        tags = {
            "platform": platform,
            "user_id": user_id,
            "operation": operation,
            "error_type": error_type,
        }

        extra = {
            "error_message": error_message,
        }

        if context:
            extra["context"] = context

        MetricLogger.log_metric(metric_name, tags=tags, extra=extra)

    @staticmethod
    def log_data_processed(
        platform: str,
        user_id: str,
        data_type: str,
        record_count: int,
        execution_time: float | None = None,
    ) -> None:
        """Log successful data processing.

        Args:
            platform: Platform name (withings, fitbit, etc.)
            user_id: User ID for the platform
            data_type: Type of data processed (weight, activity, etc.)
            record_count: Number of records processed
            execution_time: Optional execution time in seconds
        """
        metric_name = getattr(MetricNames, f"{platform.upper()}_DATA_PROCESSED")
        tags = {
            "platform": platform,
            "user_id": user_id,
            "data_type": data_type,
        }

        extra = {
            "record_count": record_count,
        }

        if execution_time is not None:
            extra["execution_time"] = execution_time

        MetricLogger.log_metric(metric_name, value=record_count, tags=tags, extra=extra)

    @staticmethod
    def log_task_execution(
        task_name: str,
        success: bool,
        execution_time: float,
        error_type: str | None = None,
        error_message: str | None = None,
        context: Dict[str, Any] | None = None,
    ) -> None:
        """Log task execution metrics.

        Args:
            task_name: Name of the task
            success: Whether the task succeeded
            execution_time: Execution time in seconds
            error_type: Optional error type if task failed
            error_message: Optional error message if task failed
            context: Optional additional context
        """
        metric_name = MetricNames.TASK_SUCCESS if success else MetricNames.TASK_FAILURE
        tags = {
            "task_name": task_name,
            "success": success,
        }

        extra = {
            "execution_time": execution_time,
        }

        if not success:
            if error_type:
                tags["error_type"] = error_type
            if error_message:
                extra["error_message"] = error_message

        if context:
            extra["context"] = context

        MetricLogger.log_metric(metric_name, tags=tags, extra=extra)


class ErrorLogger:
    """Standardized error logging utilities."""

    @staticmethod
    def log_error(
        message: str,
        error: Exception | None = None,
        context: Dict[str, Any] | None = None,
        include_traceback: bool = True,
    ) -> None:
        """Log errors in standardized format.

        Args:
            message: Error message
            error: Optional exception object
            context: Optional context information
            include_traceback: Whether to include traceback in logs
        """
        log_data = {"message": message}

        if context:
            log_data.update(context)

        if error:
            log_data["error_type"] = type(error).__name__
            log_data["error_message"] = str(error)

            if include_traceback:
                log_data["traceback"] = traceback.format_exc()

        logger.error(json.dumps(log_data))

    @staticmethod
    def log_warning(
        message: str,
        context: Dict[str, Any] | None = None,
    ) -> None:
        """Log warnings in standardized format.

        Args:
            message: Warning message
            context: Optional context information
        """
        log_data = {"message": message}

        if context:
            log_data.update(context)

        logger.warning(json.dumps(log_data))

    @staticmethod
    def log_info(
        message: str,
        context: Dict[str, Any] | None = None,
    ) -> None:
        """Log info messages in standardized format.

        Args:
            message: Info message
            context: Optional context information
        """
        log_data = {"message": message}

        if context:
            log_data.update(context)

        logger.info(json.dumps(log_data))


# Convenience functions for backward compatibility
def log_metric(
    metric_name: str,
    value: Any = 1,
    tags: Dict[str, Any] | None = None,
    extra: Dict[str, Any] | None = None,
) -> None:
    """Convenience function for logging metrics."""
    MetricLogger.log_metric(metric_name, value, tags, extra)


def log_error(
    message: str,
    error: Exception | None = None,
    context: Dict[str, Any] | None = None,
    include_traceback: bool = True,
) -> None:
    """Convenience function for logging errors."""
    ErrorLogger.log_error(message, error, context, include_traceback)
