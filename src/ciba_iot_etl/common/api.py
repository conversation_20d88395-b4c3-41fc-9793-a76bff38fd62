import httpx
from loguru import logger
from types import LambdaType
from typing import Union, Dict, Any, Optional
import asyncio
from http import HTTPStatus

ParamsType = Dict[str, Union[str, int, bool]]


def update_params(
    params: ParamsType, name: str, current_value: Any, new_value: Any = None
) -> None:
    """Add a conditional param to a params dict."""
    if current_value is None:
        return

    if isinstance(new_value, LambdaType):
        params[name] = new_value(current_value)
    else:
        params[name] = new_value or current_value


class BaseLoaderException(Exception):
    pass


class BaseLoader:
    """
    Api Base class for loaders
    """

    def __init__(self, limit_calls: int = 100, limit_period: int = 60):
        self.limit_calls = limit_calls
        self.limit_period = limit_period
        self.api_headers = {"Content-Type": "application/json"}

    async def _retry_with_backoff(
        self,
        url: str,
        method: str = "GET",
        payload: Optional[Dict[str, Any]] = None,
        querystring: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        proxies: Optional[Dict[str, str]] = None,
        max_retries: int = 5,
        initial_backoff: float = 1.0,
    ):
        """
        Internal function to handle retries with exponential backoff.
        """
        retries = 0
        while retries < max_retries:
            try:
                # In httpx 0.28.0+, proxies parameter is deprecated for AsyncClient constructor
                # Instead, we use the proxy parameter directly in the AsyncClient
                # Expected format for proxy_url: "***********************************:proxy_port"
                proxy_url = proxies.get("all", None) if proxies else None
                async with httpx.AsyncClient(proxy=proxy_url) as client:
                    response = await client.request(
                        method,
                        url=url,
                        data=payload,
                        json=payload,
                        params=querystring,
                        headers=headers,
                    )

                    if response.status_code == HTTPStatus.INTERNAL_SERVER_ERROR:
                        if "fitbit-rate-limit-limit" in response.headers:
                            logger.error(
                                f"Rate limit: {response.headers['fitbit-rate-limit-limit']}"
                            )
                            logger.error(
                                f"Rate limit remaining: {response.headers['fitbit-rate-limit-remaining']}"
                            )
                            logger.error(
                                f"Rate limit reset: {response.headers['fitbit-rate-limit-reset']}"
                            )
                        retries += 1
                        if retries < max_retries:
                            backoff_time = initial_backoff * (2 ** (retries - 1))
                            logger.warning(
                                f"Rate limit exceeded. Retrying in {backoff_time} seconds..."
                            )
                            await asyncio.sleep(backoff_time)
                            continue
                        else:
                            logger.error("Max retries reached. Giving up.")
                            return {
                                "error": "Max retries reached",
                                "status_code": response.status_code,
                            }

                    return response  # Return the response if no retry is needed

            except Exception as err:
                logger.error(f"Request failed: {err}")
                retries += 1
                if retries < max_retries:
                    backoff_time = initial_backoff * (2 ** (retries - 1))
                    logger.warning(f"Retrying in {backoff_time} seconds...")
                    await asyncio.sleep(backoff_time)
                else:
                    logger.error("Max retries reached. Giving up.")
                    return {
                        "error": str(err),
                        "status_code": HTTPStatus.INTERNAL_SERVER_ERROR,
                    }

        return {
            "error": "Max retries reached",
            "status_code": HTTPStatus.TOO_MANY_REQUESTS,
        }

    # @limits(calls=limit_calls, period=limit_period)
    async def call_api(
        self,
        url: str,
        method: str = "GET",
        payload: Optional[Dict[str, Any]] = None,
        querystring: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        proxies: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        querystring = querystring or {}
        proxies = proxies or {}

        response = await self._retry_with_backoff(
            url=url,
            method=method,
            payload=payload,
            querystring=querystring,
            headers=headers,
            proxies=proxies,
        )

        if isinstance(response, dict):
            return response
        if response.status_code == HTTPStatus.FOUND:
            return {"auth_url": response.headers["Location"]}

        if response.status_code == HTTPStatus.CONFLICT:
            logger.warning(f"Conflict: {response.text}")
            return response.json()

        if response.status_code > HTTPStatus.NO_CONTENT:
            logger.error(
                f"Invalid HTTP request: {response.status_code}: {response.text}"
            )
            return {
                "error": response.text,
                "status_code": response.status_code,
            }
        if not response.text.strip():
            logger.error("Empty response received from the API")
            return {"error": "Empty response", "status_code": response.status_code}

        try:
            return response.json()
        except ValueError as e:
            logger.error(f"JSON decode error: {e}. Response text: {response.text}")
            return {
                "error": "Invalid JSON response",
                "status_code": response.status_code,
            }
        except Exception as err:
            logger.error(err)
            return {"error": str(err), "status_code": HTTPStatus.INTERNAL_SERVER_ERROR}
