"""Background task management with proper error handling and lifecycle management."""

import asyncio
from typing import Set, Callable
from loguru import logger


class BackgroundTaskManager:
    """Manages background tasks with proper error handling and cleanup."""

    def __init__(self):
        self._tasks: Set[asyncio.Task] = set()
        self._error_callback: Callable[[asyncio.Task], None] | None = None

    def set_error_callback(self, callback: Callable[[asyncio.Task], None]) -> None:
        """Set a custom error callback for task failures.

        Args:
            callback: Function to call when a task fails with an exception
        """
        self._error_callback = callback

    def create_task(
        self, coro, name: str | None = None, context: dict | None = None
    ) -> asyncio.Task:
        """Create a task with proper error handling and tracking.

        Args:
            coro: Coroutine to execute as a background task
            name: Optional name for the task (for debugging)
            context: Optional context information for logging

        Returns:
            asyncio.Task: The created task
        """
        task = asyncio.create_task(coro, name=name)
        self._tasks.add(task)

        # Store context in task for error reporting
        if context:
            task._context = context

        task.add_done_callback(self._task_done_callback)
        return task

    def _task_done_callback(self, task: asyncio.Task) -> None:
        """Handle task completion and cleanup.

        Args:
            task: The completed task
        """
        self._tasks.discard(task)

        # Check if task was cancelled or had an exception
        try:
            exception = task.exception()
        except asyncio.CancelledError:
            # Task was cancelled - this is not an error, just log it as info
            task_name = task.get_name() or "unnamed_task"
            context = getattr(task, "_context", {})

            logger.info(
                f"Background task '{task_name}' was cancelled",
                extra={
                    "task_name": task_name,
                    "context": context,
                },
            )

            # Don't call error callback for cancelled tasks as they're not errors
            return

        # Handle actual exceptions
        if exception:
            task_name = task.get_name() or "unnamed_task"
            context = getattr(task, "_context", {})

            # Log the error with context
            logger.error(
                f"Background task '{task_name}' failed with exception: {exception}",
                extra={
                    "task_name": task_name,
                    "exception_type": type(exception).__name__,
                    "exception_message": str(exception),
                    "context": context,
                },
            )

            # Call custom error callback if set
            if self._error_callback:
                try:
                    self._error_callback(task)
                except Exception as callback_error:
                    logger.error(
                        f"Error callback failed for task '{task_name}': {callback_error}"
                    )

    def get_active_tasks(self) -> Set[asyncio.Task]:
        """Get all currently active tasks.

        Returns:
            Set[asyncio.Task]: Set of active tasks
        """
        return self._tasks.copy()

    def get_task_count(self) -> int:
        """Get the number of active tasks.

        Returns:
            int: Number of active tasks
        """
        return len(self._tasks)

    async def wait_for_all(self, timeout: float | None = None) -> None:
        """Wait for all active tasks to complete.

        Args:
            timeout: Optional timeout in seconds

        Raises:
            asyncio.TimeoutError: If timeout is reached before all tasks complete
        """
        if not self._tasks:
            return

        try:
            await asyncio.wait_for(
                asyncio.gather(*self._tasks, return_exceptions=True), timeout=timeout
            )
        except asyncio.TimeoutError:
            logger.warning(
                f"Timeout waiting for {len(self._tasks)} background tasks to complete"
            )
            raise

    async def cancel_all(self) -> None:
        """Cancel all active tasks and wait for them to complete."""
        if not self._tasks:
            return

        # Cancel all tasks
        for task in self._tasks:
            if not task.done():
                task.cancel()

        # Wait for all tasks to complete (including cancellation)
        if self._tasks:
            await asyncio.gather(*self._tasks, return_exceptions=True)

        logger.info(f"Cancelled {len(self._tasks)} background tasks")


class AsyncTaskContext:
    """Async context manager for background task management."""

    def __init__(self, task_manager: BackgroundTaskManager | None = None):
        self.task_manager = task_manager or BackgroundTaskManager()

    async def __aenter__(self) -> BackgroundTaskManager:
        return self.task_manager

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Clean up all tasks when exiting context."""
        try:
            # First try to wait for tasks to complete gracefully
            await self.task_manager.wait_for_all(timeout=5.0)
        except asyncio.TimeoutError:
            # If timeout, cancel all remaining tasks
            logger.warning(
                "Tasks did not complete within timeout, cancelling remaining tasks"
            )
            await self.task_manager.cancel_all()
        except Exception as e:
            logger.error(f"Error during task cleanup: {e}")
            # Still try to cancel tasks even if wait failed
            await self.task_manager.cancel_all()


# Global task manager instance for convenience
_global_task_manager = BackgroundTaskManager()


def create_background_task(
    coro, name: str | None = None, context: dict | None = None
) -> asyncio.Task:
    """Convenience function to create a background task using the global manager.

    Args:
        coro: Coroutine to execute as a background task
        name: Optional name for the task
        context: Optional context information for logging

    Returns:
        asyncio.Task: The created task
    """
    return _global_task_manager.create_task(coro, name=name, context=context)


def get_global_task_manager() -> BackgroundTaskManager:
    """Get the global task manager instance.

    Returns:
        BackgroundTaskManager: The global task manager
    """
    return _global_task_manager
