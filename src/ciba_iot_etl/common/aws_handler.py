import pandas as pd
from loguru import logger
import boto3
import io


async def save_df_to_s3(df: pd.DataFrame, bucket: str, key: str):
    """
    Save DataFrame to S3
    :param df: DataFrame
    :param bucket: S3 bucket
    :param key: S3 key
    """
    try:
        with io.StringIO() as csv_buffer:
            df.to_csv(csv_buffer, index=False)
            s3_resource = boto3.resource("s3")
            s3_resource.Object(bucket, key).put(Body=csv_buffer.getvalue())
        logger.info(f"Saved DataFrame to S3: {bucket}/{key}")
    except Exception as e:
        logger.exception(f"Error saving DataFrame to S3: {e}")
        raise e


def get_parameter(
    parameter_name: str, with_decryption: bool = True, region_name: str = "us-east-2"
) -> str | None:
    """
    Fetches a parameter from AWS Systems Manager Parameter Store.
    :param parameter_name: The name of the parameter
    :param with_decryption: Whether to decrypt the parameter value (if it is encrypted)
    :param region_name: The AWS region where the parameter is stored
    :return: The parameter value as a string
    """
    ssm_client = boto3.client(
        "ssm",
        region_name=region_name,
    )
    try:
        response = ssm_client.get_parameter(
            Name=parameter_name, WithDecryption=with_decryption
        )
    except ssm_client.exceptions.ParameterNotFound:
        logger.error(f"Parameter '{parameter_name}' not found in AWS SSM.")
        return None
    return response["Parameter"]["Value"]


def publish_to_sns(sns_client, sns_topic_arn, message: str):
    logger.info(f"Publishing to SNS: {message}")
    try:
        response = sns_client.publish(TopicArn=sns_topic_arn, Message=message)
        logger.info(f"Published to SNS: {response}")
    except Exception as e:
        logger.error(f"Failed to publish to SNS: {e}")
        raise e
