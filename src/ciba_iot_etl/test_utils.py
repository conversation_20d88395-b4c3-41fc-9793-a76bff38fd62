from ciba_iot_etl.models.db.activity import Activity
from ciba_iot_etl.models.db.blood_pressure import BloodPressure
from ciba_iot_etl.models.db.dexcom import Dexcom
from ciba_iot_etl.models.db.fitbit import Fitbit
from ciba_iot_etl.models.db.glucose_level import GlucoseLevel
from ciba_iot_etl.models.db.heart_rate import HeartRate
from ciba_iot_etl.models.db.ihealth import Ihealth
from ciba_iot_etl.models.db.member import Member
from ciba_iot_etl.models.db.member_device import MemberDevice
from ciba_iot_etl.models.db.member_platform import MemberPlatform
from ciba_iot_etl.models.db.member_state import MemberState
from ciba_iot_etl.models.db.oruraring import Oruraring
from ciba_iot_etl.models.db.sleep import Sleep
from ciba_iot_etl.models.db.transtek import Transtek
from ciba_iot_etl.models.db.weight import Weight
from ciba_iot_etl.models.db.withings import Withings


async def clean_db():
    """Helper function for cleaning up the test database."""
    await Activity.all().delete()
    await BloodPressure.all().delete()
    await Dexcom.all().delete()
    await Fitbit.all().delete()
    await GlucoseLevel.all().delete()
    await HeartRate.all().delete()
    await Ihealth.all().delete()
    await Member.all().delete()
    await MemberDevice.all().delete()
    await MemberPlatform.all().delete()
    await MemberState.all().delete()
    await Oruraring.all().delete()
    await Sleep.all().delete()
    await Transtek.all().delete()
    await Weight.all().delete()
    await Withings.all().delete()


async def add_test_member(email: str = "<EMAIL>"):
    """Helper function to create a test member for integration testing."""
    return await Member.create(email=email)
