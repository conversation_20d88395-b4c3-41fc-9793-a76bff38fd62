from contextlib import ContextDecorator
from typing import Any, Literal

from pandas import <PERSON>Frame
from sqlalchemy import text
from sqlalchemy.orm import Session

from ciba_iot_etl.settings import get_settings

"""Context manager for temporary table."""

settings = get_settings()


class TempContext(ContextDecorator):
    """Context manager for temporary table."""

    def __init__(self, db: Session, table_name: str, df: DataFrame) -> None:
        self.engine = db
        self.table_name = table_name
        self.df = df

    def __enter__(self) -> "TempContext":
        self.df.to_sql(self.table_name, settings.default_db_url, if_exists="replace")
        return self

    def __exit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> Literal[False]:
        self.engine.execute(text(f"DROP TABLE IF EXISTS {self.table_name}"))
        return False
