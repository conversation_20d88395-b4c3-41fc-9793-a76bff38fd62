# from airflow_client.client.model.dag_run import DAGRun
#
# from app.services.airflow import AirflowService
# from app.settings import get_settings
#
# settings = get_settings()
#
#
# def call_dag(dag_id_call: str) -> None:
#     """
#     Call dag in airflow
#     This trigger that will base on the dag_id call dags
#     """
#     airflow = AirflowService(
#         settings.AIRFLOW_HOST,
#         settings.AIRFLOW_USERNAME,
#         settings.AIRFLOW_PASSWORD,
#     )
#
#     dag_id = dag_id_call
#     dag_run = DAGRun(
#         conf={},
#         note="call_dag",
#     )
#     airflow.run_dag(dag_id, dag_run)
