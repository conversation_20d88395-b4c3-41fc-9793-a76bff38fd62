import base64
import hashlib
import hmac
import secrets
import string
from uuid import uuid4


from ciba_iot_etl.settings import get_settings

settings = get_settings()


def generate_hmac_sha256(msg: str, key: str) -> str:
    """
    Returns a string representation for HMAC-SHA256 signature
    """
    signature = (
        hmac.new(
            bytes(key, "utf-8"),
            msg=bytes(msg, "utf-8"),
            digestmod=hashlib.sha256,
        )
        .hexdigest()
        .lower()
    )
    return signature


def base64_url_encode(data: bytes) -> str:
    """Base64 URL encode"""
    # Base64 URL encode
    encoded = base64.urlsafe_b64encode(data).rstrip(b"=")
    return encoded.decode("utf-8")


def sha256_hash(data: str) -> bytes:
    """SHA256 hash"""
    sha256 = hashlib.sha256()
    sha256.update(data.encode("utf-8"))
    return sha256.digest()


def generate_code_verifier(min_length: int, max_length: int):
    # Generate a code verifier between 43 and 128 characters long
    code_verifier_length = secrets.choice(
        range(min_length, max_length)
    )  # upper bound is exclusive
    # Unreserved characters as per RFC 3986
    unreserved_chars = string.ascii_letters + string.digits + "-" + "." + "_" + "~"
    code_verifier = "".join(
        secrets.choice(unreserved_chars) for _ in range(code_verifier_length)
    )
    return code_verifier


def generate_code_challenge(code_verifier):
    # Compute SHA-256 hash of the code verifier
    sha256_hash = hashlib.sha256(code_verifier.encode("ascii")).digest()
    # Base64url encode the hash and remove padding
    code_challenge = base64.urlsafe_b64encode(sha256_hash).rstrip(b"=").decode("ascii")
    return code_challenge


def generate_unique_state() -> str:
    """Generate a unique 16-digit number from UUID."""
    return str(uuid4().int)[:16]
