from decimal import Decimal, ROUND_HALF_UP


def parse_kg_to_lb(value: Decimal, number_after_comma: int = 2) -> Decimal:
    """Method to transform KG to LB with precise rounding."""
    if type(value) is float:
        value = Decimal(value)
    elif type(value) is int:
        value = Decimal(value)
    elif not isinstance(value, Decimal):
        raise TypeError("Value must be of type Decimal, int, or float")

    # Use Decimal for the conversion factor
    conversion_factor = Decimal("2.20462")
    result = value * conversion_factor

    # Use quantize for precise rounding
    rounding_format = Decimal(f"1.{'0' * number_after_comma}")
    return result.quantize(rounding_format, rounding=ROUND_HALF_UP)
