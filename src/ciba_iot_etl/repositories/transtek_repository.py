from typing import Optional

from loguru import logger

from ciba_iot_etl.models.pydantic.transtek import TranstekStatus
from ciba_iot_etl.models.db.transtek import Transtek


class TranstekRepository:
    @staticmethod
    async def update_tracking_data(
        device_id: Optional[str],
        imei: Optional[str],
        tracking_number: str,
        carrier: str,
    ) -> Transtek:
        """Update carrier data for a Transtek device."""
        if not device_id and not imei:
            raise ValueError("Either device_id or imei must be provided")

        if device_id:
            device = await Transtek.filter(device_id=device_id).first()
        else:
            device = await Transtek.filter(imei=imei).first()

        if not device:
            logger.error(
                f"Transtek device not found in db: device_id={device_id}, imei={imei}"
            )
            raise ValueError("Device not found")

        device.tracking_number = tracking_number
        device.carrier = carrier.lower()
        device.status = TranstekStatus.SHIPPED
        await device.save()
        await device.fetch_related("member__platforms")

        return device

    @staticmethod
    async def get_device_by_member_id(member_id: str) -> Transtek:
        """Get Transtek device by member id."""
        return await Transtek.filter(member_id=member_id).first()

    @staticmethod
    async def get_device_by_device_id(device_id: str) -> Transtek:
        """Get Transtek device by device id."""
        return await Transtek.filter(device_id=device_id).first()

    @staticmethod
    async def get_device_by_imei(imei: str) -> Transtek:
        """Get Transtek device by imei."""
        return await Transtek.filter(imei=imei).first()
