import string
from typing import List
from uuid import UUID

import pendulum

from ciba_iot_etl.extract.transtek_api.core import MioConnectClient
from ciba_iot_etl.helpers.measurement import DeviceType
from ciba_iot_etl.models.db.member_device import MemberDevice
from ciba_iot_etl.models.db.transtek import Transtek
from ciba_iot_etl.models.pydantic.devices import MemberDeviceData
from ciba_iot_etl.models.pydantic.transtek import (
    MODEL_NUMBER_TO_TYPE,
    TranstekStatus,
)
from ciba_iot_etl.settings import get_settings

settings = get_settings()


class MemberDevicesRepository:
    @staticmethod
    async def add_device(member_id: UUID, device_data: MemberDeviceData):
        if device_data.vendor == DeviceType.TRANSTEK:
            mio_connect_client = MioConnectClient(api_key=settings.TRANSTEK_API_KEY)
            device = await mio_connect_client.get_device(device_data.external_id)

            new_transtek = Transtek(
                device_id=device["serialNumber"],
                imei=device["imei"],
                model=device["modelNumber"],
                device_type=MODEL_NUMBER_TO_TYPE[device["modelNumber"]],
                tracking_number=None,
                carrier=None,
                timezone=None,
                last_status_report=None,
                status=TranstekStatus.PAIRED,
                member_id=member_id,
            )
            await new_transtek.save()

        new_device = MemberDevice(**device_data.model_dump())
        new_device.member_id = member_id

        await new_device.save()

        return new_device

    @staticmethod
    async def get_devices(member_id: UUID):
        return await MemberDevice.filter(member_id=member_id).all()

    @staticmethod
    async def get_devices_by_vendor(member_id: UUID, vendor: DeviceType):
        return await MemberDevice.filter(
            member_id=member_id,
            vendor=vendor,
        ).all()

    @staticmethod
    async def mark_disconnected_devices(member_id: UUID, devices_ids: List[str]):
        current_date = pendulum.now("UTC")

        await MemberDevice.filter(
            member_id=member_id, external_id__in=devices_ids
        ).update(disconnected_at=current_date, disconnected=True)

    @staticmethod
    async def disconnect_device(member_id: UUID, external_id: string):
        await MemberDevice.filter(member_id=member_id, external_id=external_id).delete()
