"""Enhanced monitoring and metrics for token management."""

import json
from typing import Dict, Any, Optional
from enum import Enum
from datetime import datetime
from loguru import logger

from ciba_iot_etl.models.pydantic.token_management import (
    TokenErrorType,
    TokenRefreshResult,
)


def _sanitize_for_json(obj: Any) -> Any:
    """Sanitize objects for JSON serialization, handling mocks and other non-serializable objects."""
    from unittest.mock import MagicMock

    if isinstance(obj, MagicMock):
        return str(obj)
    elif isinstance(obj, dict):
        return {k: _sanitize_for_json(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [_sanitize_for_json(item) for item in obj]
    elif hasattr(obj, "__dict__") and not isinstance(
        obj, (str, int, float, bool, type(None))
    ):
        # For complex objects, convert to string representation
        return str(obj)
    else:
        return obj


def log_metric(
    metric_name: str,
    value: Any = 1,
    tags: Dict[str, Any] = None,
    extra: Dict[str, Any] = None,
) -> None:
    """
    Log a metric in a format that can be easily parsed by Datadog.

    Args:
        metric_name: The name of the metric to log
        value: The value of the metric (default: 1 for counter-type metrics)
        tags: Dictionary of tags to associate with the metric
        extra: Additional information to include in the log
    """
    log_data = {
        "metric_name": metric_name,
        "metric_value": value,
        "metric_type": "count",
        "tags": tags or {},
    }

    if extra:
        log_data.update(extra)

    # Sanitize data for JSON serialization and log with a consistent prefix for easy parsing
    sanitized_data = _sanitize_for_json(log_data)
    logger.info(f"DATADOG_METRIC: {json.dumps(sanitized_data)}")


class TokenMetricType(str, Enum):
    """Types of token-related metrics."""

    REFRESH_SUCCESS = "token.refresh.success"
    REFRESH_FAILURE = "token.refresh.failure"
    FALLBACK_USED = "token.fallback.used"
    FALLBACK_SUCCESS = "token.fallback.success"
    FALLBACK_FAILURE = "token.fallback.failure"
    REAUTH_REQUIRED = "token.reauth.required"
    TOKEN_EXPIRED = "token.expired"
    RATE_LIMITED = "token.rate_limited"
    NETWORK_ERROR = "token.network_error"


class TokenMetricsCollector:
    """Centralized token metrics collection and analysis."""

    def __init__(self):
        self.metrics_buffer = []
        self.alert_thresholds = {
            "fallback_usage_rate": 0.10,  # Alert if >10% of refreshes use fallback
            "reauth_rate": 0.05,  # Alert if >5% of refreshes require reauth
            "failure_rate": 0.15,  # Alert if >15% of refreshes fail
        }

    def log_token_refresh_result(
        self,
        result: TokenRefreshResult,
        platform: str,
        connection_id: str,
        additional_context: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        Log comprehensive metrics for a token refresh operation.

        Args:
            result: The token refresh result
            platform: Platform name (fitbit, withings)
            connection_id: Connection identifier
            additional_context: Additional context for metrics
        """
        base_tags = {
            "platform": platform,
            "connection_id": connection_id,
            "success": str(result.success),
            "used_fallback": str(result.used_fallback),
            "requires_reauth": str(result.requires_reauth),
            "error_type": result.error_type.value
            if result.error_type and hasattr(result.error_type, "value")
            else str(result.error_type)
            if result.error_type
            else "none",
        }

        base_extra = {
            "error_message": result.error_message,
            "timestamp": datetime.now().isoformat(),
            **(additional_context or {}),
        }

        if result.success:
            # Log success metrics
            log_metric(
                TokenMetricType.REFRESH_SUCCESS, tags=base_tags, extra=base_extra
            )

            if result.used_fallback:
                # Log fallback success
                log_metric(
                    TokenMetricType.FALLBACK_SUCCESS,
                    tags={**base_tags, "fallback_reason": "primary_token_invalid"},
                    extra=base_extra,
                )

                logger.info(
                    f"Token refresh successful using fallback for {platform} connection {connection_id}",
                    extra={"platform": platform, "connection_id": connection_id},
                )
        else:
            # Log failure metrics
            log_metric(
                TokenMetricType.REFRESH_FAILURE, tags=base_tags, extra=base_extra
            )

            # Log specific error type metrics
            if result.error_type:
                self._log_error_specific_metrics(
                    result, platform, connection_id, base_extra
                )

            if result.requires_reauth:
                log_metric(
                    TokenMetricType.REAUTH_REQUIRED,
                    tags={
                        **base_tags,
                        "reason": result.error_type.value
                        if result.error_type and hasattr(result.error_type, "value")
                        else str(result.error_type)
                        if result.error_type
                        else "unknown",
                    },
                    extra=base_extra,
                )

                logger.warning(
                    f"Re-authentication required for {platform} connection {connection_id}: {result.error_message}",
                    extra={"platform": platform, "connection_id": connection_id},
                )

    def _log_error_specific_metrics(
        self,
        result: TokenRefreshResult,
        platform: str,
        connection_id: str,
        base_extra: Dict[str, Any],
    ) -> None:
        """Log metrics specific to different error types."""

        error_tags = {
            "platform": platform,
            "connection_id": connection_id,
            "error_type": result.error_type.value
            if result.error_type and hasattr(result.error_type, "value")
            else str(result.error_type)
            if result.error_type
            else "none",
        }

        # Handle both enum and string error types
        error_type_str = (
            result.error_type.value
            if hasattr(result.error_type, "value")
            else str(result.error_type)
        )

        if error_type_str == TokenErrorType.RATE_LIMIT.value:
            log_metric(TokenMetricType.RATE_LIMITED, tags=error_tags, extra=base_extra)
        elif error_type_str == TokenErrorType.NETWORK_ERROR.value:
            log_metric(TokenMetricType.NETWORK_ERROR, tags=error_tags, extra=base_extra)
        elif error_type_str == TokenErrorType.EXPIRED_TOKEN.value:
            log_metric(TokenMetricType.TOKEN_EXPIRED, tags=error_tags, extra=base_extra)

    def log_fallback_attempt(
        self,
        platform: str,
        connection_id: str,
        reason: str,
        has_fallback_token: bool,
        fallback_token_expired: bool,
    ) -> None:
        """
        Log when fallback logic is attempted.

        Args:
            platform: Platform name
            connection_id: Connection identifier
            reason: Reason for attempting fallback
            has_fallback_token: Whether old_refresh_token exists
            fallback_token_expired: Whether old_refresh_token is expired
        """
        log_metric(
            TokenMetricType.FALLBACK_USED,
            tags={
                "platform": platform,
                "connection_id": connection_id,
                "reason": reason,
                "has_fallback_token": str(has_fallback_token),
                "fallback_token_expired": str(fallback_token_expired),
                "can_attempt_fallback": str(
                    has_fallback_token and not fallback_token_expired
                ),
            },
            extra={
                "timestamp": datetime.now().isoformat(),
                "fallback_available": has_fallback_token and not fallback_token_expired,
            },
        )

    def check_alert_conditions(
        self, platform: str, time_window_minutes: int = 60
    ) -> Dict[str, Any]:
        """
        Check if any alert conditions are met based on recent metrics.

        Args:
            platform: Platform to check alerts for
            time_window_minutes: Time window for analysis

        Returns:
            Dictionary with alert information
        """
        # This would typically query a metrics store
        # For now, return a placeholder structure
        alerts = {
            "platform": platform,
            "time_window_minutes": time_window_minutes,
            "alerts": [],
            "metrics_summary": {
                "total_refresh_attempts": 0,
                "success_rate": 0.0,
                "fallback_usage_rate": 0.0,
                "reauth_rate": 0.0,
            },
        }

        # In a real implementation, this would:
        # 1. Query metrics from the last time_window_minutes
        # 2. Calculate rates and percentages
        # 3. Compare against thresholds
        # 4. Generate alerts if thresholds are exceeded

        return alerts

    def generate_token_health_report(
        self, platform: str, days: int = 7
    ) -> Dict[str, Any]:
        """
        Generate a comprehensive token health report.

        Args:
            platform: Platform to generate report for
            days: Number of days to analyze

        Returns:
            Comprehensive health report
        """
        report = {
            "platform": platform,
            "analysis_period_days": days,
            "generated_at": datetime.now().isoformat(),
            "summary": {
                "total_connections": 0,
                "healthy_connections": 0,
                "connections_requiring_reauth": 0,
                "average_token_refresh_frequency": "0 per day",
            },
            "metrics": {
                "token_refresh_success_rate": 0.0,
                "fallback_usage_rate": 0.0,
                "reauth_required_rate": 0.0,
                "average_response_time_ms": 0.0,
            },
            "trends": {
                "refresh_attempts_trend": "stable",
                "success_rate_trend": "stable",
                "fallback_usage_trend": "stable",
            },
            "recommendations": [],
        }

        # In a real implementation, this would:
        # 1. Query connection health status
        # 2. Analyze token refresh patterns
        # 3. Calculate success rates and trends
        # 4. Generate actionable recommendations

        return report


# Global metrics collector instance
token_metrics = TokenMetricsCollector()


def log_token_refresh_metrics(
    result: TokenRefreshResult,
    platform: str,
    connection_id: str,
    additional_context: Optional[Dict[str, Any]] = None,
) -> None:
    """
    Convenience function to log token refresh metrics.

    Args:
        result: Token refresh result
        platform: Platform name
        connection_id: Connection identifier
        additional_context: Additional context for metrics
    """
    token_metrics.log_token_refresh_result(
        result, platform, connection_id, additional_context
    )


def log_fallback_attempt_metrics(
    platform: str,
    connection_id: str,
    reason: str,
    has_fallback_token: bool,
    fallback_token_expired: bool,
) -> None:
    """
    Convenience function to log fallback attempt metrics.

    Args:
        platform: Platform name
        connection_id: Connection identifier
        reason: Reason for attempting fallback
        has_fallback_token: Whether old_refresh_token exists
        fallback_token_expired: Whether old_refresh_token is expired
    """
    token_metrics.log_fallback_attempt(
        platform, connection_id, reason, has_fallback_token, fallback_token_expired
    )
