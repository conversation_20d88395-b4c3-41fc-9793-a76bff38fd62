from tortoise import Model
from tortoise.fields import ForeignKeyField, ForeignKeyRelation, IntField

from ciba_iot_etl.models.db.base import MeasurementMixin, TimestampMixin
from ciba_iot_etl.models.db.member import Member


class BloodPressure(Model, TimestampMixin, MeasurementMixin):
    """Table to store members' blood pressure measures"""

    diastolic_value = IntField()
    systolic_value = IntField()
    member: ForeignKeyRelation[Member] = ForeignKeyField(
        "models.Member", related_name="blood_pressures"
    )

    class Meta:
        table = "blood_pressures"
        indexes = (
            ("created_at",),
            ("member_id", "created_at"),
        )
