from tortoise import Model
from tortoise.fields import ForeignKeyField, ForeignKeyRelation, IntField, DatetimeField

from ciba_iot_etl.models.db.base import MeasurementMixin, TimestampMixin
from ciba_iot_etl.models.db.member import Member


class Sleep(Model, TimestampMixin, MeasurementMixin):
    """Table to store members' sleep measures"""

    start_time = DatetimeField()
    end_time = DatetimeField()
    duration = IntField()
    bed_time = IntField()  # totalTimeInBed
    asleep_time = IntField(default=0)

    deep_time = IntField()
    deep_count = IntField()

    light_time = IntField()
    light_count = IntField()

    rem_time = IntField()
    rem_count = IntField()

    wake_time = IntField()
    wake_count = IntField()

    efficiency = IntField()

    member: ForeignKeyRelation[Member] = ForeignKeyField(
        "models.Member", related_name="sleep"
    )

    class Meta:
        table = "sleep"
        indexes = (
            ("created_at",),
            ("member_id", "created_at"),
        )
