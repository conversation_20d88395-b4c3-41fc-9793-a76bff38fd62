from tortoise import Model
from tortoise.fields import IntField, ForeignKeyRelation, ForeignKeyField

from ciba_iot_etl.models.db.base import MeasurementMixin, TimestampMixin
from ciba_iot_etl.models.db.member import Member


class GlucoseLevel(Model, TimestampMixin, MeasurementMixin):
    """
    Table representing a glucose level measures.
    """

    value = IntField()
    member: ForeignKeyRelation[Member] = ForeignKeyField(
        "models.Member", related_name="glucose_levels"
    )

    class Meta:
        table = "glucose_levels"
        indexes = (
            ("created_at",),
            ("member_id", "created_at"),
        )
