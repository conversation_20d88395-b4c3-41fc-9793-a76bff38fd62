from datetime import datetime
from typing import Dict
from uuid import uuid4

from tortoise import Model
from tortoise.fields import <PERSON><PERSON><PERSON><PERSON>, ReverseRelation, UUIDField

from ciba_iot_etl.models.db.base import TimestampMixin
from ciba_iot_etl.models.db.base import DeviceType


class Member(Model, TimestampMixin):
    """Table to store Member data."""

    id = UUIDField(pk=True, default=uuid4)
    email = CharField(max_length=255, unique=True)

    # Define a reverse relation
    platforms = ReverseRelation["MemberPlatform"]
    state = ReverseRelation["MemberState"]
    withings = ReverseRelation["Withings"]
    fitbit = ReverseRelation["Fitbit"]
    dexcom = ReverseRelation["Dexcom"]
    devices = ReverseRelation["MemberDevice"]
    weights = ReverseRelation["Weight"]
    heart_rates = ReverseRelation["HeartRate"]
    blood_pressures = ReverseRelation["BloodPressure"]
    sleep = ReverseRelation["Sleep"]
    activities = ReverseRelation["Activity"]
    glucose_levels = ReverseRelation["GlucoseLevel"]
    transtek = ReverseRelation["Transtek"]

    @staticmethod
    async def get_by_platform(platform_type: str, platform_id: str) -> Model | None:
        """Check if user exists by external platform type and id"""
        return await Member.filter(
            platforms__platform_type=platform_type,
            platforms__platform_id=platform_id,
        ).get_or_none()

    async def get_withings_tokens(self) -> Dict[str, str | None]:
        """Get all withings tokens and IDs in a single query.

        Retrieves all Withings-related tokens and identifiers for this member
        in a single database query to optimize performance.

        Returns:
            Dict[str, str | None]: Dictionary containing:
                - access_token: The Withings access token
                - refresh_token: The Withings refresh token
                - user_id: The Withings user ID
                - id: The Withings connection ID
                Values are None if no Withings connection exists.

        Raises:
            DatabaseError: If database query fails.
        """
        withings_instance = await self.withings.first()
        if withings_instance:
            return {
                "access_token": withings_instance.access_token,
                "refresh_token": withings_instance.refresh_token,
                "user_id": str(withings_instance.user_id),
                "id": str(withings_instance.id),
            }
        return {
            "access_token": None,
            "refresh_token": None,
            "user_id": None,
            "id": None,
        }

    async def get_fitbit_tokens(self) -> Dict[str, str | None]:
        """Get all fitbit tokens and IDs in a single query.

        Retrieves all Fitbit-related tokens and identifiers for this member
        in a single database query to optimize performance.

        Returns:
            Dict[str, str | None]: Dictionary containing:
                - access_token: The Fitbit access token
                - refresh_token: The Fitbit refresh token
                - user_id: The Fitbit user ID
                - id: The Fitbit connection ID
                Values are None if no Fitbit connection exists.

        Raises:
            DatabaseError: If database query fails.
        """
        fitbit_instance = await self.fitbit.first()
        if fitbit_instance:
            return {
                "access_token": fitbit_instance.access_token,
                "refresh_token": fitbit_instance.refresh_token,
                "user_id": str(fitbit_instance.user_id),
                "id": str(fitbit_instance.id),
            }
        return {
            "access_token": None,
            "refresh_token": None,
            "user_id": None,
            "id": None,
        }

    async def get_withings_access_token(self) -> str | None:
        """Return withings access token if exists.

        Returns:
            str | None: The Withings access token if a connection exists,
                       None otherwise.

        Raises:
            DatabaseError: If database query fails.
        """
        tokens = await self.get_withings_tokens()
        return tokens["access_token"]

    async def get_withings_refresh_token(self) -> str | None:
        """Return withings refresh token if exists.

        Returns:
            str | None: The Withings refresh token if a connection exists,
                       None otherwise.

        Raises:
            DatabaseError: If database query fails.
        """
        tokens = await self.get_withings_tokens()
        return tokens["refresh_token"]

    async def get_fitbit_access_token(self) -> str | None:
        """Return fitbit access token if exists"""
        tokens = await self.get_fitbit_tokens()
        return tokens["access_token"]

    async def get_fitbit_refresh_token(self) -> str | None:
        """Return fitbit refresh token if exists"""
        tokens = await self.get_fitbit_tokens()
        return tokens["refresh_token"]

    async def get_withings_id(self) -> str | None:
        """Return withings id if exists"""
        tokens = await self.get_withings_tokens()
        return tokens["id"]

    async def get_withings_user_id(self) -> str | None:
        """Return unique withings account user_id.

        Retrieves the user_id from the associated Withings connection
        for this member.

        Returns:
            str | None: The Withings user_id if a connection exists,
                          None otherwise.

        Raises:
            DatabaseError: If database query fails.
        """
        tokens = await self.get_withings_tokens()
        return tokens["user_id"]

    async def get_fitbit_id(self) -> str | None:
        """Return fitbit id if exists"""
        tokens = await self.get_fitbit_tokens()
        return tokens["id"]

    async def get_fitbit_user_id(self) -> str | None:
        """Return unique fitbit account user_id"""
        tokens = await self.get_fitbit_tokens()
        return tokens["user_id"]

    async def _get_platform_attribute(
        self, platform: str, attribute: str
    ) -> str | None:
        """Generic method to get platform attributes.

        Args:
            platform: Platform name (withings, fitbit, transtek)
            attribute: Attribute name to retrieve

        Returns:
            str | None: The attribute value if connection exists, None otherwise

        Raises:
            DatabaseError: If database query fails
        """
        platform_instance = await getattr(self, platform).first()
        if platform_instance:
            return str(getattr(platform_instance, attribute))
        return None

    async def get_transtek_device_id(self) -> str | None:
        """Return unique transtek device id.

        Retrieves the device_id from the associated Transtek connection
        for this member.

        Returns:
            str | None: The Transtek device_id if a connection exists,
                          None otherwise.

        Raises:
            DatabaseError: If database query fails.
        """
        return await self._get_platform_attribute("transtek", "device_id")

    async def delete_withings(self) -> int:
        """Delete related Withings models, return a count of deleted models"""
        return await self.withings.delete()

    async def delete_fitbit(self) -> int:
        """Delete related Fitbit models, return a count of deleted models"""
        return await self.fitbit.delete()

    async def get_platforms(self) -> list:
        """Retutn platforms id and type"""
        platforms = await self.platforms.all()
        return [
            {"type": platform.platform_type, "id": str(platform.platform_id)}
            for platform in platforms
        ]

    async def update_withings_credentional(self, refresh_token: str, access_token: str):
        """
        DEPRECATED: Save new access and refresh token.

        This method bypasses proper token management and should not be used.
        Use Withings.update_tokens() instead for proper token lifecycle management.
        """
        import warnings

        warnings.warn(
            "update_withings_credentional is deprecated. Use Withings.update_tokens() instead.",
            DeprecationWarning,
            stacklevel=2,
        )

        withings_instance = await self.withings.first()
        if withings_instance is None:
            return

        # ANTI-PATTERN: Direct field updates bypass proper token management
        withings_instance.refresh_token = refresh_token
        withings_instance.access_token = access_token
        await withings_instance.save()

    async def update_fitbit_credentials(self, refresh_token: str, access_token: str):
        """
        DEPRECATED: Save new access and refresh token.

        This method bypasses proper token management and should not be used.
        Use Fitbit.update_tokens() instead for proper token lifecycle management.
        """
        import warnings

        warnings.warn(
            "update_fitbit_credentials is deprecated. Use Fitbit.update_tokens() instead.",
            DeprecationWarning,
            stacklevel=2,
        )

        fitbit_instance = await self.fitbit.first()
        if fitbit_instance is None:
            return

        # ANTI-PATTERN: Direct field updates bypass proper token management
        fitbit_instance.refresh_token = refresh_token
        fitbit_instance.access_token = access_token
        await fitbit_instance.save()

    async def withings_user_valid(self, userid: str) -> bool:
        """validate user_id"""
        withings_instance = await self.withings.first()
        if withings_instance is None:
            return False
        return withings_instance.user_id == userid

    async def get_latest_fitbit_update(self) -> datetime | None:
        """
        For each of the Activity, Sleep, HeartRate, and BloodPressure tables
        (all device=DeviceType.FITBIT), this method:

        1. Finds the last (most recent) record in each table by ordering
           descending on updated_at and taking the first row.
        2. Among those 'last records', returns the earliest updated_at
           (i.e. the 'oldest' among the last-updates).

        Returns None if there are no records across any of those tables.
        """

        # Fetch the reverse relations so we can easily query them
        await self.fetch_related(
            "activity",  # <-- Make sure these match your ReverseRelation names
            "sleep",
            "heart_rates",
            "blood_pressures",
        )

        # Collect updated_at timestamps from each table's "most recent" entry
        last_updates = []
        for related_manager in [
            self.activity,
            self.sleep,
            self.heart_rates,
            self.blood_pressures,
        ]:
            last_record = (
                await related_manager.filter(device=DeviceType.FITBIT)
                .order_by("-updated_at")
                .first()
            )
            if last_record:
                last_updates.append(last_record.updated_at)

        if not last_updates:
            # No data across any of the tables for this member with DeviceType.FITBIT
            return None

        # Return the earliest among the 'most recent' updated_at values
        return min(last_updates)

    class Meta:
        table = "members"  # Specify the table name explicitly
