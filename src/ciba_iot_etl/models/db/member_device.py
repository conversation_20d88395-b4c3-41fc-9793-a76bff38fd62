from uuid import uuid4, UUID

from tortoise import Model
from tortoise.fields import (
    BooleanField,
    CharField,
    DatetimeField,
    ForeignKeyField,
    ForeignKeyRelation,
    IntEnumField,
    UUIDField,
)

from ciba_iot_etl.helpers.measurement import DeviceType
from ciba_iot_etl.models.db.base import TimestampMixin
from ciba_iot_etl.models.db.member import Member


class MemberDevice(Model, TimestampMixin):
    id = UUIDField(primary_key=True, default=uuid4)
    linked_at = DatetimeField(null=True)
    last_synced_at = DatetimeField()
    disconnected_at = DatetimeField(null=True)
    disconnected = BooleanField(default=False)
    timezone = CharField(max_length=64, default="UTC")
    external_id = CharField(max_length=256)
    device_type = CharField(max_length=128)
    model = CharField(max_length=128, null=True)
    vendor = IntEnumField(DeviceType)

    member_id: UUID
    member: ForeignKeyRelation[Member] = ForeignKeyField(
        "models.Member", related_name="devices"
    )

    class Meta:
        table = "member_devices"
        unique_together = ("member_id", "external_id")
