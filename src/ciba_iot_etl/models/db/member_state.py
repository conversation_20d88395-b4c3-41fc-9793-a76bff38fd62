from uuid import uuid4

from tortoise import Model
from tortoise.fields import (
    BigIntField,
    CharField,
    ForeignKeyField,
    ForeignKeyRelation,
    UUIDField,
    DatetimeField,
)

from ciba_iot_etl.models.db.base import TimestampMixin
from ciba_iot_etl.models.db.member import Member


class MemberState(Model, TimestampMixin):
    id = UUIDField(pk=True, default=uuid4)
    state = BigIntField()
    redirect_uri = CharField(max_length=255, null=True)
    code_verifier = CharField(max_length=128, null=True)
    sync_start_date = DatetimeField(null=True)

    member: ForeignKeyRelation[Member] = ForeignKeyField(
        "models.Member", related_name="states"
    )

    @staticmethod
    async def get_redirect_uri(state: str) -> str:
        """
        Method to search a redirect uri from a given state
        """
        instance = await MemberState.filter(state=state).get_or_none()
        if instance:
            return instance.redirect_uri
        return None

    @staticmethod
    async def get_member_by_state(state: str) -> Member:
        """
        Method to search a member from a given state
        """
        instance = await MemberState.filter(state=state).get_or_none()
        if instance:
            return await instance.member
        return None

    @staticmethod
    async def get_last_state(member: Member) -> str:
        """
        Get the most recent state associated with the member.
        """
        member_state = (
            await MemberState.filter(member=member).order_by("-created_at").first()
        )
        return member_state.state if member_state else ""

    class Meta:
        table = "member_states"  # Specify the table name explicitly
