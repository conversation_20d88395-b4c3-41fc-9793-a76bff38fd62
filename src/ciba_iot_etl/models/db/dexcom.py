from uuid import uuid4

from tortoise import Model
from tortoise.fields import (
    BooleanField,
    CharField,
    IntField,
    OneToOneField,
    OneToOneRelation,
    UUIDField,
)

from ciba_iot_etl.models.db.base import FieldExistsMixin, StatusMixin, TimestampMixin
from ciba_iot_etl.models.db.member import Member


class Dexcom(Model, TimestampMixin, StatusMixin, FieldExistsMixin):
    """Table to store Dexcom access tokens."""

    id = UUIDField(pk=True, default=uuid4)
    user_id = CharField(max_length=255, unique=True, null=True)
    access_token = CharField(max_length=850, unique=True)
    refresh_token = CharField(max_length=850, unique=True)
    expires_in = IntField()
    start = BooleanField(default=False)

    member: OneToOneRelation[Member] = OneToOneField(
        "models.Member", related_name="dexcom", null=True
    )
