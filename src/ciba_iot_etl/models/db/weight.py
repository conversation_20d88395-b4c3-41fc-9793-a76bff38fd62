from tortoise import Model
from tortoise.fields import DecimalField, ForeignKeyField, ForeignKeyRelation

from ciba_iot_etl.models.db.base import MeasurementMixin, TimestampMixin
from ciba_iot_etl.models.db.member import Member


class Weight(Model, TimestampMixin, MeasurementMixin):
    """Table to store members' weight measures"""

    value = DecimalField(max_digits=8, decimal_places=2)
    member: ForeignKeyRelation[Member] = ForeignKeyField(
        "models.Member", related_name="weights"
    )

    class Meta:
        table = "weights"
        indexes = (
            ("created_at",),
            ("member_id", "created_at"),
        )
