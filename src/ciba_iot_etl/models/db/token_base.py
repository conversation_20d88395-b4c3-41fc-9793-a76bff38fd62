"""Base model for token handling."""

from datetime import datetime
from typing import Dict, TypeVar

from tortoise import fields
from tortoise.models import Model

from ciba_iot_etl.utils.datetime_utils import (
    get_timezone_naive_now,
    is_datetime_expired,
)

# Type variable for the model class
T = TypeVar("T", bound="TokenBase")


class TokenBase(Model):
    """Base model for token handling.

    This abstract base class provides common functionality for token management
    that can be shared between different API integrations like Fitbit and Withings.
    """

    class Meta:
        """Meta class for TokenBase."""

        abstract = True

    # Common fields for token management
    access_token = fields.CharField(max_length=1024, null=True)
    refresh_token = fields.CharField(max_length=1024, null=True)
    old_refresh_token = fields.CharField(max_length=1024, null=True)
    expires_in = fields.IntField(default=0)

    # Timestamp fields
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
    access_token_expires_at = fields.DatetimeField(null=True)
    refresh_token_expires_at = fields.DatetimeField(null=True)
    old_refresh_token_expires_at = fields.DatetimeField(null=True)

    def is_access_token_expired(self) -> bool:
        """Check if the access token is expired."""
        return is_datetime_expired(
            self.access_token_expires_at,
            fallback_start=self.updated_at,
            fallback_seconds=self.expires_in,
        )

    def is_refresh_token_expired(self) -> bool:
        """Check if the refresh token is expired."""
        return is_datetime_expired(self.refresh_token_expires_at)

    def is_old_refresh_token_expired(self) -> bool:
        """Check if the old refresh token is expired."""
        if not self.old_refresh_token or not self.old_refresh_token_expires_at:
            # If no old refresh token or no expiration timestamp, consider it expired
            return True

        return is_datetime_expired(self.old_refresh_token_expires_at)

    @staticmethod
    def _calculate_base_timestamps() -> Dict[str, datetime]:
        """Calculate base timestamps for token updates.

        Returns:
            Dict[str, datetime]: A dictionary with the updated_at timestamp
        """
        return {"updated_at": get_timezone_naive_now()}
