from uuid import uuid4

from tortoise import Model
from tortoise.fields import BooleanField, OneToOneRelation, UUIDField

from ciba_iot_etl.models.db.base import FieldExistsMixin, StatusMixin, TimestampMixin
from ciba_iot_etl.models.db.member import Member


class Ihealth(Model, TimestampMixin, StatusMixin, FieldExistsMixin):
    """Table to store Ihealth mails."""

    id = UUIDField(pk=True, default=uuid4)
    deleted = BooleanField(default=False)

    member: OneToOneRelation[Member] = OneToOneRelation(
        "models.Member", related_name="ihealth", null=True
    )
