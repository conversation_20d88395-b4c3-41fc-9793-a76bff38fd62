from uuid import uuid4

from tortoise import Model
from tortoise.fields import (
    CharField,
    ForeignKeyField,
    ForeignKeyRelation,
    UUIDField,
)

from ciba_iot_etl.models.db.base import TimestampMixin
from ciba_iot_etl.models.db.member import Member


class MemberPlatform(Model, TimestampMixin):
    """
    platform_type: str
    is participant or patient, it is used to identify where to send data and other related stuff
    """

    id = UUIDField(pk=True, default=uuid4)
    platform_type = CharField(max_length=50)
    platform_id = UUIDField(default=uuid4)

    member: ForeignKeyRelation[Member] = ForeignKeyField(
        "models.Member", related_name="platforms"
    )

    class Meta:
        table = "member_platforms"  # Specify the table name explicitly
