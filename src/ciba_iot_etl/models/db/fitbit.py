from uuid import uuid4
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Dict

from tortoise.fields import (
    BooleanField,
    CharField,
    OneToOneField,
    OneToOneRelation,
    UUIDField,
)

from ciba_iot_etl.models.db.base import FieldExistsMixin, StatusMixin
from ciba_iot_etl.models.db.member import Member
from ciba_iot_etl.models.db.token_base import TokenBase


class Fitbit(TokenBase, StatusMixin, FieldExistsMixin):
    """Table to store Fitbit access tokens.

    Token Expiration Guidelines:
    - access_token: Expires after 8 hours (28800 seconds)
    - refresh_token: No explicit expiration, but should be refreshed regularly
    - old_refresh_token: Stored as a backup, but not used in the refresh flow
      (unlike Withings, Fitbit doesn't have the concept of an old refresh token
      that expires when a new access token is used)
    """

    id = UUIDField(pk=True, default=uuid4)
    user_id = CharField(max_length=255, unique=True)

    # Override the base class fields with specific constraints
    access_token = Char<PERSON>ield(max_length=305, unique=True)
    refresh_token = CharField(max_length=305, unique=True)
    old_refresh_token = CharField(
        max_length=305, null=True
    )  # Store previous refresh token for backup

    start = BooleanField(default=False)
    healthy = BooleanField(default=True)

    member: OneToOneRelation[Member] = OneToOneField(
        "models.Member", related_name="fitbit", null=True
    )

    # Token expiration methods are inherited from TokenBase

    @staticmethod
    def _calculate_expiration_timestamps(expires_in: int) -> Dict[str, datetime]:
        """Calculate expiration timestamps for tokens.

        This is a helper method for testing and internal use.

        Args:
            expires_in: Token expiration time in seconds

        Returns:
            Dict[str, datetime]: A dictionary with the calculated timestamps
        """
        # Get base timestamps
        timestamps = TokenBase._calculate_base_timestamps()

        # Add Fitbit-specific timestamps
        now = timestamps["updated_at"]
        timestamps["access_token_expires_at"] = now + timedelta(
            seconds=expires_in
        )  # Access token expires after specified time

        return timestamps

    @classmethod
    async def update_tokens(
        cls,
        fitbit_id: str,
        access_token: str,
        refresh_token: str,
        expires_in: int,
    ) -> "Fitbit":
        """Update tokens with proper expiration timestamps.

        Args:
            fitbit_id: The ID of the Fitbit connection to update
            access_token: New access token
            refresh_token: New refresh token
            expires_in: Token expiration time in seconds

        Returns:
            Updated Fitbit record
        """
        # Get current record to preserve old refresh token
        fitbit_connection = await cls.get(id=fitbit_id)
        old_refresh_token = fitbit_connection.refresh_token

        # Calculate expiration timestamps
        timestamps = cls._calculate_expiration_timestamps(expires_in)

        # Update the record
        await cls.filter(id=fitbit_id).update(
            access_token=access_token,
            refresh_token=refresh_token,
            old_refresh_token=old_refresh_token,
            expires_in=expires_in,
            updated_at=timestamps["updated_at"],
            access_token_expires_at=timestamps["access_token_expires_at"],
        )

        # Return the updated record
        return await cls.get(id=fitbit_id)
