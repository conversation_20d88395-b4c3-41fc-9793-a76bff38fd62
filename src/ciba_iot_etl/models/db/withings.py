from uuid import uuid4
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict

from tortoise.fields import (
    BooleanField,
    CharField,
    OneToOneField,
    OneToOneRelation,
    UUIDField,
)
from tortoise.exceptions import DoesNotExist

from ciba_iot_etl.models.db.base import FieldExistsMixin, StatusMixin
from ciba_iot_etl.models.db.member import Member
from ciba_iot_etl.models.db.token_base import TokenBase


class Withings(TokenBase, StatusMixin, FieldExistsMixin):
    """Table to store withings access tokens.

    Token Expiration Guidelines:
    - access_token: Expires after 3 hours
    - refresh_token: Expires after 1 year
    - old_refresh_token: Expires 8 hours after new issuance or once the new access_token is used
    """

    id = UUIDField(pk=True, default=uuid4)
    user_id = CharField(max_length=255, unique=True)

    # Override the base class fields with specific constraints
    access_token = CharField(max_length=305, unique=True)
    refresh_token = CharField(max_length=305, unique=True)
    old_refresh_token = CharField(max_length=305, null=True)

    start = BooleanField(default=False)
    healthy = BooleanField(default=True)

    member: OneToOneRelation[Member] = OneToOneField(
        "models.Member", related_name="withings", null=True
    )

    @classmethod
    async def delete_withings(
        cls,
        user_id: str = None,
        member_id: str = None,
    ) -> bool:
        """Delete a Withings record by user_id."""
        try:
            withings_record = (
                await cls.get(user_id=user_id)
                if user_id
                else await cls.get(member__id=member_id)
            )
            await withings_record.delete()
            return True
        except DoesNotExist:
            return False

    @staticmethod
    def _calculate_expiration_timestamps() -> Dict[str, datetime]:
        """Calculate expiration timestamps for tokens.

        This is a helper method for testing and internal use.

        Returns:
            Dict[str, datetime]: A dictionary with the calculated timestamps
        """
        # Get base timestamps
        timestamps = TokenBase._calculate_base_timestamps()

        # Add Withings-specific timestamps
        now = timestamps["updated_at"]
        timestamps["access_token_expires_at"] = now + timedelta(
            hours=3
        )  # Access token expires after 3 hours
        timestamps["refresh_token_expires_at"] = now + timedelta(
            days=365
        )  # Refresh token expires after 1 year
        timestamps["old_refresh_token_expires_at"] = now + timedelta(
            hours=8
        )  # Old refresh token expires after 8 hours

        return timestamps

    @classmethod
    async def update_tokens(
        cls,
        withings_id: str,
        access_token: str,
        refresh_token: str,
        expires_in: int,
    ) -> "Withings":
        """Update tokens with proper expiration timestamps.

        Args:
            withings_id: The ID of the Withings connection to update
            access_token: New access token
            refresh_token: New refresh token
            expires_in: Token expiration time in seconds

        Returns:
            Updated Withings record
        """
        # Get current record to preserve old refresh token
        withings_record = await cls.get(id=withings_id)
        old_refresh_token = withings_record.refresh_token

        # Calculate expiration timestamps
        timestamps = cls._calculate_expiration_timestamps()

        # Update the record
        await cls.filter(id=withings_id).update(
            access_token=access_token,
            refresh_token=refresh_token,
            old_refresh_token=old_refresh_token,
            expires_in=expires_in,
            updated_at=timestamps["updated_at"],
            access_token_expires_at=timestamps["access_token_expires_at"],
            refresh_token_expires_at=timestamps["refresh_token_expires_at"],
            old_refresh_token_expires_at=timestamps["old_refresh_token_expires_at"],
        )

        # Return the updated record
        return await cls.get(id=withings_id)

    # Token expiration methods are inherited from TokenBase

    @staticmethod
    def _calculate_expire_old_refresh_token_timestamps() -> Dict[str, datetime]:
        """Calculate expiration timestamps for expiring old refresh token.

        This is a helper method for testing and internal use.

        Returns:
            Dict[str, datetime]: A dictionary with the calculated timestamps
        """
        # Get base timestamps
        timestamps = TokenBase._calculate_base_timestamps()

        # Add Withings-specific timestamps
        timestamps["old_refresh_token_expires_at"] = timestamps["updated_at"]

        return timestamps

    @classmethod
    async def expire_old_refresh_token(cls, withings_id: str) -> bool:
        """Mark the old refresh token as expired.

        According to Withings API documentation, the old refresh token expires
        either 8 hours after a new refresh token is issued OR immediately once
        the new access token is used. This method handles the latter case.

        Args:
            withings_id: The ID of the Withings connection to update

        Returns:
            bool: True if the update was successful, False otherwise
        """
        try:
            # Calculate expiration timestamps
            timestamps = cls._calculate_expire_old_refresh_token_timestamps()

            # Update the record
            await cls.filter(id=withings_id).update(
                old_refresh_token_expires_at=timestamps["old_refresh_token_expires_at"],
                updated_at=timestamps["updated_at"],
            )
            from loguru import logger

            logger.info(
                f"Expired old refresh token for Withings user {withings_id} after using new access token"
            )
            return True
        except Exception as e:
            # Log the error but don't raise it - this is a non-critical operation
            from loguru import logger

            logger.error(
                f"Error expiring old refresh token for Withings user {withings_id}: {e}"
            )
            return False
