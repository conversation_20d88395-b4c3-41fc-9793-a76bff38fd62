from tortoise import Model
from tortoise.fields import (
    DecimalField,
    ForeignKeyField,
    ForeignKeyRelation,
    IntEnumField,
)

from ciba_iot_etl.models.db.base import MeasurementMixin, TimestampMixin
from ciba_iot_etl.models.db.member import Member
from enum import IntEnum


class ActivityCategory(IntEnum):
    """Enum for activities."""

    WALK = 1
    EXERCISE = 2
    GENERAL = 3


class Activity(Model, TimestampMixin, MeasurementMixin):
    """Table to store members' activity measures"""

    value = DecimalField(max_digits=16, decimal_places=2)
    activity_category = IntEnumField(enum_type=ActivityCategory)
    member: ForeignKeyRelation[Member] = ForeignKeyField(
        "models.Member", related_name="activity"
    )

    class Meta:
        table = "activity"
        indexes = (
            ("created_at",),
            ("member_id", "created_at"),
        )
