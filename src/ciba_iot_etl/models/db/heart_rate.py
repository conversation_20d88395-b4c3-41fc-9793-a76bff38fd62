from tortoise import Model
from tortoise.fields import ForeignKeyField, ForeignKeyRelation, IntField

from ciba_iot_etl.models.db.base import MeasurementMixin, TimestampMixin
from ciba_iot_etl.models.db.member import Member


class HeartRate(Model, TimestampMixin, MeasurementMixin):
    """Table to store members' heart rate measures"""

    value = IntField()
    member: ForeignKeyRelation[Member] = ForeignKeyField(
        "models.Member", related_name="heart_rates"
    )

    class Meta:
        table = "heart_rates"
        indexes = (
            ("created_at",),
            ("member_id", "created_at"),
        )
