from pydantic import BaseModel
from enum import Enum
from ciba_iot_etl.models.pydantic.withings import WithingsPayload
from ciba_iot_etl.models.pydantic.fitbit import FitbitPayload
from ciba_iot_etl.models.pydantic.transtek import TranstekTelemetryMessage
from typing import Optional


class PlatformType(Enum):
    participant = "participant"
    patient = "patient"


class ActivityDevice(str, Enum):
    """Type of device to track activity"""

    WITHINGS = "withings"
    OURARING = "ouraring"
    FITBIT = "fitbit"
    DEXCOM = "dexcom"
    TRANSTEK = "transtek"


class Carrier(str, Enum):
    """Carrier for shipping devices"""

    UPS = "ups"
    USPS = "usps"
    FEDEX = "fedex"
    DHL = "dhl"


TRACKING_URLS = {
    Carrier.UPS: "https://www.ups.com/track?track=yes&trackNums={tracking_number}",
    Carrier.USPS: "https://tools.usps.com/go/TrackConfirmAction?tLabels={tracking_number}",
    Carrier.FEDEX: "https://www.fedex.com/fedextrack/?trknbr={tracking_number}",
    Carrier.DHL: "https://www.dhl.com/us-en/home/<USER>/tracking-express.html?submit=1&tracking-id={tracking_number}",
}


class Platform(BaseModel):
    id: str
    type: PlatformType


class PullDataNotification(BaseModel):
    """
    correlation_id: str
    a unique identifier used to track and correlate related events,
    requests, or transactions across different systems,
     services, or components in a distributed system.
    """

    member_id: str
    activity_device: ActivityDevice
    platforms: list[Platform]
    payload: WithingsPayload | FitbitPayload | TranstekTelemetryMessage
    corellation_id: Optional[str] = "-"


class DataResp(BaseModel):
    platform: str = ""
    statusCode: int = 500
    body: dict = {}
    error: dict = {"message": "Internal Server Error"}
    correlation_id: str = ""


class SlackNotification(BaseModel):
    environment: str
    is_test: bool
    source: str
    title: Optional[str] = ""
    url: Optional[str] = ""
    type: Optional[str] = ""
    details: Optional[str] = ""
    additional_info: Optional[str] = ""
