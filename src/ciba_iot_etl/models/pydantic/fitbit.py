from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime
import pendulum
from enum import Enum
from ciba_iot_etl.models.db.activity import ActivityCategory


class FitbitPayload(BaseModel):
    fitbit_id: str
    scope: list[str]
    start_date: int
    end_date: int


class RefreshTokenResp(BaseModel):
    error: Optional[str] = None
    success: Optional[str] = None
    access_token: Optional[str] = None
    expires_in: Optional[int] = None
    refresh_token: Optional[str] = None
    token_type: Optional[str] = None
    user_id: Optional[str] = None


class SubscriptionCollectionPath(Enum):
    SLEEP = "sleep"
    ACTIVITIES = "activities"
    BODY = "body"
    FOOD = "foods"
    USER_REVOKED_ACCESS = "userRevokedAccess"


class Subscription(BaseModel):
    collection_type: str = Field(alias="collectionType")
    owner_id: str = Field(alias="ownerId")
    owner_type: str = Field(alias="ownerType")
    susbcriber_id: str = Field(alias="subscriberId")
    subscription_id: str = Field(alias="subscriptionId")


class SubscriptionResponse(BaseModel):
    subscriptions: list[Optional[Subscription]] = Field(alias="apiSubscriptions")


class SubscriptionNotification(BaseModel):
    collection_type: str = Field(alias="collectionType")
    date: datetime
    owner_id: str = Field(alias="ownerId")
    owner_type: str = Field(alias="ownerType")
    subscription_id: str = Field(alias="subscriptionId")

    def member_id(self):
        return self.subscription_id.split("_")[0]


class FitbitHeartRateZone(BaseModel):
    minute_multiplier: int = Field(alias="minuteMultiplier")
    minutes: int
    order: int
    type: str
    zone_name: str = Field(alias="zoneName")


class FitbitActiveZoneMinutes(BaseModel):
    total_minutes: int = Field(alias="totalMinutes")
    heart_rate_zones: list[FitbitHeartRateZone] = Field(alias="minutesInHeartRateZones")


class FitbitActivity(BaseModel):
    steps: Optional[int] = 0
    distance: Optional[float] = 0.0
    distance_unit: Optional[str] = None
    duration: Optional[int] = Field(alias="minutes", default=0)
    original_duration: Optional[int] = Field(alias="originalDuration", default=0)
    has_active_zone_minutes: Optional[bool] = Field(
        alias="hasActiveZoneMinutes", default=False
    )
    active_zone_minutes: Optional[FitbitActiveZoneMinutes] = Field(
        alias="activeZoneMinutes", default=None
    )
    calories: Optional[int] = 0
    start_time: datetime = Field(alias="dateTime")
    category: Optional[ActivityCategory] = ActivityCategory.GENERAL

    def get_duration_in_minutes(self) -> float:
        """
        Converts the duration (in milliseconds) to minutes.
        """
        return self.duration / 60000  # 1 minute = 60,000 milliseconds

    def get_original_duration_in_minutes(self) -> float:
        """
        Converts the original duration (in milliseconds) to minutes.
        """
        return self.original_duration / 60000  # 1 minute = 60,000 milliseconds

    def utc_start_time(self) -> str:
        """
        Returns the start time in UTC.
        """
        return pendulum.instance(self.start_time).in_tz("UTC")


class SleepLevel(BaseModel):
    created_at: datetime = Field(alias="dateTime")
    level: str
    seconds: int


class SleepSummaryValue(BaseModel):
    count: int
    minutes: int


class SleepSummary(BaseModel):
    deep: Optional[SleepSummaryValue] = None
    light: Optional[SleepSummaryValue] = None
    rem: Optional[SleepSummaryValue] = None
    wake: Optional[SleepSummaryValue] = None
    restless: Optional[SleepSummaryValue] = None
    asleep: Optional[SleepSummaryValue] = None
    awake: Optional[SleepSummaryValue] = None


class SleepLevels(BaseModel):
    summary: SleepSummary
    data: list[SleepLevel]


class FitbitSleep(BaseModel):
    date_of_sleep: str = Field(alias="dateOfSleep")
    duration: int
    efficiency: int
    end_time: str = Field(alias="endTime")
    info_code: int = Field(alias="infoCode")
    is_main_sleep: bool = Field(alias="isMainSleep")
    levels: SleepLevels
    log_id: int = Field(alias="logId")
    log_type: str = Field(alias="logType")
    minutes_after_wakeup: int = Field(alias="minutesAfterWakeup")
    minutes_asleep: int = Field(alias="minutesAsleep")
    minutes_awake: int = Field(alias="minutesAwake")
    minutes_to_fall_asleep: int = Field(alias="minutesToFallAsleep")
    start_time: str = Field(alias="startTime")
    time_in_bed: int = Field(alias="timeInBed")
    type: str

    def get_duration_in_minutes(self) -> float:
        """
        Converts the duration (in milliseconds) to minutes.
        """
        return self.duration / 60000  # 1 minute = 60,000 milliseconds

    def get_time_in_bed_in_minutes(self) -> float:
        """
        Converts the time in bed (in milliseconds) to minutes.
        """
        return self.time_in_bed / 60000  # 1 minute = 60,000 milliseconds

    def get_minutes_asleep_in_minutes(self) -> float:
        """
        Converts the minutes asleep (in milliseconds) to minutes.
        """
        return self.minutes_asleep / 60000


class HeartRateZone(BaseModel):
    max: int
    min: int
    name: str
    minutes: Optional[int] = None
    calories_out: Optional[float] = Field(alias="caloriesOut", default=None)


class HeartRateValue(BaseModel):
    custom_heartrate_zones: list = Field(alias="customHeartRateZones")
    heart_rate_zones: list[HeartRateZone] = Field(alias="heartRateZones")
    resting_heart_rate: Optional[int] = Field(alias="restingHeartRate", default=None)


class FitbitHeartRate(BaseModel):
    date_time: str = Field(alias="dateTime")
    value: int
