from datetime import datetime
from typing import Optional

from pydantic import BaseModel

from ciba_iot_etl.helpers.measurement import DeviceType


class MemberDeviceData(BaseModel):
    linked_at: Optional[datetime] = None
    last_synced_at: datetime
    disconnected_at: Optional[datetime] = None
    timezone: str = "UTC"
    external_id: str
    device_type: str
    model: Optional[str] = None
    vendor: DeviceType
