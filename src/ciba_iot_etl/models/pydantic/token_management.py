"""Standardized token management models and error handling."""

from enum import Enum
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field


class TokenErrorType(str, Enum):
    """Standardized token error types across all platforms."""

    INVALID_GRANT = "invalid_grant"
    EXPIRED_TOKEN = "expired_token"
    NETWORK_ERROR = "network_error"
    API_ERROR = "api_error"
    RATE_LIMIT = "rate_limit"
    VALIDATION_ERROR = "validation_error"


class TokenRefreshResult(BaseModel):
    """Standardized result for token refresh operations."""

    success: bool = Field(description="Whether the token refresh was successful")
    error_type: Optional[TokenErrorType] = Field(
        default=None, description="Type of error if refresh failed"
    )
    error_message: Optional[str] = Field(
        default=None, description="Detailed error message"
    )
    requires_reauth: bool = Field(
        default=False, description="Whether user re-authentication is required"
    )
    used_fallback: bool = Field(
        default=False, description="Whether old_refresh_token fallback was used"
    )
    token_data: Optional[Dict[str, Any]] = Field(
        default=None, description="Token data if refresh was successful"
    )
    platform: str = Field(description="Platform name (fitbit, withings)")

    class Config:
        """Pydantic configuration."""

        use_enum_values = True


class TokenValidationResult(BaseModel):
    """Result of token validation checks."""

    is_valid: bool = Field(description="Whether the token is valid")
    is_expired: bool = Field(description="Whether the token is expired")
    expires_at: Optional[str] = Field(
        default=None, description="Token expiration timestamp"
    )
    needs_refresh: bool = Field(
        default=False, description="Whether token needs to be refreshed"
    )


class FallbackTokenInfo(BaseModel):
    """Information about fallback token availability."""

    has_old_token: bool = Field(description="Whether old_refresh_token exists")
    old_token_expired: bool = Field(description="Whether old_refresh_token is expired")
    can_use_fallback: bool = Field(description="Whether fallback can be attempted")
    old_token_expires_at: Optional[str] = Field(
        default=None, description="Old token expiration timestamp"
    )
