"""Utility functions for datetime handling."""

from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

import pytz


def get_timezone_naive_now() -> datetime:
    """Get the current datetime as timezone-naive.

    Returns:
        datetime: Current datetime with tzinfo=None
    """
    return datetime.now().replace(tzinfo=None)


def ensure_timezone_naive(dt: Optional[datetime]) -> Optional[datetime]:
    """Ensure a datetime is timezone-naive.

    Args:
        dt: The datetime to check

    Returns:
        datetime: The datetime with tzinfo=None, or None if dt is None
    """
    if dt is None:
        return None

    if dt.tzinfo is not None:
        # Convert to UTC first, then remove timezone info
        # This ensures correct comparison with naive datetime
        if hasattr(dt.tzinfo, "localize"):
            # For pytz timezones
            utc_dt = dt.astimezone(pytz.UTC)
            return utc_dt.replace(tzinfo=None)
        else:
            # For standard library timezones
            return dt.replace(tzinfo=None)

    return dt


def is_datetime_expired(
    expires_at: Optional[datetime],
    fallback_start: Optional[datetime] = None,
    fallback_seconds: Optional[int] = None,
) -> bool:
    """Check if a datetime is expired.

    Args:
        expires_at: The expiration datetime to check
        fallback_start: If expires_at is None, use this as the start time for fallback
        fallback_seconds: If expires_at is None, use this as the duration in seconds for fallback

    Returns:
        bool: True if expired, False otherwise
    """
    now = get_timezone_naive_now()

    # If no expiration timestamp, fall back to expires_in if provided
    if expires_at is None:
        if fallback_start is not None and fallback_seconds is not None:
            # Make sure fallback_start is timezone-naive for comparison
            fallback_start = ensure_timezone_naive(fallback_start)
            return fallback_start + timedelta(seconds=fallback_seconds) <= now
        # If no fallback, assume not expired
        return False

    # Make sure expires_at is timezone-naive for comparison
    expires_at = ensure_timezone_naive(expires_at)
    return expires_at <= now
