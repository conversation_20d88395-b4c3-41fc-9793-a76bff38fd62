from typing import Any, Dict, List

from ciba_iot_etl.models.db.weight import Weight
from ciba_iot_etl.models.db.blood_pressure import BloodPressure
from ciba_iot_etl.models.db.heart_rate import HeartRate
from ciba_iot_etl.helpers.measurement import (
    DeviceType,
    UnitOfMeasurement as CibaUnitOfMeasurement,
)
from ciba_iot_etl.models.pydantic.transtek import (
    TranstekTelemetryMessage,
    ScaleTelemetry,
    BPMTelemetry,
)
from ciba_iot_etl.helpers.unit_transformation import parse_kg_to_lb

import pendulum


class TranstekToTortoiseConverter:
    """
    A reusable converter to transform Transtek data into Tortoise ORM models.
    """

    def __init__(self, member_id: int):
        self.member_id = member_id  # Associate all data with a specific member

    async def convert(self, telemetry: TranstekTelemetryMessage) -> Dict[str, Any]:
        """
        Transforms Transtek data into Tortoise ORM models.
        """
        weight_measurements: List[Weight] = []
        blood_pressure_measurements: List[BloodPressure] = []
        heart_rate_measurements: List[HeartRate] = []

        if isinstance(telemetry.data, ScaleTelemetry):
            weight_telemetry: ScaleTelemetry = telemetry.data
            weight = Weight(
                value=parse_kg_to_lb(weight_telemetry.wt / 1000, number_after_comma=2),
                unit=CibaUnitOfMeasurement.LB,
                device=DeviceType.TRANSTEK,
                created_at=pendulum.from_timestamp(weight_telemetry.ts),
                updated_at=pendulum.from_timestamp(weight_telemetry.ts),
                member_id=self.member_id,
            )
            await weight.save()

            weight_measurements.append(weight)
        elif isinstance(telemetry.data, BPMTelemetry):
            blood_telemetry: BPMTelemetry = telemetry.data
            blood_pressure = BloodPressure(
                systolic_value=blood_telemetry.sys,
                diastolic_value=blood_telemetry.dia,
                unit=CibaUnitOfMeasurement.MM_HG,
                device=DeviceType.TRANSTEK,
                created_at=pendulum.from_timestamp(blood_telemetry.ts),
                updated_at=pendulum.from_timestamp(blood_telemetry.ts),
                member_id=self.member_id,
            )
            await blood_pressure.save()

            heart_rate = HeartRate(
                value=telemetry.data.pul,
                unit=CibaUnitOfMeasurement.BPM,
                device=DeviceType.TRANSTEK,
                created_at=pendulum.from_timestamp(blood_telemetry.ts),
                updated_at=pendulum.from_timestamp(blood_telemetry.ts),
                member_id=self.member_id,
            )
            await heart_rate.save()

            blood_pressure_measurements.append(blood_pressure)
            heart_rate_measurements.append(heart_rate)
        else:
            raise ValueError("No valid data to be converted")

        return weight_measurements, blood_pressure_measurements, heart_rate_measurements
