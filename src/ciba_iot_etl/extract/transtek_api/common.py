HOST = "https://api.connect.mio-labs.com"
DEVICE_SERVICE = "/v1/devices"
NETWORK_ERROR = "Network error when trying to connect to MioConnect Platform"
MIO_ERROR = "Error on MioConnect external service"
DEVICE_NOT_FOUND_ERROR = "Device not found. Please double-check IMEI code"
ACTIVATE_ERROR = "Device could not be activated/deactivated. Please contact support"
TELEMETRY_DATES_ERROR_1 = "Request Error: Start date is greater than end date"
TELEMETRY_DATES_ERROR_2 = (
    "Request Error: Start date and/or end date are not integer unix timestamps"
)
UNKNOWN_ERROR = "Unknown error. Please contact Support"


class MioConnectError(Exception): ...
