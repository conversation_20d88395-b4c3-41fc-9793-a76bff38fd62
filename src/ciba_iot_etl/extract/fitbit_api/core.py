from ciba_iot_etl.common.api import BaseLoader
from loguru import logger
from ciba_iot_etl.helpers.crypto import (
    base64_url_encode,
    sha256_hash,
    generate_code_verifier,
)
from uuid import uuid4
from ciba_iot_etl.models.pydantic.fitbit import (
    RefreshTokenResp,
    Subscription,
    SubscriptionCollectionPath,
    SubscriptionResponse,
)
from datetime import date, datetime
import pendulum
import asyncio
from typing import Dict, Any, Optional
from enum import Enum
from http import HTTPStatus


class FitbitServiceException(Exception):
    """Base exception class for Fitbit service errors."""


class FitbitErrorCode:
    """Fitbit API error codes"""

    INVALID_TOKEN = "invalid_token"
    EXPIRED_TOKEN = "expired_token"
    INVALID_CLIENT = "invalid_client"
    INVALID_GRANT = "invalid_grant"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"


class IntradayGranularity(Enum):
    SECOND = "1sec"
    MINUTE = "1min"
    FIVE_MINUTES = "5min"
    FIFTEEN_MINUTES = "15min"


class FitbitLoader(BaseLoader):
    limit_period: int = 60
    limit_calls: int = 100  # 100 calls per minute
    OAUTH_URL = "https://www.fitbit.com/oauth2/authorize"
    API_URL = "https://api.fitbit.com"
    TIMEOUT = 4.5
    SCOPE = (
        "activity heartrate location nutrition profile settings "
        "sleep social weight cardio_fitness electrocardiogram "
        "oxygen_saturation respiratory_rate temperature"
    )

    # Rate limiting parameters
    MAX_RETRIES: int = 5
    INITIAL_BACKOFF: float = 1.0

    def __init__(self, client_id: str, client_secret: str, redirect_uri: str):
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri
        super(FitbitLoader, self).__init__(self.limit_calls, self.limit_period)

    async def _generate_code_challenge(self) -> dict:
        """Generate a code challenge from the code verifier."""
        code_verifier = generate_code_verifier(43, 128)
        hashed = sha256_hash(code_verifier)
        code_challenge = base64_url_encode(hashed)
        return {"code_verifier": code_verifier, "code_challenge": code_challenge}

    async def _generate_headers(self) -> str:
        """Generate headers for the request."""
        credentials = f"{self.client_id}:{self.client_secret}"
        return base64_url_encode(credentials.encode("utf-8"))

    async def _handle_rate_limit(
        self, response: Dict[str, Any], retry_count: int
    ) -> Optional[Dict[str, Any]]:
        """
        Handle Fitbit API rate limit response.

        Args:
            response: The API response to check
            retry_count: Current retry attempt number

        Returns:
            None if rate limit was detected and we should retry, or the original response if no rate limit or max retries reached
        """
        # Check if response contains rate limit error
        if isinstance(response, dict):
            # Check for error in response
            error_type = (
                response.get("errors", [{}])[0].get("errorType")
                if response.get("errors")
                else None
            )
            error_type = error_type or response.get("error")

            if (
                error_type == FitbitErrorCode.RATE_LIMIT_EXCEEDED
                or response.get("status_code") == HTTPStatus.TOO_MANY_REQUESTS
            ):
                if retry_count < self.MAX_RETRIES:
                    # Calculate backoff time with exponential backoff
                    backoff_time = self.INITIAL_BACKOFF * (2**retry_count)
                    logger.warning(
                        f"Fitbit API rate limit exceeded. Retrying in {backoff_time} seconds (attempt {retry_count + 1}/{self.MAX_RETRIES})"
                    )
                    await asyncio.sleep(backoff_time)
                    return None  # Signal to retry the request
                else:
                    logger.error("Fitbit API rate limit exceeded. Max retries reached.")
                    return response  # Return the rate limit response after max retries

        # No rate limit detected, return the original response
        return response

    async def _call_api_with_rate_limit_handling(
        self,
        url: str,
        method: str = "GET",
        headers: Optional[Dict[str, str]] = None,
        payload: Optional[Dict[str, Any]] = None,
        querystring: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Call API with Fitbit rate limit handling.

        This method wraps the BaseLoader.call_api method with specific handling for Fitbit rate limit responses.
        """
        retry_count = 0

        while retry_count <= self.MAX_RETRIES:
            response = await self.call_api(
                url=url,
                method=method,
                headers=headers,
                payload=payload,
                querystring=querystring,
            )

            # Handle rate limit response
            result = await self._handle_rate_limit(response, retry_count)
            if result is not None:
                return result  # Either no rate limit or max retries reached

            # If we get here, we need to retry
            retry_count += 1

        # This should not be reached, but just in case
        return {
            "error": FitbitErrorCode.RATE_LIMIT_EXCEEDED,
            "status_code": HTTPStatus.TOO_MANY_REQUESTS,
        }

    async def _generate_unique_state(self) -> str:
        """Generate a unique 16-digit number from UUID."""
        return str(uuid4().int)[:16]

    async def get_auth_page_url(self, state: str = None):
        unique_state = await self._generate_unique_state() if not state else state

        result = {
            "auth_url": "",
            "error": "",
            "code_challenge": "",
            "code_verifier": "",
            "state": unique_state,
        }
        challenge = await self._generate_code_challenge()
        result.update(challenge)

        logger.info("Generating Fitbit authorization URL.")

        params = {
            "response_type": "code",
            "client_id": self.client_id,
            "redirect_uri": self.redirect_uri.split("?")[0],
            "code_challenge_method": "S256",
            "code_challenge": result["code_challenge"],
            "scope": self.SCOPE,
            "state": result["state"],
        }
        headers = await self._generate_headers()
        response = await self._call_api_with_rate_limit_handling(
            url=self.OAUTH_URL,
            method="GET",
            querystring=params,
            headers={
                "Authorization": f"Basic {headers}",
                "Content-Type": "application/x-www-form-urlencoded",
            },
        )

        result.update(response)

        return result

    async def get_token(
        self, code: str, state: str, site: str, code_verifier: str
    ) -> Dict[str, Any]:
        """
        Get token using authorization code.

        This method handles rate limiting with exponential backoff retry logic.
        """
        logger.info("Getting Fitbit token")
        logger.debug(f"Code: {code}, State: {state}, Site: {site}")

        payload = {
            "client_id": self.client_id,
            "grant_type": "authorization_code",
            "code": code,
            "code_verifier": code_verifier,
            "redirect_uri": self.redirect_uri.split("?")[0],
        }
        headers = await self._generate_headers()
        response = await self._call_api_with_rate_limit_handling(
            method="POST",
            url=f"{self.API_URL}/oauth2/token",
            payload=payload,
            headers={"Authorization": f"Basic {headers}"},
        )

        return response

    async def get_user(self, access_token: str) -> Dict[str, Any]:
        """
        Get user profile data.

        This method handles rate limiting with exponential backoff retry logic.
        """
        logger.info("Getting Fitbit user data.")
        response = await self._call_api_with_rate_limit_handling(
            method="GET",
            url=f"{self.API_URL}/1/user/-/profile.json",
            headers={"Authorization": f"Bearer {access_token}"},
        )

        return response

    async def get_all_activities(
        self, access_token: str, after_date: date = None, next_url: str = None
    ) -> Dict[str, Any]:
        """
        Get all user activities.

        This method handles rate limiting with exponential backoff retry logic.
        """
        logger.info("Getting Fitbit user activities.")

        url = (
            f"{self.API_URL}/1/user/-/activities/list.json?afterDate={after_date.isoformat()}&offset=0&limit=100"
            if not next_url
            else next_url
        )

        response = await self._call_api_with_rate_limit_handling(
            method="GET",
            url=url,
            headers={"Authorization": f"Bearer {access_token}"},
        )

        return response

    async def get_steps(
        self, access_token: str, start_date: date, end_date: date
    ) -> Dict[str, Any]:
        """
        Get user steps data.

        This method handles rate limiting with exponential backoff retry logic.
        """
        logger.info("Getting fitbit steps")
        url = f"{self.API_URL}/1/user/-/activities/steps/date/{start_date.isoformat()}/{end_date.isoformat()}.json"

        response = await self._call_api_with_rate_limit_handling(
            method="GET", url=url, headers={"Authorization": f"Bearer {access_token}"}
        )
        return response

    async def get_distance(
        self, access_token: str, start_date: date, end_date: date
    ) -> Dict[str, Any]:
        """
        Get user distance data.

        This method handles rate limiting with exponential backoff retry logic.
        """
        logger.info("Getting fitbit distance")
        url = f"{self.API_URL}/1/user/-/activities/distance/date/{start_date.isoformat()}/{end_date.isoformat()}.json"

        response = await self._call_api_with_rate_limit_handling(
            method="GET", url=url, headers={"Authorization": f"Bearer {access_token}"}
        )
        return response

    async def get_active_minutes_lightly(
        self, access_token: str, start_date: date, end_date: date
    ) -> Dict[str, Any]:
        """
        Get user lightly active minutes data.

        This method handles rate limiting with exponential backoff retry logic.
        """
        logger.info("Getting fitbit minutesLightlyActive")
        url = f"{self.API_URL}/1/user/-/activities/minutesLightlyActive/date/{start_date.isoformat()}/{end_date.isoformat()}.json"

        response = await self._call_api_with_rate_limit_handling(
            method="GET", url=url, headers={"Authorization": f"Bearer {access_token}"}
        )
        return response

    async def get_active_minutes_fairly(
        self, access_token: str, start_date: date, end_date: date
    ) -> Dict[str, Any]:
        """
        Get user fairly active minutes data.

        This method handles rate limiting with exponential backoff retry logic.
        """
        logger.info("Getting fitbit minutesFairlyActive")
        url = f"{self.API_URL}/1/user/-/activities/minutesFairlyActive/date/{start_date.isoformat()}/{end_date.isoformat()}.json"

        response = await self._call_api_with_rate_limit_handling(
            method="GET", url=url, headers={"Authorization": f"Bearer {access_token}"}
        )
        return response

    async def get_active_minutes_very(
        self, access_token: str, start_date: date, end_date: date
    ) -> Dict[str, Any]:
        """
        Get user very active minutes data.

        This method handles rate limiting with exponential backoff retry logic.
        """
        logger.info("Getting fitbit minutesVeryActive")
        url = f"{self.API_URL}/1/user/-/activities/minutesVeryActive/date/{start_date.isoformat()}/{end_date.isoformat()}.json"

        response = await self._call_api_with_rate_limit_handling(
            method="GET", url=url, headers={"Authorization": f"Bearer {access_token}"}
        )
        return response

    async def get_sleep_data(
        self, access_token: str, start_date: date, end_date: date
    ) -> Dict[str, Any]:
        """
        Get user sleep data.

        This method handles rate limiting with exponential backoff retry logic.
        """
        logger.info("Getting Fitbit sleep data.")

        url = f"{self.API_URL}/1.2/user/-/sleep/date/{start_date.isoformat()}/{end_date.isoformat()}.json"

        response = await self._call_api_with_rate_limit_handling(
            method="GET",
            url=url,
            headers={"Authorization": f"Bearer {access_token}"},
        )

        return response

    async def get_heart_rate_day(
        self,
        access_token: str,
        date: date,
        granularity: IntradayGranularity = IntradayGranularity.MINUTE,
    ) -> Dict[str, Any]:
        """
        Get heart rate data for a day

        Args:
            access_token (str): user access token
            date (date): date to get heart rate data for
            granularity (IntradayGranularity, optional): granularity of the data. Defaults to IntradayGranularity.MINUTE.

        Returns:
            Dict[str, Any]: Heart rate data response

        This method handles rate limiting with exponential backoff retry logic.
        """

        logger.info(f"Getting Fitbit heart rate data for date: {date.isoformat()}")

        url = f"{self.API_URL}/1/user/-/activities/heart/date/{date.isoformat()}/1d/{granularity.value}.json"

        response = await self._call_api_with_rate_limit_handling(
            method="GET",
            url=url,
            headers={"Authorization": f"Bearer {access_token}"},
        )

        return response

    async def get_heart_rate_range(
        self,
        access_token: str,
        start_date: datetime,
        end_date: datetime,
        granularity: IntradayGranularity = IntradayGranularity.MINUTE,
    ) -> Dict[str, Any]:
        """
        Get heart rate data for a range of dates

        Args:
            access_token (str): user access token
            start_date (datetime): start date
            end_date (datetime): end date
            granularity (IntradayGranularity, optional): granularity of the data. Defaults to IntradayGranularity.MINUTE.

        Returns:
            Dict[str, Any]: Heart rate data response

        This method handles rate limiting with exponential backoff retry logic.
        """
        logger.info(
            f"Getting Fitbit heart rate data for date: {start_date.isoformat()} to {end_date.isoformat()}"
        )

        start_day = start_date.date()
        start_time = start_date.time()

        end_day = end_date.date()
        end_time = end_date.time()

        url = f"{self.API_URL}/1/user/-/activities/heart/date/{start_day.isoformat()}/{end_day.isoformat()}/{granularity.value}/time/{start_time.isoformat()}/{end_time.isoformat()}.json"

        response = await self._call_api_with_rate_limit_handling(
            method="GET",
            url=url,
            headers={"Authorization": f"Bearer {access_token}"},
        )

        return response

    async def get_body_weight(
        self, access_token: str, date: date, next_url: str = None
    ) -> Dict[str, Any]:
        """
        Get user body weight data.

        This method handles rate limiting with exponential backoff retry logic.
        """
        logger.info("Getting Fitbit body weight data.")

        url = (
            f"{self.API_URL}/1/user/-/body/weight/date/{date.isoformat()}/{pendulum.now().to_date_string()}.json"
            if not next_url
            else next_url
        )

        response = await self._call_api_with_rate_limit_handling(
            method="GET",
            url=url,
            headers={"Authorization": f"Bearer {access_token}"},
        )

        return response

    async def get_sp02(
        self, access_token: str, date: date, next_url: str = None
    ) -> Dict[str, Any]:
        """
        Get user SpO2 data.

        Does not support subscription. Have to pull manually.

        This method handles rate limiting with exponential backoff retry logic.

        Args:
            access_token: User access token
            date: Date to get data for
            next_url: URL for pagination

        Returns:
            Dict[str, Any]: SpO2 data response
        """
        logger.info("Getting Fitbit sp02 data.")

        url = (
            f"{self.API_URL}/1/user/-/spo2/date/{date.isoformat()}/{pendulum.now().to_date_string()}.json"
            if not next_url
            else next_url
        )

        response = await self._call_api_with_rate_limit_handling(
            method="GET",
            url=url,
            headers={"Authorization": f"Bearer {access_token}"},
        )

        return response

    async def get_activity_descriptions(self, access_token: str) -> Dict[str, Any]:
        """
        Get activity descriptions.

        This method handles rate limiting with exponential backoff retry logic.
        """
        logger.info("Getting Fitbit activity descriptions.")

        response = await self._call_api_with_rate_limit_handling(
            method="GET",
            url=f"{self.API_URL}/1/activities.json",
            headers={"Authorization": f"Bearer {access_token}"},
        )

        return response

    async def create_subscription(
        self,
        access_token: str,
        collection_path: SubscriptionCollectionPath = None,
        user_id: str = "-",
        member_id: str = None,
    ) -> Subscription:
        """
        https://dev.fitbit.com/build/reference/web-api/subscription/create-subscription/


        :param access_token: user token
        :param user_id: id of the user or "-" for current user
        :param collection_path: path to the collection
        :param member_id: id of the rpm member
        :return:
        """

        if collection_path:
            url = f"{self.API_URL}/1/user/{user_id}/{collection_path}/apiSubscriptions/{member_id}_{collection_path.value}_{pendulum.now().int_timestamp}.json"
        else:
            url = f"{self.API_URL}/1/user/{user_id}/apiSubscriptions/{member_id}_{pendulum.now().int_timestamp}.json"

        logger.info("Creating Fitbit subscription.")

        response = await self._call_api_with_rate_limit_handling(
            method="POST",
            url=url,
            headers={"Authorization": f"Bearer {access_token}"},
        )

        return Subscription.model_validate(response)

    async def delete_subscription(
        self,
        access_token: str,
        collection_path: SubscriptionCollectionPath = None,
        user_id: str = "-",
    ):
        """
        https://dev.fitbit.com/build/reference/web-api/subscription/delete-subscription/


        :param access_token: user token
        :param user_id: id of the user or "-" for current user
        :param collection_path: path to the collection
        :param member_id: id of the rpm member
        :return:
        """

        subscriptions_resp = await self.get_subscriptions(
            access_token=access_token, collection_path=collection_path, user_id=user_id
        )

        if not subscriptions_resp.subscriptions:
            return True

        try:
            for subscription in subscriptions_resp.subscriptions:
                if collection_path:
                    url = f"{self.API_URL}/1/user/{user_id}/{collection_path}/apiSubscriptions/{subscription.subscription_id}.json"
                else:
                    url = f"{self.API_URL}/1/user/{user_id}/apiSubscriptions/{subscription.subscription_id}.json"

                logger.info("Deleting Fitbit subscription.")

                response = await self._call_api_with_rate_limit_handling(
                    method="DELETE",
                    url=url,
                    headers={"Authorization": f"Bearer {access_token}"},
                )
                logger.debug(response)
        except Exception as e:
            logger.error(f"Error deleting subscription: {e}")
            return False
        logger.info("All subscriptions deleted.")
        return True

    async def get_subscriptions(
        self,
        access_token: str,
        collection_path: SubscriptionCollectionPath = None,
        user_id: str = "-",
    ) -> SubscriptionResponse:
        """
        https://dev.fitbit.com/build/reference/web-api/subscription/get-subscriptions/


        :param access_token: user token
        :param user_id: id of the user or "-" for current user
        :param collection_path: path to the collection
        :return:
        """

        if collection_path:
            url = f"{self.API_URL}/1/user/{user_id}/{collection_path}/apiSubscriptions.json"
        else:
            url = f"{self.API_URL}/1/user/{user_id}/apiSubscriptions.json"

        logger.info("Getting Fitbit subscriptions.")

        response = await self._call_api_with_rate_limit_handling(
            method="GET",
            url=url,
            headers={"Authorization": f"Bearer {access_token}"},
        )

        return SubscriptionResponse.model_validate(response)

    async def refresh_token(self, refresh_token: str) -> RefreshTokenResp:
        """
        Refresh the access token using a refresh token.

        This method handles rate limiting with exponential backoff retry logic.

        Args:
            refresh_token: The refresh token to use

        Returns:
            RefreshTokenResp: The response containing the new access token and refresh token
        """
        headers = await self._generate_headers()

        resp = await self._call_api_with_rate_limit_handling(
            method="POST",
            url=f"{self.API_URL}/oauth2/token",
            payload={
                "grant_type": "refresh_token",
                "refresh_token": refresh_token,
                "expires_in": 28800,
            },
            headers={"Authorization": f"Basic {headers}"},
        )

        return RefreshTokenResp.validate(resp)
