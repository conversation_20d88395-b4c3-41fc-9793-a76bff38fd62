import json
import traceback
from collections import defaultdict
from http import HTTPStatus
from typing import Dict, Any, List, Optional, Union

import pendulum
from loguru import logger

from ciba_iot_etl.extract.fitbit_api.core import FitbitLoader, FitbitErrorCode
from ciba_iot_etl.models.pydantic.common import PullDataNotification, FitbitPayload
from ciba_iot_etl.models.db.fitbit import Fitbit
from ciba_iot_etl.models.db.activity import Activity, ActivityCategory
from ciba_iot_etl.helpers.measurement import DeviceType
from ciba_iot_etl.helpers.measurement import UnitOfMeasurement
from ciba_iot_etl.models.db.sleep import Sleep
from ciba_iot_etl.models.db.heart_rate import HeartRate
from ciba_iot_etl.models.pydantic.fitbit import (
    FitbitActivity,
    FitbitSleep,
    FitbitHeartRate,
)


def log_metric(
    metric_name: str,
    value: Any = 1,
    tags: Dict[str, Any] = None,
    extra: Dict[str, Any] = None,
) -> None:
    """
    Log a metric in a format that can be easily parsed by Datadog.

    Args:
        metric_name: The name of the metric to log
        value: The value of the metric (default: 1 for counter-type metrics)
        tags: Dictionary of tags to associate with the metric
        extra: Additional information to include in the log
    """
    log_data = {
        "metric_name": metric_name,
        "metric_value": value,
        "metric_type": "count",
        "tags": tags or {},
    }

    if extra:
        log_data.update(extra)

    # Convert to JSON string and log with a consistent prefix for easy parsing
    logger.info(f"DATADOG_METRIC: {json.dumps(log_data)}")


async def update_fitbit_health_status(
    fitbit_id: Union[str, int], is_healthy: bool, reason: str = None
) -> bool:
    """
    Update the healthy status of a Fitbit connection.

    Args:
        fitbit_id: The ID of the Fitbit connection to update
        is_healthy: Whether the connection is healthy (True) or unhealthy (False)
        reason: Optional reason for the status change, for logging purposes

    Returns:
        bool: True if the update was successful, False otherwise
    """
    try:
        # Get current status to avoid unnecessary updates
        fitbit_connection = await Fitbit.filter(id=fitbit_id).first()
        if not fitbit_connection:
            error_msg = (
                f"Cannot update health status: Fitbit connection {fitbit_id} not found"
            )
            logger.error(error_msg)

            # Log metric for missing connection
            log_metric(
                "fitbit.connection.missing",
                tags={
                    "fitbit_id": str(fitbit_id),
                },
                extra={"error": error_msg},
            )
            return False

        # Only update if the status is changing
        if fitbit_connection.healthy != is_healthy:
            status_text = "healthy" if is_healthy else "unhealthy"
            reason_text = f" - Reason: {reason}" if reason else ""
            logger.info(
                f"Marking Fitbit connection {fitbit_id} as {status_text}{reason_text}"
            )

            # Log metric for device health status change
            log_metric(
                "fitbit.device.health_status",
                value=1 if is_healthy else 0,  # 1 for healthy, 0 for unhealthy
                tags={
                    "fitbit_id": str(fitbit_id),
                    "status": status_text,
                    "reason_category": reason.split(":")[0]
                    if reason and ":" in reason
                    else "general"
                    if reason
                    else "unknown",
                },
                extra={"reason": reason, "user_id": fitbit_connection.user_id},
            )

            await Fitbit.filter(id=fitbit_id).update(healthy=is_healthy)
            return True
        return True  # No update needed, but operation was successful
    except Exception as e:
        error_msg = f"Error updating Fitbit health status for {fitbit_id}: {e}"
        logger.error(error_msg)

        # Log metric for exceptions
        log_metric(
            "fitbit.error",
            tags={
                "fitbit_id": str(fitbit_id),
                "error_type": type(e).__name__,
                "operation": "update_health_status",
            },
            extra={"error_message": str(e), "traceback": traceback.format_exc()},
        )
        return False


async def process_fitbit_message(
    fitbit_service: FitbitLoader, message: PullDataNotification
):
    """
    Process a message for a Fitbit user.

    This function handles rate limiting with exponential backoff retry logic.

    Args:
        fitbit_service: FitbitLoader instance
        message: Message containing the payload for data fetching

    Returns:
        List: A list containing the processed data, or an empty list if processing fails
    """
    fitbit_id = message.payload.fitbit_id
    logger.info(f"Processing messages for Fitbit user: {fitbit_id}")

    try:
        result = await process_user_message(
            fitbit_service=fitbit_service,
            fitbit_id=fitbit_id,
            message=message,
        )

        # If no result, return empty list
        if not result:
            logger.warning(
                f"No data returned for Fitbit user: {fitbit_id}. This could be due to rate limiting or other errors."
            )
            return []

        # Add additional fields to the result
        result["fitbit_id"] = fitbit_id
        result["platforms"] = message.platforms
        result["member_id"] = message.member_id
        return [result]

    except Exception as e:
        logger.error(
            f"Unexpected error processing message for Fitbit user {fitbit_id}: {e}"
        )
        # Mark the connection as unhealthy in case of any exception
        try:
            await update_fitbit_health_status(
                fitbit_id, False, f"Unexpected error: {e}"
            )
        except Exception as db_error:
            logger.error(f"Error updating Fitbit connection status: {db_error}")
        return []


def split_date_range(start_date, end_date, interval_days=99):
    """
    Splits the date range between start_date and end_date into multiple ranges,
    each spanning at most interval_days. Returns a list of (start, end) pendulum objects.

    Args:
        start_date: The start date as a pendulum datetime
        end_date: The end date as a pendulum datetime
        interval_days: Maximum number of days per range

    Returns:
        List of (start_date, end_date) tuples
    """
    try:
        ranges = []
        current_start = start_date

        # Safety check to ensure we're working with pendulum objects
        if not hasattr(current_start, "add") or not hasattr(end_date, "add"):
            # Convert to pendulum if they're not pendulum objects
            if isinstance(current_start, (int, float)):
                current_start = pendulum.from_timestamp(current_start)
            elif isinstance(current_start, str):
                current_start = pendulum.parse(current_start)

            if isinstance(end_date, (int, float)):
                end_date = pendulum.from_timestamp(end_date)
            elif isinstance(end_date, str):
                end_date = pendulum.parse(end_date)

        while current_start <= end_date:
            current_end = current_start.add(days=interval_days - 1)
            if current_end > end_date:
                current_end = end_date
            ranges.append((current_start, current_end))
            current_start = current_end.add(days=1)
        return ranges
    except Exception as e:
        logger.error(f"Error splitting date range: {e}")
        # Return a single range as fallback
        return [(start_date, end_date)]


async def enrich_hr_datetime(hr_data: list[dict]):
    for el in hr_data:
        tm = pendulum.parse(el["time"])
        dt = el["date"].replace(hour=tm.hour, minute=tm.minute, second=tm.second)
        el["dateTime"] = dt.isoformat()
    return hr_data


async def parse_hr_data(hr_response: dict):
    dataset = []
    intraday_heart_rate = hr_response.get("activities-heart-intraday", [])
    if intraday_heart_rate:
        dataset = intraday_heart_rate.get("dataset", dataset)
        if dataset:
            dataset = await enrich_hr_datetime(dataset)
    return dataset


async def fetch_fitbit_data(
    access_token: str,
    fitbit_service: FitbitLoader,
    fitbit_payload: FitbitPayload,
    fitbit_id: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Fetch data from Fitbit API.

    This function handles rate limiting with exponential backoff retry logic.

    Args:
        access_token: User access token
        fitbit_service: FitbitLoader instance
        fitbit_payload: Payload containing request parameters
        fitbit_id: Optional Fitbit ID for logging and metrics

    Returns:
        Dict[str, Any]: Processed Fitbit data
    """
    processed_data = {}
    activity = []
    all_sleep_data = []
    heart_rate = []
    spo2 = []

    try:
        # Log the API call if we have a fitbit_id
        if fitbit_id:
            logger.info(
                f"Fetching Fitbit data for user {fitbit_id} from {fitbit_payload.start_date} to {fitbit_payload.end_date}"
            )

            # Log metric for API call
            log_metric(
                "fitbit.api.call",
                tags={"fitbit_id": fitbit_id, "operation": "fetch_data"},
            )

        # Convert timestamps to pendulum datetime objects safely
        try:
            end_date = pendulum.now()
            # Ensure we're working with integers for timestamps
            start_timestamp = int(fitbit_payload.start_date)
            start_date = pendulum.from_timestamp(start_timestamp)

            # Calculate difference in days directly instead of using pendulum's diff
            # which can be problematic with certain timestamp formats
            diff_seconds = end_date.timestamp() - start_date.timestamp()
            diff_days = int(diff_seconds / (24 * 60 * 60))
        except Exception as e:
            logger.error(f"Error processing dates: {e}")
            # Default to a reasonable time range if date processing fails
            end_date = pendulum.now()
            start_date = end_date.subtract(days=30)  # Default to last 30 days
            diff_days = 30

        # Get user profile data
        user_data = await fitbit_service.get_user(access_token)
        if user_data.get("error"):
            error_type = user_data.get("error")
            if (
                error_type == FitbitErrorCode.RATE_LIMIT_EXCEEDED
                or user_data.get("status_code") == HTTPStatus.TOO_MANY_REQUESTS
            ):
                error_msg = f"Rate limit exceeded while fetching user data for Fitbit user {fitbit_payload.fitbit_id}"
                logger.error(error_msg)

                # Log metric for rate limit
                if fitbit_id:
                    log_metric(
                        "fitbit.api.rate_limit",
                        tags={
                            "fitbit_id": fitbit_id,
                            "endpoint": "user",
                            "operation": "get_user",
                        },
                        extra={"error_message": error_msg},
                    )
            else:
                logger.error(f"Error fetching user data: {error_type}")
            return processed_data
        user_data = user_data["user"]

        # Get user activities
        exercises = await fitbit_service.get_all_activities(
            access_token, after_date=start_date.date()
        )
        if exercises.get("error"):
            error_type = exercises.get("error")
            if (
                error_type == FitbitErrorCode.RATE_LIMIT_EXCEEDED
                or exercises.get("status_code") == HTTPStatus.TOO_MANY_REQUESTS
            ):
                error_msg = f"Rate limit exceeded while fetching activities for Fitbit user {fitbit_payload.fitbit_id}"
                logger.error(error_msg)

                # Log metric for rate limit
                if fitbit_id:
                    log_metric(
                        "fitbit.api.rate_limit",
                        tags={
                            "fitbit_id": fitbit_id,
                            "endpoint": "activities",
                            "operation": "get_all_activities",
                        },
                        extra={"error_message": error_msg},
                    )
            else:
                logger.error(f"Error fetching user activities: {error_type}")
            return processed_data
        exercises = exercises.get("activities", [])

        steps = await fitbit_service.get_steps(
            access_token, start_date=start_date.date(), end_date=end_date.date()
        )
        steps = steps.get("activities-steps", [])
        activity_lightly = await fitbit_service.get_active_minutes_lightly(
            access_token, start_date=start_date.date(), end_date=end_date.date()
        )
        activity_lightly = activity_lightly.get("activities-minutesLightlyActive", [])
        activity_fairly = await fitbit_service.get_active_minutes_fairly(
            access_token, start_date=start_date.date(), end_date=end_date.date()
        )
        activity_fairly = activity_fairly.get("activities-minutesFairlyActive", [])
        activity_very = await fitbit_service.get_active_minutes_very(
            access_token, start_date=start_date.date(), end_date=end_date.date()
        )
        activity_very = activity_very.get("activities-minutesVeryActive", [])
        distance = await fitbit_service.get_distance(
            access_token, start_date=start_date.date(), end_date=end_date.date()
        )
        distance = distance.get("activities-distance", [])

        aggregated_data = defaultdict(
            lambda: {
                "minutes": 0,
                "steps": 0,
                "distance": 0.0,
                "distance_unit": user_data["distanceUnit"],
                "weight_unit": user_data["weightUnit"],
                "exercise": [],
            }
        )

        # Helper function to add minutes data
        def add_minutes(activity_list):
            for activity in activity_list:
                date = activity["dateTime"]
                aggregated_data[date]["minutes"] += int(activity["value"])

        # Helper function to add steps data
        def add_steps(steps_list):
            for step in steps_list:
                date = step["dateTime"]
                aggregated_data[date]["steps"] += int(step["value"])

        def add_distance(distance_list):
            for distance in distance_list:
                date = distance["dateTime"]
                aggregated_data[date]["distance"] += float(distance["value"])

        def add_exercise(exercise_list):
            for exercise in exercise_list:
                date = pendulum.parse(exercise["originalStartTime"]).date().isoformat()
                if "exercise" not in aggregated_data[date]:
                    aggregated_data[date]["exercise"] = []
                seconds = 1000
                minute = 60
                raw_duration = exercise.get("duration", 0)
                duration = (raw_duration / seconds) / minute if raw_duration else 0
                exercise = {
                    "activity_name": exercise.get("activityName", "Name not specified"),
                    "duration": duration,
                    "distance": exercise.get("distance", 0),
                    "calories": exercise.get("calories", 0),
                    "steps": exercise.get("steps", 0),
                }
                aggregated_data[date]["exercise"].append(exercise)

        add_exercise(exercises) if exercises else None
        add_minutes(activity_lightly) if activity_lightly else None
        add_minutes(activity_fairly) if activity_fairly else None
        add_minutes(activity_very) if activity_very else None
        add_distance(distance) if distance else None
        add_steps(steps) if steps else None

        activity = [
            {"dateTime": date, **data} for date, data in aggregated_data.items()
        ]
    except Exception as e:
        logger.error(f"Error fetching fitbit data: {e}")

    all_sleep_data = []
    sleep_max_days = 99
    try:
        # Safely get sleep data with proper error handling
        async def get_sleep_safely(start, end):
            try:
                # Ensure we have proper date objects
                if hasattr(start, "date"):
                    start_date_obj = start.date()
                else:
                    # Fallback if start is not a pendulum object
                    logger.warning(
                        f"Start date {start} is not a pendulum object, using string representation"
                    )
                    start_date_obj = str(start)

                if hasattr(end, "date"):
                    end_date_obj = end.date()
                else:
                    # Fallback if end is not a pendulum object
                    logger.warning(
                        f"End date {end} is not a pendulum object, using string representation"
                    )
                    end_date_obj = str(end)

                sleep_data = await fitbit_service.get_sleep_data(
                    access_token, start_date=start_date_obj, end_date=end_date_obj
                )

                # Check if we got valid data
                if not isinstance(sleep_data, dict) or "sleep" not in sleep_data:
                    logger.warning(f"Invalid sleep data response: {sleep_data}")
                    return []

                return sleep_data["sleep"]
            except Exception as e:
                logger.error(
                    f"Error fetching sleep data for range {start} to {end}: {e}"
                )
                return []

        # Use date ranges if the time span is large
        if diff_days > sleep_max_days:
            date_ranges = split_date_range(
                start_date=start_date, end_date=end_date, interval_days=sleep_max_days
            )

            for range_start, range_end in date_ranges:
                sleep_chunk = await get_sleep_safely(range_start, range_end)
                all_sleep_data.extend(sleep_chunk)
        else:
            # For smaller ranges, just get the whole period
            sleep_data = await get_sleep_safely(start_date, end_date)
            all_sleep_data.extend(sleep_data)

    except Exception as e:
        logger.error(f"Error in sleep data processing: {e}")
        # Fallback to last 30 days if everything else fails
        try:
            fallback_start = end_date.subtract(days=30)

            # Define a simple fallback function since the nested one might not be accessible
            async def fallback_get_sleep():
                try:
                    sleep_data = await fitbit_service.get_sleep_data(
                        access_token,
                        start_date=fallback_start.date(),
                        end_date=end_date.date(),
                    )
                    return sleep_data.get("sleep", [])
                except Exception as e:
                    logger.error(f"Error in fallback sleep fetch: {e}")
                    return []

            sleep_data = await fallback_get_sleep()
            all_sleep_data.extend(sleep_data)
        except Exception as fallback_error:
            logger.error(f"Even fallback sleep data fetch failed: {fallback_error}")
            # Continue with empty sleep data rather than failing the whole function

    spo2 = []
    processed_data = {
        "activity_measurements": activity,
        "sleep_measurements": all_sleep_data,
        "heart_rate_measurements": heart_rate,
        "spo2_measurements": spo2,
    }

    return processed_data


async def handle_expired_tokens(fitbit_id: int) -> bool:
    """
    Handle the case when all tokens are expired and require re-authentication.

    This function marks the connection as unhealthy and logs that re-authentication is required.

    Args:
        fitbit_id: The Fitbit connection ID

    Returns:
        bool: True if the health status was updated successfully, False otherwise
    """
    # Mark the connection as unhealthy
    reason = "All tokens expired. Re-authentication required."
    success = await update_fitbit_health_status(fitbit_id, False, reason)

    # Get the user ID associated with this Fitbit connection for logging
    fitbit_connection = await Fitbit.filter(id=fitbit_id).first()
    if not fitbit_connection:
        error_msg = (
            f"Cannot handle expired tokens: Fitbit connection {fitbit_id} not found"
        )
        logger.error(error_msg)

        # Log metric for missing connection during token handling
        log_metric(
            "fitbit.connection.missing",
            tags={"fitbit_id": str(fitbit_id), "operation": "handle_expired_tokens"},
            extra={"error": error_msg},
        )
        return False

    # Log that re-authentication is required
    logger.warning(
        f"Fitbit user {fitbit_id} requires re-authentication due to expired tokens"
    )

    # Log metric for expired tokens
    log_metric(
        "fitbit.tokens.expired",
        tags={"fitbit_id": str(fitbit_id), "user_id": fitbit_connection.user_id},
    )

    return success


async def refresh_fitbit_token(
    fitbit_service: FitbitLoader, fitbit_id: int, fitbit_connection: Fitbit
) -> Optional[str]:
    """
    Refresh the Fitbit access token.

    Args:
        fitbit_service: The Fitbit API service
        fitbit_id: The Fitbit user ID
        fitbit_connection: The Fitbit connection record
        member_id: Optional member ID for notifications

    Returns:
        The new access token if successful, None otherwise
    """
    # Check if refresh token is expired
    if fitbit_connection.is_refresh_token_expired():
        logger.error(
            f"Refresh token expired for Fitbit user {fitbit_id}. Re-authentication required."
        )
        await handle_expired_tokens(fitbit_id)
        return None

    # Get the refresh token to use
    refresh_token = fitbit_connection.refresh_token

    # Refresh the access token
    user = await fitbit_service.refresh_token(refresh_token)
    if user.error:
        # Check if the error indicates an invalid refresh token
        if (
            "invalid_grant" in str(user.error).lower()
            or "expired" in str(user.error).lower()
        ):
            logger.error(
                f"Refresh token invalid or expired for Fitbit user {fitbit_id}. Re-authentication required."
            )
            await handle_expired_tokens(fitbit_id)
            return None

        # Handle other errors
        reason = f"Error refreshing token: {user.error}"
        logger.error(f"{reason} for Fitbit user {fitbit_id}")
        await update_fitbit_health_status(fitbit_id, False, reason)

        return None

    # Extract new tokens
    new_access_token = user.access_token
    new_refresh_token = user.refresh_token

    # Update tokens in the database with proper expiration timestamps
    updated_connection = await Fitbit.update_tokens(
        fitbit_id=fitbit_id,
        access_token=new_access_token,
        refresh_token=new_refresh_token,
        expires_in=user.expires_in,
    )

    # Mark as healthy since token refresh was successful
    await update_fitbit_health_status(fitbit_id, True, "Token refresh successful")
    logger.info(f"Updated tokens for Fitbit member: {fitbit_id}")

    return updated_connection.access_token


async def process_user_message(
    fitbit_service: FitbitLoader,
    fitbit_id: int,
    message: PullDataNotification,
) -> List:
    """
    Process a user's Fitbit message by ensuring valid tokens and fetching data.

    This function retrieves the user's Fitbit connection. If the access token has expired,
    it attempts to refresh it. After ensuring valid tokens, it fetches data based on the
    provided payload in the message.

    Args:
        fitbit_service (FitbitLoader): The service to manage Fitbit operations.
        fitbit_id (int): The Fitbit user identifier.
        message (PullDataNotification): The message containing the payload for data fetching.

    Returns:
        List: A list of processed Fitbit data, or an empty list if processing fails.
    """
    processed_data: List = []

    fitbit_connection = await Fitbit.filter(id=fitbit_id).first()
    if not fitbit_connection:
        logger.error(f"User tokens not found for Fitbit user: {fitbit_id}")
        return processed_data

    try:
        # Check if access token is expired
        if fitbit_connection.is_access_token_expired():
            logger.info(
                f"Access token expired for Fitbit user {fitbit_id}, refreshing..."
            )
            access_token = await refresh_fitbit_token(
                fitbit_service, fitbit_id, fitbit_connection
            )
            if not access_token:
                return processed_data  # Token refresh failed
        else:
            # Access token is still valid
            access_token = fitbit_connection.access_token

        processed_data = await fetch_fitbit_data(
            access_token=access_token,
            fitbit_service=fitbit_service,
            fitbit_payload=message.payload,
            fitbit_id=str(fitbit_id),  # Pass the fitbit_id for logging and metrics
        )

        # If processed_data is empty, it means there was an error (possibly rate limit)
        if not processed_data:
            reason = "Failed to fetch data (possibly due to rate limiting)"
            logger.warning(f"{reason} for Fitbit user {fitbit_id}")

            # Log metric for data fetch failure
            log_metric(
                "fitbit.data.fetch_failure",
                tags={"fitbit_id": str(fitbit_id), "reason": "rate_limit_or_api_error"},
            )
            # Don't mark the connection as unhealthy for rate limit errors
        else:
            # Mark as healthy since data fetch was successful
            await update_fitbit_health_status(fitbit_id, True, "Data fetch successful")

            # Log metric for successful data fetch
            log_metric(
                "fitbit.data.fetch_success",
                tags={"fitbit_id": str(fitbit_id)},
                extra={
                    "data_points": len(processed_data)
                    if isinstance(processed_data, list)
                    else 0
                },
            )

    except Exception as e:
        reason = f"Exception during API processing: {e}"
        logger.error(f"{reason} for Fitbit user {fitbit_id}")
        # Mark the connection as unhealthy in case of any exception
        await update_fitbit_health_status(fitbit_id, False, reason)

        # Log metric for general processing exceptions
        log_metric(
            "fitbit.processing.error",
            tags={"fitbit_id": str(fitbit_id), "error_type": type(e).__name__},
            extra={"error_message": str(e), "traceback": traceback.format_exc()},
        )

    return processed_data


def transform_to_fitbit_data_model(data: list[dict]):
    transformed_data = {
        "activity_measurements": [],
        "sleep_measurements": [],
        "heart_rate_measurements": [],
        "spo2_measurements": [],
        "weight_measurements": [],
    }
    for el in data:
        activity_measurements = el.get("activity_measurements", [])
        sleep_measurements = el.get("sleep_measurements", [])
        heart_rate_measurements = el.get("heart_rate_measurements", [])
        if activity_measurements:
            for activity in el["activity_measurements"]:
                exercises = activity.get("exercise", [])
                if exercises:
                    for exercise in exercises:
                        exercise["dateTime"] = activity["dateTime"]
                        exercise["distance_unit"] = activity["distance_unit"]
                        exercise["minutes"] = round(exercise["duration"])
                        exercise_activity = FitbitActivity.model_validate(exercise)
                        exercise_activity.category = ActivityCategory.EXERCISE
                        transformed_data["activity_measurements"].append(
                            exercise_activity
                        )
                fitbit_activity = FitbitActivity.model_validate(activity)
                transformed_data["activity_measurements"].append(fitbit_activity)
        if sleep_measurements:
            for sleep in el["sleep_measurements"]:
                fitbit_sleep = FitbitSleep.model_validate(sleep)
                transformed_data["sleep_measurements"].append(fitbit_sleep)
        if heart_rate_measurements:
            for heart_rate in el["heart_rate_measurements"]:
                fitbit_heart_rate = FitbitHeartRate.model_validate(heart_rate)
                transformed_data["heart_rate_measurements"].append(fitbit_heart_rate)
    return transformed_data


class FitbitToTortoiseConverter:
    """
    A reusable converter to transform Fitbit data models into Tortoise ORM models.
    """

    def __init__(self, member_id: int):
        self.member_id = member_id  # Associate all data with a specific member

    async def convert_activity(
        self, fitbit_activity: "FitbitActivity"
    ) -> list[Activity]:
        """
        Converts a FitbitActivity instance into one or more Activity records.
        """
        activities = []

        # Steps
        activities.append(
            Activity(
                value=fitbit_activity.steps,
                activity_category=ActivityCategory.WALK
                if fitbit_activity.category != ActivityCategory.EXERCISE
                else ActivityCategory.EXERCISE,
                unit=UnitOfMeasurement.STEP,
                member_id=self.member_id,
                device=DeviceType.FITBIT,
                created_at=fitbit_activity.utc_start_time(),
            )
        )

        # Distance
        activities.append(
            Activity(
                value=fitbit_activity.distance,
                activity_category=ActivityCategory.GENERAL
                if fitbit_activity.category != ActivityCategory.EXERCISE
                else ActivityCategory.EXERCISE,
                unit=UnitOfMeasurement.KM
                if fitbit_activity.distance_unit == "METRIC"
                else UnitOfMeasurement.MI,
                member_id=self.member_id,
                device=DeviceType.FITBIT,
                created_at=fitbit_activity.utc_start_time(),
            )
        )

        # Duration
        activities.append(
            Activity(
                value=fitbit_activity.duration,
                activity_category=ActivityCategory.GENERAL
                if fitbit_activity.category != ActivityCategory.EXERCISE
                else ActivityCategory.EXERCISE,
                unit=UnitOfMeasurement.MINUTE,
                member_id=self.member_id,
                device=DeviceType.FITBIT,
                created_at=fitbit_activity.utc_start_time(),
            )
        )

        # await Activity.bulk_create(activities)
        return activities

    async def convert_sleep(self, fitbit_sleep: "FitbitSleep") -> list[Sleep]:
        """
        Converts a FitbitSleep instance into a Sleep record.
        """

        levels = fitbit_sleep.levels.summary.model_dump()

        sleep = Sleep(
            start_time=pendulum.parse(fitbit_sleep.start_time),
            end_time=pendulum.parse(fitbit_sleep.end_time),
            duration=fitbit_sleep.get_duration_in_minutes(),
            bed_time=fitbit_sleep.time_in_bed,
            asleep_time=fitbit_sleep.minutes_asleep,
            deep_time=(levels.get("deep") or {}).get("minutes", 0),
            deep_count=(levels.get("deep") or {}).get("count", 0),
            light_time=(levels.get("light") or {}).get("minutes", 0),
            light_count=(levels.get("light") or {}).get("count", 0),
            rem_time=(levels.get("rem") or {}).get("minutes", 0),
            rem_count=(levels.get("rem") or {}).get("count", 0),
            wake_time=(levels.get("wake") or {}).get("minutes", 0),
            wake_count=(levels.get("wake") or {}).get("count", 0),
            efficiency=fitbit_sleep.efficiency,
            member_id=self.member_id,
            unit=UnitOfMeasurement.MINUTE,
            created_at=pendulum.parse(fitbit_sleep.start_time),
            device=DeviceType.FITBIT,
        )

        # Save the sleep record to the database
        # await sleep.save()
        return [sleep]

    async def convert_heart_rate(
        self, fitbit_heart_rate: "FitbitHeartRate"
    ) -> list[HeartRate]:
        """
        Converts a FitbitHeartRate instance into one or more HeartRate records.
        """
        heart_rates = []

        for zone in fitbit_heart_rate.value.heart_rate_zones:
            if not zone.minutes:
                continue

            current_time = pendulum.now()
            created_date = pendulum.parse(fitbit_heart_rate.date_time)
            created_at = created_date.replace(
                hour=current_time.hour,
                minute=current_time.minute,
                second=current_time.second,
            )
            heart_rate = HeartRate(
                value=zone.min,  # Example: Use the minimum heart rate in the zone
                member_id=self.member_id,
                unit=UnitOfMeasurement.BPM,
                device=DeviceType.FITBIT,
                created_at=created_at,
            )
            heart_rates.append(heart_rate)

        # Save all heart rate records to the database
        # await HeartRate.bulk_create(heart_rates)
        return heart_rates

    async def convert(
        self, fitbit_data: Union["FitbitActivity", "FitbitSleep", "FitbitHeartRate"]
    ):
        """
        Converts any Fitbit data model into the corresponding Tortoise ORM model(s).
        """
        if isinstance(fitbit_data, FitbitActivity):
            return await self.convert_activity(fitbit_data)
        elif isinstance(fitbit_data, FitbitSleep):
            return await self.convert_sleep(fitbit_data)
        elif isinstance(fitbit_data, FitbitHeartRate):
            return await self.convert_heart_rate(fitbit_data)
        else:
            raise ValueError(f"Unsupported Fitbit data type: {type(fitbit_data)}")
