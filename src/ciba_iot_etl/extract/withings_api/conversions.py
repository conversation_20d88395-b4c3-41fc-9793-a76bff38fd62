from datetime import datetime
from typing import Any, Dict, List

from ciba_iot_etl.extract.withings_api.common import (
    MeasureGetMeasGroup,
)
from dateutil import tz


def convert_to_lbs(value: float, unit: int) -> float:
    """Convert value to pounds."""
    return round(value * 2.20462, 1)  # Withings defaults to kg


def transform_withings_weights(
    measuregrps: List[MeasureGetMeasGroup], timezone_str: str
) -> List[Dict[str, Any]]:
    """Convert Withings measurements to a standard format."""
    timezone = tz.gettz(timezone_str)
    measurements = []
    for group in measuregrps:
        for measure in group.measures:
            if measure.type != 1:  # Only process weight measurements
                continue
            category = "weight"
            value = measure.value * (10**measure.unit)
            value = convert_to_lbs(value, measure.unit)
            created = datetime.fromtimestamp(group.date, timezone).isoformat()

            measurements.append(
                {
                    "category": category,
                    "value": value,
                    "unit": "lbs",
                    "created": created,
                }
            )
    return measurements
