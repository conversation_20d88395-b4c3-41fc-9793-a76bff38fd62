import json
import traceback
import asyncio
from typing import List, Dict, Any, Optional, Union, Tuple
from datetime import datetime

from loguru import logger

from ciba_iot_etl.models.db.withings import Withings
from ciba_iot_etl.extract.withings_api.core import WithingsLoader, WithingsStatusCode
from ciba_iot_etl.extract.withings_api.common import MeasureGetMeasResponse
from ciba_iot_etl.extract.withings_api.const import MeasureType
from ciba_iot_etl.models.pydantic.common import PullDataNotification
from ciba_iot_etl.models.pydantic.withings import WithingsPayload
from ciba_iot_etl.models.db.weight import Weight
from ciba_iot_etl.models.db.blood_pressure import BloodPressure
from ciba_iot_etl.models.db.heart_rate import HeartRate
from ciba_iot_etl.helpers.measurement import UnitOfMeasurement as CibaUnitOfMeasurement
from ciba_iot_etl.helpers.measurement import DeviceType
from ciba_iot_etl.extract.withings_api.common import MeasureGetMeasGroup


def log_metric(
    metric_name: str,
    value: Any = 1,
    tags: Dict[str, Any] = None,
    extra: Dict[str, Any] = None,
) -> None:
    """
    Log a metric in a format that can be easily parsed by Datadog.

    Args:
        metric_name: The name of the metric to log
        value: The value of the metric (default: 1 for counter-type metrics)
        tags: Dictionary of tags to associate with the metric
        extra: Additional information to include in the log
    """
    log_data = {
        "metric_name": metric_name,
        "metric_value": value,
        "metric_type": "count",
        "tags": tags or {},
    }

    if extra:
        log_data.update(extra)

    # Convert to JSON string and log with a consistent prefix for easy parsing
    logger.info(f"DATADOG_METRIC: {json.dumps(log_data)}")


async def update_withings_health_status(
    withings_id: Union[str, int], is_healthy: bool, reason: str = None
) -> bool:
    """
    Update the healthy status of a Withings connection.

    Args:
        withings_id: The ID of the Withings connection to update
        is_healthy: Whether the connection is healthy (True) or unhealthy (False)
        reason: Optional reason for the status change, for logging purposes

    Returns:
        bool: True if the update was successful, False otherwise
    """
    try:
        # Get current status to avoid unnecessary updates
        withings_connection = await Withings.filter(id=withings_id).first()
        if not withings_connection:
            error_msg = f"Cannot update health status: Withings connection {withings_id} not found"
            logger.error(error_msg)

            # Log metric for missing connection
            log_metric(
                "withings.connection.missing",
                tags={
                    "withings_id": str(withings_id),
                },
                extra={"error": error_msg},
            )
            return False

        # Only update if the status is changing
        if withings_connection.healthy != is_healthy:
            status_text = "healthy" if is_healthy else "unhealthy"
            reason_text = f" - Reason: {reason}" if reason else ""
            logger.info(
                f"Marking Withings connection {withings_id} as {status_text}{reason_text}"
            )

            # Log metric for device health status change
            log_metric(
                "withings.device.health_status",
                value=1 if is_healthy else 0,  # 1 for healthy, 0 for unhealthy
                tags={
                    "withings_id": str(withings_id),
                    "status": status_text,
                    "reason_category": reason.split(":")[0]
                    if reason and ":" in reason
                    else "general"
                    if reason
                    else "unknown",
                },
                extra={"reason": reason, "user_id": withings_connection.user_id},
            )

            await Withings.filter(id=withings_id).update(healthy=is_healthy)
            return True
        return True  # No update needed, but operation was successful
    except Exception as e:
        error_msg = f"Error updating Withings health status for {withings_id}: {e}"
        logger.error(error_msg)

        # Log metric for exceptions
        log_metric(
            "withings.error",
            tags={
                "withings_id": str(withings_id),
                "error_type": type(e).__name__,
                "operation": "update_health_status",
            },
            extra={"error_message": str(e), "traceback": traceback.format_exc()},
        )
        return False


async def process_withings_message(
    withings_service: WithingsLoader, message: PullDataNotification
):
    """Process a list of messages for Withings users concurrently.

    This function handles the Withings API rate limit (status code 601) by using
    the rate limit handling methods in the underlying functions.

    The Withings API has a rate limit of 120 requests per minute per API project.
    When the rate limit is exceeded, the API returns status code 601 in the response body
    while maintaining HTTP 200 OK status.
    """
    # Get withings_id from the message payload
    withings_id = message.payload.withings_id
    logger.info(f"Processing messages for Withings user: {withings_id}")

    try:
        # Process the message using process_user_message which handles rate limits
        result = await process_user_message(
            withings_service=withings_service,
            withings_id=withings_id,
            message=message,
        )

        # If no result, return empty list
        if not result:
            logger.warning(
                f"No data returned for Withings user: {withings_id}. This could be due to rate limiting or other errors."
            )
            return []

        # Convert the result to JSON and add additional fields
        try:
            result_json = result.model_dump_json()
            normal_json = json.loads(result_json)
            normal_json["withings_id"] = withings_id
            normal_json["platforms"] = message.platforms
            normal_json["member_id"] = message.member_id
            return [normal_json]
        except Exception as json_error:
            logger.error(
                f"Error converting result to JSON for Withings user {withings_id}: {json_error}"
            )
            return []

    except Exception as e:
        reason = f"Unexpected error processing message: {e}"
        logger.error(f"{reason} for Withings user {withings_id}")
        # Mark the connection as unhealthy in case of any exception
        await update_withings_health_status(withings_id, False, reason)
        return []


async def handle_expired_tokens(withings_id: int) -> bool:
    """
    Handle the case when all tokens are expired and require re-authentication.

    This function marks the connection as unhealthy and logs that re-authentication is required.

    Args:
        withings_id: The Withings connection ID

    Returns:
        bool: True if the health status was updated successfully, False otherwise
    """
    # Mark the connection as unhealthy
    reason = "All tokens expired. Re-authentication required."
    success = await update_withings_health_status(withings_id, False, reason)

    # Get the user ID associated with this Withings connection for logging
    withings_connection = await Withings.filter(id=withings_id).first()
    if not withings_connection:
        error_msg = (
            f"Cannot handle expired tokens: Withings connection {withings_id} not found"
        )
        logger.error(error_msg)

        # Log metric for missing connection during token handling
        log_metric(
            "withings.connection.missing",
            tags={
                "withings_id": str(withings_id),
                "operation": "handle_expired_tokens",
            },
            extra={"error": error_msg},
        )
        return False

    # Log that re-authentication is required
    logger.warning(
        f"Withings user {withings_id} requires re-authentication due to expired tokens"
    )

    # Log metric for expired tokens
    log_metric(
        "withings.tokens.expired",
        tags={"withings_id": str(withings_id), "user_id": withings_connection.user_id},
    )

    return success


async def refresh_withings_token(
    withings_service: WithingsLoader, withings_id: int, withings_connection: Withings
) -> Optional[str]:
    """Refresh the Withings access token.

    Args:
        withings_service: The Withings API service
        withings_id: The Withings user ID
        withings_connection: The Withings connection record

    Returns:
        The new access token if successful, None otherwise
    """
    # Check if refresh token is expired
    if withings_connection.is_refresh_token_expired():
        reason = "Refresh token expired (1 year limit). User needs to re-authenticate."
        logger.error(f"{reason} - Withings user {withings_id}")
        await handle_expired_tokens(withings_id)
        return None

    # Determine which refresh token to use
    if (
        not withings_connection.is_old_refresh_token_expired()
        and withings_connection.old_refresh_token
    ):
        # Use old refresh token if it's still valid (within 8 hours of issuance)
        logger.info(f"Using old refresh token for Withings user {withings_id}")
        refresh_token = withings_connection.old_refresh_token
    else:
        # Use current refresh token
        refresh_token = withings_connection.refresh_token

    # Refresh the access token
    # The get_new_token method already includes rate limit handling with retries
    user = await withings_service.get_new_token(refresh_token=refresh_token)

    # Check for rate limit error
    if (
        isinstance(user, dict)
        and user.get("status") == WithingsStatusCode.RATE_LIMIT_EXCEEDED
    ):
        reason = "Rate limit exceeded while refreshing token. Max retries reached."
        logger.error(f"{reason} - Withings user {withings_id}")

        # Log metric for rate limit during token refresh
        log_metric(
            "withings.api.rate_limit",
            tags={"withings_id": str(withings_id), "operation": "refresh_token"},
            extra={"error_message": reason},
        )

        # Don't mark the connection as unhealthy for rate limit errors
        # Just return None to indicate the refresh failed
        return None

    # Check for other errors
    error = user.get("error")
    if error:
        # Check if the error indicates an invalid refresh token
        if "invalid_grant" in str(error).lower() or "expired" in str(error).lower():
            logger.error(
                f"Refresh token invalid or expired for Withings user {withings_id}. Re-authentication required."
            )
            await handle_expired_tokens(withings_id)
            return None

        reason = f"Error refreshing user token: {error}"
        logger.error(f"{reason} - Withings user {withings_id}")
        await update_withings_health_status(withings_id, False, reason)

        return None

    # Extract new tokens
    new_access_token = user.get("access_token")
    new_refresh_token = user.get("refresh_token")
    if not new_access_token or not new_refresh_token:
        reason = f"Invalid token response: {user}"
        logger.error(f"{reason} - Withings user {withings_id}")
        await update_withings_health_status(withings_id, False, reason)

        return None

    # Update tokens in the database with proper expiration timestamps
    updated_connection = await Withings.update_tokens(
        withings_id=withings_id,
        access_token=new_access_token,
        refresh_token=new_refresh_token,
        expires_in=user.get("expires_in"),
    )

    # Mark as healthy since token refresh was successful
    await update_withings_health_status(withings_id, True, "Token refresh successful")
    logger.info(f"Updated tokens for Withings member: {withings_id}")

    return updated_connection.access_token


async def process_user_message(
    withings_service: WithingsLoader, withings_id: int, message: PullDataNotification
) -> MeasureGetMeasResponse:
    """Process messages for a single Withings user.

    This function handles token refresh and data fetching from the Withings API,
    including proper handling of rate limits (status code 601).

    The Withings API has a rate limit of 120 requests per minute per API project.
    When the rate limit is exceeded, the API returns status code 601 in the response body
    while maintaining HTTP 200 OK status.
    """
    processed_data: list = []

    # Retrieve user tokens from the database only once per user
    withings_connection = await Withings.filter(id=withings_id).first()

    if not withings_connection:
        logger.error(f"User tokens not found for Withings user: {withings_id}")
        return processed_data

    try:
        # Get or refresh access token
        if withings_connection.is_access_token_expired():
            logger.info(
                f"Access token expired for Withings user {withings_id}, refreshing..."
            )
            access_token = await refresh_withings_token(
                withings_service, withings_id, withings_connection
            )
            if not access_token:
                return processed_data  # Token refresh failed
        else:
            # Access token is still valid
            access_token = withings_connection.access_token

        # Fetch and process data
        # The fetch_withings_data function already includes rate limit handling
        processed_data = await fetch_withings_data(
            access_token=access_token,
            withings_service=withings_service,
            withings_payload=message.payload,
            withings_id=str(
                withings_id
            ),  # Pass the withings_id to expire old refresh token
        )

        # If processed_data is None, it means there was an error (possibly rate limit)
        if processed_data is None:
            reason = "Failed to fetch data (possibly due to rate limiting)"
            logger.warning(f"{reason} for Withings user {withings_id}")

            # Log metric for data fetch failure
            log_metric(
                "withings.data.fetch_failure",
                tags={
                    "withings_id": str(withings_id),
                    "reason": "rate_limit_or_api_error",
                },
            )
            return []
        else:
            # Mark as healthy since data fetch was successful
            await update_withings_health_status(
                withings_id, True, "Data fetch successful"
            )

            # Log metric for successful data fetch
            log_metric(
                "withings.data.fetch_success",
                tags={"withings_id": str(withings_id)},
                extra={
                    "data_points": len(processed_data.measuregrps)
                    if hasattr(processed_data, "measuregrps")
                    else 0
                },
            )

    except Exception as e:
        reason = f"Exception during API processing: {e}"
        logger.error(f"{reason} for Withings user {withings_id}")
        # Mark the connection as unhealthy in case of any exception
        await update_withings_health_status(withings_id, False, reason)

        # Log metric for general processing exceptions
        log_metric(
            "withings.processing.error",
            tags={"withings_id": str(withings_id), "error_type": type(e).__name__},
            extra={"error_message": str(e), "traceback": traceback.format_exc()},
        )
        return []

    return processed_data


async def fetch_withings_data(
    access_token: str,
    withings_service: "WithingsLoader",
    withings_payload: WithingsPayload,
    withings_id: Optional[str] = None,
) -> MeasureGetMeasResponse:
    """Fetch data from Withings API.

    This function handles the Withings API rate limit (status code 601) by using
    the rate limit handling methods in WithingsLoader.

    The Withings API has a rate limit of 120 requests per minute per API project.
    When the rate limit is exceeded, the API returns status code 601 in the response body
    while maintaining HTTP 200 OK status.

    Args:
        access_token: The access token to use for the API call
        withings_service: The WithingsLoader service to use
        withings_payload: The payload containing the request parameters
        withings_id: Optional Withings ID to expire the old refresh token

    Returns:
        The response from the Withings API, or None if there was an error
    """
    meastypes = ",".join([str(num) for num in withings_payload.meastypes])

    query_params = {
        "meastypes": meastypes,
        "category": withings_payload.category,
        "startdate": withings_payload.start_date,
        "enddate": withings_payload.end_date,
    }

    try:
        # The get_user_measure method already includes rate limit handling
        # It will automatically retry with exponential backoff if rate limit is hit
        user_data = await withings_service.get_user_measure(access_token, query_params)

        # If we have a withings_id and we're using a new access token, expire the old refresh token
        # According to Withings API docs, old refresh token expires when new access token is used
        if withings_id:
            # We don't need to await this or check the result - it's a best-effort operation
            # If it fails, the old refresh token will still expire after 8 hours
            asyncio.create_task(Withings.expire_old_refresh_token(withings_id))

            # Log metric for old refresh token expiration
            log_metric(
                "withings.tokens.old_refresh_expired",
                tags={
                    "withings_id": str(withings_id),
                    "reason": "new_access_token_used",
                },
            )

        # Check for success status (0)
        if user_data.status == WithingsStatusCode.SUCCESS:
            logger.debug(f"user data: {user_data.model_dump()}")

            # Log metric for successful API call
            log_metric(
                "withings.api.success",
                tags={
                    "withings_id": str(withings_payload.withings_id),
                    "endpoint": "measure",
                    "operation": "get_user_measure",
                },
            )
            return user_data.body

        # Check specifically for rate limit status (601)
        elif user_data.status == WithingsStatusCode.RATE_LIMIT_EXCEEDED:
            error_msg = f"Rate limit exceeded for Withings user {withings_payload.withings_id}. The WithingsLoader should have handled retries, but max retries may have been reached."
            logger.warning(error_msg)

            # Log metric for rate limit
            log_metric(
                "withings.api.rate_limit",
                tags={
                    "withings_id": str(withings_payload.withings_id),
                    "endpoint": "measure",
                    "operation": "get_user_measure",
                },
                extra={"error_message": error_msg},
            )
            # Return None to indicate failure due to rate limit
            # Note: We don't mark the connection as unhealthy for rate limit errors
            # as they are temporary and expected
            return None

        # Handle other error statuses
        else:
            # Define common error messages to avoid duplication
            INVALID_USERID = "Invalid userid"
            UNKNOWN_USERID = "Unknown userid"

            status_codes = {
                2: "Invalid credentials",
                100: "Missing required parameter",
                200: "Invalid parameter",
                201: "Unknown action",
                202: "Unexpected action",
                203: "No such subscription",
                204: "No such revocation",
                250: "User unknown",
                286: "No such user in family",
                293: "No notification",
                294: "No such user",
                304: "Incorrect signature",
                305: "Too many requests",
                328: "Invalid callback URL",
                342: "Invalid timezone",
                343: "Invalid timezone offset",
                344: "Invalid timezone name",
                505: "Invalid measure",
                506: "Invalid unit",
                509: "Invalid category",
                517: "Invalid date",
                521: "Invalid time",
                522: "Invalid duration",
                525: "Invalid offset",
                526: "Invalid lastupdate",
                527: "Invalid startdate",
                528: "Invalid enddate",
                529: "Invalid updatetime",
                530: "Invalid meastype",
                531: "Invalid field",
                532: INVALID_USERID,
                533: "Invalid shortname",
                555: UNKNOWN_USERID,
                556: "Incompatible user",
                601: "Too Many Requests",
                602: "Quota Exceeded",
                700: "Invalid comment",
                999: "Unknown error",
                2553: INVALID_USERID,
                2554: INVALID_USERID,
                2555: UNKNOWN_USERID,
                2556: "Incompatible user",
            }

            error_message = status_codes.get(
                user_data.status, f"Unknown error code: {user_data.status}"
            )
            error_msg = f"Error fetching Withings data for user {withings_payload.withings_id}: Status code {user_data.status} - {error_message}"
            logger.error(f"{error_msg} - {user_data}")

            # Log metric for API error
            log_metric(
                "withings.api.error",
                tags={
                    "withings_id": str(withings_payload.withings_id),
                    "endpoint": "measure",
                    "operation": "get_user_measure",
                    "status_code": str(user_data.status),
                    "error_type": error_message.replace(" ", "_").lower()
                    if error_message
                    else "unknown",
                },
                extra={"error_message": error_msg, "status_code": user_data.status},
            )
            return None
    except Exception as e:
        error_msg = f"Exception while fetching data for user {withings_payload.withings_id}: {e}"
        logger.warning(error_msg)

        # Log metric for exceptions during API calls
        log_metric(
            "withings.exception",
            tags={
                "withings_id": str(withings_payload.withings_id),
                "endpoint": "measure",
                "operation": "get_user_measure",
                "error_type": type(e).__name__,
            },
            extra={"error_message": str(e), "traceback": traceback.format_exc()},
        )
        return None


class WithingsToTortoiseConverter:
    """
    A reusable converter to transform Withings data into Tortoise ORM models.
    """

    def __init__(self, member_id: int):
        self.member_id = member_id  # Associate all data with a specific member

    async def transform_weight(self, timestamp: int, value: float) -> Weight:
        """
        Transforms a weight measurement into a Tortoise ORM Weight model.
        """
        weight = Weight(
            value=value,
            unit=CibaUnitOfMeasurement.KG,
            device=DeviceType.WITHINGS,
            created_at=datetime.fromtimestamp(timestamp),
        )
        weight.member_id = self.member_id
        return weight

    async def transform_blood_pressure(
        self, timestamp: int, systolic_value: float, diastolic_value: float
    ) -> BloodPressure:
        """
        Transforms a blood pressure measurement into a Tortoise ORM BloodPressure model.
        """
        bp = BloodPressure(
            systolic_value=systolic_value,
            diastolic_value=diastolic_value,
            unit=CibaUnitOfMeasurement.MM_HG,
            device=DeviceType.WITHINGS,
            created_at=datetime.fromtimestamp(timestamp),
        )
        bp.member_id = self.member_id
        return bp

    async def transform_heart_rate(self, timestamp: int, value: int) -> HeartRate:
        """
        Transforms a heart rate measurement into a Tortoise ORM HeartRate model.
        """
        hr = HeartRate(
            value=int(float(value)),
            unit=CibaUnitOfMeasurement.BPM,
            device=DeviceType.WITHINGS,
            created_at=datetime.fromtimestamp(timestamp),
        )
        hr.member_id = self.member_id
        return hr

    @staticmethod
    def verify_misentered_lb(weights: List[float]) -> Tuple[List[float], Optional[int]]:
        """
        Given a list of floats (all in kg, except maybe one in lb),
        scan for a middle value that is ≥10% off both neighbors. If found,
        pretend it was entered in lb, convert it to kg, and check whether the
        converted value now falls within ±10% of BOTH neighbors. If so, replace it.
        Return (corrected_list, index_of_correction) or (original_list, None).
        """
        LB_TO_KG = 1.0 / 2.20462
        corrected = weights.copy()
        corrected_index = None

        for i in range(1, len(weights) - 1):
            prev_w = weights[i - 1]
            curr_w = weights[i]
            next_w = weights[i + 1]

            prev_diff = abs(curr_w - prev_w) / prev_w
            next_diff = abs(curr_w - next_w) / next_w

            if prev_diff >= 0.10 and next_diff >= 0.10:
                # Candidate spike—pretend curr_w is in pounds
                converted = curr_w * LB_TO_KG

                new_prev_diff = abs(converted - prev_w) / prev_w
                new_next_diff = abs(converted - next_w) / next_w

                if new_prev_diff < 0.10 and new_next_diff < 0.10:
                    corrected[i] = converted
                    corrected_index = i
                break

        return corrected, corrected_index

    async def convert(
        self, measure_getmeas: "MeasureGetMeasResponse"
    ) -> Dict[str, Any]:
        """
        Transforms Withings data into Tortoise ORM models.
        After collecting all weight_measurements (in kg), we run
        verify_misentered_lb(...) once to see if exactly one entry was
        typed in pounds by mistake. If so, we replace it with its
        kg‐converted value.
        """
        weight_measurements: List[Any] = []
        blood_pressure_measurements: List[Any] = []
        heart_rate_measurements: List[Any] = []

        # Temporary dict for pairing systolic/diastolic by timestamp
        bp_measurements_dict: Dict[int, Dict[str, float]] = {}

        # 1) Process each measure group and fill in weight/HR/BP lists
        for measuregrp in measure_getmeas.measuregrps:
            await self._process_measure_group(
                measuregrp,
                weight_measurements,
                heart_rate_measurements,
                bp_measurements_dict,
            )

        # 2) Once all groups are processed, collect blood pressure readings
        await self._collect_blood_pressure_measurements(
            bp_measurements_dict, blood_pressure_measurements
        )

        # 3) Now—**after** collecting all weight_measurements—run our check
        #    to see if exactly one of them was actually in lb rather than kg.
        #
        #    We need to extract a plain list of floats from `weight_measurements`.
        #    Adjust the “.value” below to match your transform_weight return type
        #    (e.g. maybe it’s `.weight_kg`, or maybe a dict key “['value']”).
        #
        raw_weights: List[float] = []
        for wm in weight_measurements:
            # --- assume transform_weight returned an object with `.value` being the kg reading ---
            raw_weights.append(float(wm.value))  # Convert Decimal to float

        corrected_weights, corrected_index = self.verify_misentered_lb(raw_weights)

        # 4) If `corrected_index` is not None, patch that single measurement
        if corrected_index is not None:
            new_kg_value = corrected_weights[corrected_index]
            # modify the existing Tortoise model or dict in place:
            weight_measurements[corrected_index].value = new_kg_value

        transformed_data = {
            "weight_measurements": weight_measurements,
            "blood_pressure_measurements": blood_pressure_measurements,
            "heart_rate_measurements": heart_rate_measurements,
        }
        return transformed_data

    async def _process_measure_group(
        self,
        measuregrp: "MeasureGetMeasGroup",
        weight_measurements: List[Any],
        heart_rate_measurements: List[Any],
        bp_measurements_dict: Dict[int, Dict[str, float]],
    ):
        """
        Processes a single measure group and updates the lists:
         - weight_measurements
         - heart_rate_measurements
         - bp_measurements_dict (temporary dict to pair systolic/diastolic values)
        """
        timestamp = measuregrp.date  # Unix timestamp

        for measure in measuregrp.measures:
            value = measure.value * (10**measure.unit)
            measure_type = measure.type

            if measure_type == MeasureType.WEIGHT.value:
                # transform_weight(...) should create a model or dict with `.value == kg`
                weight_model = await self.transform_weight(timestamp, value)
                weight_measurements.append(weight_model)

            elif measure_type in [
                MeasureType.DIASTOLIC_BLOOD_PRESSURE.value,
                MeasureType.SYSTOLIC_BLOOD_PRESSURE.value,
            ]:
                self._update_blood_pressure_dict(
                    bp_measurements_dict, timestamp, measure_type, value
                )

            elif measure_type == MeasureType.HEART_RATE.value:
                hr_model = await self.transform_heart_rate(timestamp, value)
                heart_rate_measurements.append(hr_model)

        # NOTE: We no longer call verify_misentered_lb here—waiting until after the loop.

    def _update_blood_pressure_dict(
        self, bp_measurements_dict, timestamp, measure_type, value
    ):
        """
        Updates the blood pressure dictionary with systolic or diastolic values.
        """
        if timestamp not in bp_measurements_dict:
            bp_measurements_dict[timestamp] = {
                "systolic_value": None,
                "diastolic_value": None,
            }

        if measure_type == MeasureType.SYSTOLIC_BLOOD_PRESSURE.value:
            bp_measurements_dict[timestamp]["systolic_value"] = value
        elif measure_type == MeasureType.DIASTOLIC_BLOOD_PRESSURE.value:
            bp_measurements_dict[timestamp]["diastolic_value"] = value

    async def _collect_blood_pressure_measurements(
        self, bp_measurements_dict, blood_pressure_measurements
    ):
        """
        Collects blood pressure measurements from the dictionary and appends them to the list.
        """
        for timestamp, values in bp_measurements_dict.items():
            if (
                values["systolic_value"] is not None
                and values["diastolic_value"] is not None
            ):
                blood_pressure_measurement = await self.transform_blood_pressure(
                    timestamp, values["systolic_value"], values["diastolic_value"]
                )
                blood_pressure_measurements.append(blood_pressure_measurement)
