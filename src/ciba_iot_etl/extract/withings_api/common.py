import datetime
from datetime import tzinfo
from enum import IntEnum
from typing import Any, Dict, List, Optional, Union, Type, TypeVar
from dateutil import tz

from dateutil.tz import tzlocal
from pydantic import BaseModel, validator
from typing_extensions import Final

# from arrow import Arrow, get as arrow_get
from ciba_iot_etl.extract.withings_api.const import (
    MeasureType,
)
import logging

_LOGGER = logging.getLogger("extract.withings_api")

_GenericType = TypeVar("_GenericType")


def to_enum(
    enum_class: Type[_GenericType], value: Any, default_value: _GenericType
) -> _GenericType:
    """Attempt to convert a value to an enum."""
    try:
        return enum_class(value)  # type: ignore
    except ValueError:
        _LOGGER.warning(
            "Unsupported %s value %s. Replacing with UNKNOWN value %s. Please report this warning to the developer to ensure proper support.",
            str(enum_class),
            value,
            str(default_value),
        )
        return default_value


class MeasureGetMeasGroupCategory(IntEnum):
    """Measure categories."""

    UNKNOWN = -999999
    REAL = 1
    USER_OBJECTIVES = 2


class MeasureGetMeasGroupAttrib(IntEnum):
    """Measure group attributions."""

    UNKNOWN = -1
    DEVICE_ENTRY_FOR_USER = 0
    DEVICE_ENTRY_FOR_USER_AMBIGUOUS = 1
    MANUAL_USER_ENTRY = 2
    MANUAL_USER_DURING_ACCOUNT_CREATION = 4
    MEASURE_AUTO = 5
    MEASURE_USER_CONFIRMED = 7
    SAME_AS_DEVICE_ENTRY_FOR_USER = 8


# class ArrowType(Arrow):
#     """Subclass of Arrow for parsing dates."""
#
#     @classmethod
#     def __get_validators__(cls) -> Any:
#         # one or more validators may be yielded which will be called in the
#         # order to validate the input, each validator will receive as an input
#         # the value returned from the previous validator
#         yield cls.validate
#
#     @classmethod
#     def validate(cls, value: Any, field: Any) -> Arrow:
#         """Convert input to the desired object."""
#         if isinstance(value, str):
#             if value.isdigit():
#                 return arrow_get(int(value))
#             return arrow_get(value)
#         if isinstance(value, int):
#             return arrow_get(value)
#         if isinstance(value, (Arrow, ArrowType)):
#             return value
#
#         raise TypeError("string or int required")


class TimeZone(tzlocal):
    """Subclass of tzinfo for parsing timezones."""

    @classmethod
    def __get_validators__(cls) -> Any:
        # one or more validators may be yielded which will be called in the
        # order to validate the input, each validator will receive as an input
        # the value returned from the previous validator
        yield cls.validate

    @classmethod
    def validate(cls, value: Any, field: Any) -> tzinfo:
        """Convert input to the desired object."""
        if isinstance(value, tzinfo):
            return value
        if isinstance(value, str):
            timezone: Final = tz.gettz(value)
            if timezone:
                return timezone
            raise ValueError(f"Invalid timezone provided {value}")

        raise TypeError("string or tzinfo required")


class ConfiguredBaseModel(BaseModel):
    """An already configured pydantic model."""

    class Config:
        """Config for pydantic model."""

        arbitrary_types_allowed = True  # Allow arbitrary types

        ignore_extra: Final = True
        allow_extra: Final = False
        allow_mutation: Final = False


class MeasureGetMeasMeasure(ConfiguredBaseModel):
    """MeasureGetMeasMeasure."""

    type: MeasureType
    unit: int
    value: int

    @validator("type", pre=True)
    @classmethod
    def _type_to_enum(cls, value: Any) -> MeasureType:
        return to_enum(MeasureType, value, MeasureType.UNKNOWN)


class MeasureGetMeasGroup(BaseModel):
    """
    Model for a measure group.
    Appear that modelid sometimes can be string and sometimes int.
    """

    grpid: int
    attrib: int
    date: int
    created: int
    modified: int
    category: int
    deviceid: Optional[str] = None
    hash_deviceid: Optional[str] = None
    measures: List[MeasureGetMeasMeasure]
    modelid: Optional[Any] = None
    model: Optional[str] = None
    comment: Optional[str] = None

    @validator("attrib", pre=True)
    @classmethod
    def _attrib_to_enum(cls, value: Any) -> MeasureGetMeasGroupAttrib:
        return to_enum(
            MeasureGetMeasGroupAttrib, value, MeasureGetMeasGroupAttrib.UNKNOWN
        )

    @validator("category", pre=True)
    @classmethod
    def _category_to_enum(cls, value: Any) -> MeasureGetMeasGroupCategory:
        return to_enum(
            MeasureGetMeasGroupCategory, value, MeasureGetMeasGroupCategory.UNKNOWN
        )


class MeasureGetMeasResponse(ConfiguredBaseModel):
    """MeasureGetMeasResponse."""

    measuregrps: List[MeasureGetMeasGroup]
    more: Optional[bool] = False
    offset: Optional[int] = 0
    timezone: str
    updatetime: datetime.datetime

    # @validator("updatetime")
    # @classmethod
    # def _set_timezone_on_updatetime(
    #         cls, value: ArrowType, values: Dict[str, Any]
    # ) -> Arrow:
    #     return cast(Arrow, value.to(values["timezone"]))


class WithingsResponse(ConfiguredBaseModel):
    status: int
    body: Union[MeasureGetMeasResponse, Dict[str, Any]]

    @classmethod
    def from_dict(
        cls: Type["WithingsResponse"],
        data: Dict[str, Any],
        body_type: Type[_GenericType],
    ) -> "WithingsResponse":
        error = data.get("error")
        if error:
            return cls(status=data["status"], body={"error": error})
        body_data = data["body"]
        body = body_type(**body_data)
        return cls(status=data["status"], body=body)
