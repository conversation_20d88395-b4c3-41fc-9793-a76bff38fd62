from enum import IntEnum
from typing import Final


class NotificationCategory(IntEnum):
    """Notification categories."""

    WEIGHT = 1
    TEMPERATURE = 2
    PRESSURE = 4
    ACTIVITY = 16
    SLEEP = 44
    ACTION_ON_USER = 46
    BED_IN = 50
    BED_OUT = 51
    INFLATE_DONE = 52
    EGC = 54
    GLUCOSE = 58


class MeasureType(IntEnum):
    """Measure types."""

    UNKNOWN = -999999
    WEIGHT = 1
    HEIGHT = 4
    FAT_FREE_MASS = 5
    FAT_RATIO = 6
    FAT_MASS_WEIGHT = 8
    DIASTOLIC_BLOOD_PRESSURE = 9
    SYSTOLIC_BLOOD_PRESSURE = 10
    HEART_RATE = 11
    TEMPERATURE = 12
    SP02 = 54
    BODY_TEMPERATURE = 71
    SKIN_TEMPERATURE = 73
    MUSCLE_MASS = 76
    HYDRATION = 77
    BONE_MASS = 88
    PULSE_WAVE_VELOCITY = 91
    VO2 = 123
    QRS_INTERVAL = 135
    PR_INTERVAL = 136
    QT_INTERVAL = 137
    CORRECTED_QT_INTERVAL = 138
    ATRIAL_FIBRILLATION = 139
    EXTRA_CELL_WATER_IN_KG = 168
    INTRA_CELL_WATER_IN_KG = 169
    VISCERAL_FAT_MASS = 170
    FAT_MASS_4_SEGMENTS_IN_MASS = 174
    MUSCLE_MASS_4_SEGMENTS_IN_MASS = 175
    ELECTRODERMAL_ACTIVITY_FEET = 196
    ELECTRODERMAL_ACTIVITY_LEFT_FOOT = 197
    ELECTRODERMAL_ACTIVITY_RIGHT_FOOT = 198


class MeasureUnits(IntEnum):
    """Measurement units."""

    UNKNOWN = 0
    KG = 1
    METER = 2
    PERCENT = 3
    MMHG = 4
    BPM = 5
    CELSIUS = 6
    M_PER_S = 7
    ML_PER_MIN_PER_KG = 8
    NONE = 9  # For measurements without units


class MeasureTypeUnits(IntEnum):
    WEIGHT = MeasureUnits.KG
    HEIGHT = MeasureUnits.METER
    FAT_FREE_MASS = MeasureUnits.KG
    FAT_RATIO = MeasureUnits.PERCENT
    FAT_MASS_WEIGHT = MeasureUnits.KG
    DIASTOLIC_BLOOD_PRESSURE = MeasureUnits.MMHG
    SYSTOLIC_BLOOD_PRESSURE = MeasureUnits.MMHG
    HEART_RATE = MeasureUnits.BPM
    TEMPERATURE = MeasureUnits.CELSIUS
    SP02 = MeasureUnits.PERCENT
    BODY_TEMPERATURE = MeasureUnits.CELSIUS
    SKIN_TEMPERATURE = MeasureUnits.CELSIUS
    MUSCLE_MASS = MeasureUnits.KG
    HYDRATION = MeasureUnits.KG
    BONE_MASS = MeasureUnits.KG
    PULSE_WAVE_VELOCITY = MeasureUnits.M_PER_S
    VO2 = MeasureUnits.ML_PER_MIN_PER_KG
    ATRIAL_FIBRILLATION = MeasureUnits.NONE
    QRS_INTERVAL = MeasureUnits.NONE
    PR_INTERVAL = MeasureUnits.NONE
    QT_INTERVAL = MeasureUnits.NONE
    CORRECTED_QT_INTERVAL = MeasureUnits.NONE
    EXTRA_CELL_WATER_IN_KG = MeasureUnits.KG
    INTRA_CELL_WATER_IN_KG = MeasureUnits.KG
    VISCERAL_FAT_MASS = MeasureUnits.NONE
    FAT_MASS_4_SEGMENTS_IN_MASS = MeasureUnits.NONE
    MUSCLE_MASS_4_SEGMENTS_IN_MASS = MeasureUnits.NONE
    ELECTRODERMAL_ACTIVITY_FEET = MeasureUnits.NONE
    ELECTRODERMAL_ACTIVITY_LEFT_FOOT = MeasureUnits.NONE
    ELECTRODERMAL_ACTIVITY_RIGHT_FOOT = MeasureUnits.NONE

    @staticmethod
    def get_unit(measure_type):
        try:
            return MeasureTypeUnits[measure_type].name
        except KeyError:
            return MeasureUnits.UNKNOWN.name


STATUS_SUCCESS: Final = (0,)

STATUS_AUTH_FAILED: Final = (100, 101, 102, 200, 401)

STATUS_INVALID_PARAMS: Final = (
    201,
    202,
    203,
    204,
    205,
    206,
    207,
    208,
    209,
    210,
    211,
    212,
    213,
    216,
    217,
    218,
    220,
    221,
    223,
    225,
    227,
    228,
    229,
    230,
    234,
    235,
    236,
    238,
    240,
    241,
    242,
    243,
    244,
    245,
    246,
    247,
    248,
    249,
    250,
    251,
    252,
    254,
    260,
    261,
    262,
    263,
    264,
    265,
    266,
    267,
    271,
    272,
    275,
    276,
    283,
    284,
    285,
    286,
    287,
    288,
    290,
    293,
    294,
    295,
    297,
    300,
    301,
    302,
    303,
    304,
    321,
    323,
    324,
    325,
    326,
    327,
    328,
    329,
    330,
    331,
    332,
    333,
    334,
    335,
    336,
    337,
    338,
    339,
    340,
    341,
    342,
    343,
    344,
    345,
    346,
    347,
    348,
    349,
    350,
    351,
    352,
    353,
    380,
    381,
    382,
    400,
    501,
    502,
    503,
    504,
    505,
    506,
    509,
    510,
    511,
    523,
    532,
    3017,
    3018,
    3019,
)

STATUS_UNAUTHORIZED: Final = (214, 277, 2553, 2554, 2555)

STATUS_ERROR_OCCURRED: Final = (
    215,
    219,
    222,
    224,
    226,
    231,
    233,
    237,
    253,
    255,
    256,
    257,
    258,
    259,
    268,
    269,
    270,
    273,
    274,
    278,
    279,
    280,
    281,
    282,
    289,
    291,
    292,
    296,
    298,
    305,
    306,
    308,
    309,
    310,
    311,
    312,
    313,
    314,
    315,
    316,
    317,
    318,
    319,
    320,
    322,
    370,
    371,
    372,
    373,
    374,
    375,
    383,
    391,
    402,
    516,
    517,
    518,
    519,
    520,
    521,
    525,
    526,
    527,
    528,
    529,
    530,
    531,
    533,
    602,
    700,
    1051,
    1052,
    1053,
    1054,
    2551,
    2552,
    2556,
    2557,
    2558,
    2559,
    3000,
    3001,
    3002,
    3003,
    3004,
    3005,
    3006,
    3007,
    3008,
    3009,
    3010,
    3011,
    3012,
    3013,
    3014,
    3015,
    3016,
    3020,
    3021,
    3022,
    3023,
    3024,
    5000,
    5001,
    5005,
    5006,
    6000,
    6010,
    6011,
    9000,
    10000,
)

STATUS_TIMEOUT: Final = (522,)
STATUS_BAD_STATE: Final = (524,)
STATUS_TOO_MANY_REQUESTS: Final = (601,)
