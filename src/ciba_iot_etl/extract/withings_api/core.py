from datetime import datetime, date
from typing import Union, Dict, Any, Optional
import time
import logging
import asyncio

# import arrow
from ciba_iot_etl.extract.withings_api.const import MeasureType

from ciba_iot_etl.extract.withings_api.common import (
    WithingsResponse,
    MeasureGetMeasResponse,
)
from ciba_iot_etl.common.api import BaseLoader
from ciba_iot_etl.helpers.crypto import base64_url_encode, generate_unique_state

DateType = Union[date, datetime, int, str]
ParamsType = Dict[str, Union[str, int, bool]]
logger = logging.getLogger(__name__)


# Withings API status codes
class WithingsStatusCode:
    SUCCESS = 0
    RATE_LIMIT_EXCEEDED = 601


class WithingsLoader(BaseLoader):
    limit_period: int = 60
    limit_calls: int = 100  # 100 calls per minute
    OAUTH_URL: str = "https://account.withings.com/oauth2_user/authorize2"
    API_URL: str = "https://wbsapi.withings.net"
    PATH_V2_USER: str = "v2/user"
    PATH_V2_MEASURE: str = "v2/measure"
    PATH_MEASURE: str = "measure"
    PATH_V2_SLEEP: str = "v2/sleep"
    PATH_NOTIFY: str = "notify"
    PATH_V2_HEART: str = "v2/heart"

    # Rate limiting parameters
    MAX_RETRIES: int = 5
    INITIAL_BACKOFF: float = 1.0

    def __init__(
        self,
        client_id: str,
        client_secret: str,
        redirect_uri: str,
        callback_uri: str,
        notification_callback_uri: str,
        scope: str,
        state: str = "random_state",
        demo: bool = False,
    ):
        self.client_id = client_id
        self.client_secret = client_secret
        self.state = state
        self.redirect_uri = redirect_uri
        self.callback_uri = callback_uri
        self.notification_callback_uri = notification_callback_uri
        self.scope = scope
        self.demo = demo
        super(WithingsLoader, self).__init__(self.limit_calls, self.limit_period)

    def get_auth_headers(self, access_token: str) -> dict:
        """
        Build auth headers dict
        """
        return {"Authorization": f"Bearer {access_token}"}

    def get_real_value(self, value, unit):
        """
        Power of ten to multiply the value field to get the real value.
        Formula: value * 10^unit = real value.
        Eg: value = 20 and unit = -1 => real value = 2
        """
        return value * pow(10, unit)

    def build_withings_meastypes(self) -> str:
        """
        Build meastypes query param
        """

        return ",".join(MeasureType)

    def yield_withings_meastype(self) -> int:
        """
        Build meastypes query param
        """
        meastypes_temp = [1, 4, 6, 9, 10, 11, 76]
        for i in meastypes_temp:
            yield i

    def get_request_timestamp(self, date=datetime.utcnow()) -> int:
        """
        Build request timestamp
        """
        return int(time.mktime(date.timetuple()))

    async def get_user_devices(self, access_token: str) -> dict:
        """
        Returns the list of user linked devices.
        """
        user_devices = await self._call_api_with_rate_limit_handling(
            f"{self.API_URL}/{self.PATH_V2_USER}",
            method="POST",
            headers=self.get_auth_headers(access_token),
            payload={"action": "getdevice"},
        )
        return user_devices

    async def get_user_measure(
        self, access_token: str, query_params: dict
    ) -> WithingsResponse:
        """
        https://developer.withings.com/api-reference#operation/measure-getmeas
        Returns the list of user measure meas.

        This method handles rate limiting with status code 601 by implementing retry logic.
        """
        base_params = {"action": "getmeas"}
        user_devices = await self._call_api_with_rate_limit_handling(
            url=f"{self.API_URL}/{self.PATH_MEASURE}",
            method="POST",
            headers=self.get_auth_headers(access_token),
            payload={**base_params, **query_params},
        )

        return WithingsResponse.from_dict(user_devices, MeasureGetMeasResponse)

    def get_user_activity(self, access_token: str, query_params: dict) -> dict:
        """
        https://developer.withings.com/api-reference#operation/measurev2-getactivity
        Returns daily aggregated activity data of a user.
        """
        base_params = {"action": "getactivity"}
        user_devices = self.call_api(
            f"{self.API_URL}/{self.PATH_V2_MEASURE}",
            method="POST",
            headers=self.get_auth_headers(access_token),
            payload={**base_params, **query_params},
        )
        return user_devices

    def get_user_intraday_activity(self, access_token: str) -> dict:
        """
        https://developer.withings.com/api-reference#operation/measurev2-getintradayactivity
        Returns user activity data captured at high frequency.
        """
        user_devices = self.call_api(
            f"{self.API_URL}/{self.PATH_V2_MEASURE}",
            method="POST",
            headers=self.get_auth_headers(access_token),
            payload={"action": "getintradayactivity"},
        )
        return user_devices

    def get_user_workouts(self, access_token: str) -> dict:
        """
        https://developer.withings.com/api-reference#operation/measurev2-getworkouts
        Returns workout summaries, which are an aggregation all data that was captured during that workout.
        """
        user_devices = self.call_api(
            f"{self.API_URL}/{self.PATH_V2_MEASURE}",
            method="POST",
            headers=self.get_auth_headers(access_token),
            payload={"action": "getworkouts"},
        )
        return user_devices

    async def get_new_token(
        self, refresh_token: str, query_params: dict = {}
    ) -> Dict[str, Any]:
        """
        https://developer.withings.com/api-reference/#tag/oauth2/operation/oauth2-getaccesstoken
        https://wbsapi.withings.net/v2/oauth2
        Returns new token.

        This method handles rate limiting with status code 601 by implementing retry logic.
        """
        base_params = {
            "action": "requesttoken",
            "grant_type": "refresh_token",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "refresh_token": refresh_token,
        }
        user = await self._call_api_with_rate_limit_handling(
            url=f"{self.API_URL}/v2/oauth2",
            method="POST",
            headers=self.get_auth_headers(refresh_token),
            querystring={**base_params, **query_params},
        )
        if "error" in user:
            return user
        return user.get("body", {})

    async def _generate_headers(self) -> dict:
        """Generate headers for the request."""
        credentials = f"{self.client_id}:{self.client_secret}"
        return {
            "Authorization": f"Basic {base64_url_encode(credentials.encode('utf-8'))}",
            "Content-Type": "application/x-www-form-urlencoded",
        }

    async def _handle_rate_limit(
        self, response: Dict[str, Any], retry_count: int
    ) -> Optional[Dict[str, Any]]:
        """
        Handle Withings API rate limit response (status code 601).

        Args:
            response: The API response to check
            retry_count: Current retry attempt number

        Returns:
            None if rate limit was detected and we should retry, or the original response if no rate limit or max retries reached
        """
        # Check if response contains status code 601 (rate limit exceeded)
        if (
            isinstance(response, dict)
            and response.get("status") == WithingsStatusCode.RATE_LIMIT_EXCEEDED
        ):
            if retry_count < self.MAX_RETRIES:
                # Calculate backoff time with exponential backoff
                backoff_time = self.INITIAL_BACKOFF * (2**retry_count)
                logger.warning(
                    f"Withings API rate limit exceeded. Retrying in {backoff_time} seconds (attempt {retry_count + 1}/{self.MAX_RETRIES})"
                )
                await asyncio.sleep(backoff_time)
                return None  # Signal to retry the request
            else:
                logger.error("Withings API rate limit exceeded. Max retries reached.")
                return response  # Return the rate limit response after max retries

        # No rate limit detected, return the original response
        return response

    async def _call_api_with_rate_limit_handling(
        self,
        url: str,
        method: str = "POST",
        headers: Optional[Dict[str, str]] = None,
        payload: Optional[Dict[str, Any]] = None,
        querystring: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Call API with Withings rate limit handling.

        This method wraps the BaseLoader.call_api method with specific handling for Withings rate limit responses.
        """
        retry_count = 0

        while retry_count <= self.MAX_RETRIES:
            response = await self.call_api(
                url=url,
                method=method,
                headers=headers,
                payload=payload,
                querystring=querystring,
            )

            # Handle rate limit response
            result = await self._handle_rate_limit(response, retry_count)
            if result is not None:
                return result  # Either no rate limit or max retries reached

            # If we get here, we need to retry
            retry_count += 1

        # This should not be reached, but just in case
        return {
            "error": "Max retries reached",
            "status": WithingsStatusCode.RATE_LIMIT_EXCEEDED,
        }

    async def get_auth_page_url(self, state: str = None) -> dict:
        """
        Get the URL to redirect the user to for authentication.
        """

        unique_state = generate_unique_state() if not state else state
        result = {
            "auth_url": "",
            "error": "",
            "code_challenge": "",
            "code_verifier": "",
            "state": unique_state,
            "redirect_uri": self.redirect_uri,
        }

        params = {
            "response_type": "code",
            "client_id": self.client_id,
            "state": unique_state,
            "scope": self.scope,
            "redirect_uri": self.redirect_uri,
        }

        if self.demo:
            params["mode"] = "demo"

        logger.info("Generating Withings authorization URL.")

        headers = await self._generate_headers()
        response = await self._call_api_with_rate_limit_handling(
            url=self.OAUTH_URL,
            method="GET",
            querystring=params,
            headers=headers,
        )

        if "auth_url" in response:
            result.update(response)
            result["auth_url"] = f"https://account.withings.com{response['auth_url']}"

        logger.info(f"Generated Withings authorization URL: {result['auth_url']}")

        return result

    # Removed unused methods

    async def get_token(self, code: str) -> Dict[str, Any]:
        """
        Get token using authorization code.

        This method handles rate limiting with status code 601 by implementing retry logic.
        """
        logger.info("Getting Withings token.")
        logger.debug(f"Code: {code}")

        # Note: The TODO below is part of the original codebase and not related to our rate limit implementation
        # TODO: add withings email linking with rpm email

        payload = {
            "action": "requesttoken",
            "grant_type": "authorization_code",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": code,
            "redirect_uri": self.redirect_uri,
        }

        headers = await self._generate_headers()
        response = await self._call_api_with_rate_limit_handling(
            url=f"{self.API_URL}/v2/oauth2",
            method="POST",
            headers=headers,
            payload=payload,
        )

        return response

    async def refresh_token(self, refresh_token: str) -> Optional[Dict[str, Any]]:
        """
        https://developer.withings.com/api-reference/#tag/oauth2/operation/oauth2-getaccesstoken
        https://wbsapi.withings.net/v2/oauth2
        Returns new token or None if there was an error.

        This method handles rate limiting with status code 601 by implementing retry logic.
        """
        base_params = {
            "action": "requesttoken",
            "grant_type": "refresh_token",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "refresh_token": refresh_token,
        }

        headers = await self._generate_headers()
        response = await self._call_api_with_rate_limit_handling(
            url=f"{self.API_URL}/v2/oauth2",
            method="POST",
            headers=headers,
            payload=base_params,
        )

        if "error" in response:
            logger.error("refreshing token failed: %s", response["error"])
            return None

        return response

    async def _notify(self, headers: dict, payload: dict) -> Dict[str, Any]:
        """Call withings notification configuration API with rate limit handling"""

        logger.info(
            f"{payload['action']} to {payload['appli']} events with callback {payload['callbackurl']}"
        )
        resp = await self._call_api_with_rate_limit_handling(
            url=f"{self.API_URL}/notify",
            method="POST",
            headers=headers,
            payload=payload,
        )

        return resp

    async def create_subscription(
        self,
        appli: str,
        access_token: str,
    ) -> dict:
        """
        Subscribe to a variety or events,
        including user data creation and updates depends on appli
        """
        payload = {
            "action": "subscribe",
            "appli": appli,
            "callbackurl": self.notification_callback_uri,
            "comment": "Subscribed to user device updates",
        }
        headers = self.get_auth_headers(access_token)

        return await self._notify(headers, payload)

    async def delete_subscription(
        self,
        appli: str,
        access_token: str,
    ) -> dict:
        """
        Disables all notifications for a given user.
        """
        payload = {
            "action": "revoke",
            "appli": appli,
            "callbackurl": self.notification_callback_uri,
        }
        headers = self.get_auth_headers(access_token)

        return await self._notify(headers, payload)
