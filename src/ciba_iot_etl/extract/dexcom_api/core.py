from ciba_iot_etl.common.api import BaseLoader
from ciba_iot_etl.helpers.crypto import base64_url_encode
from uuid import uuid4


class DexcomServiceException(Exception):
    """Base exception class for Dexcom service errors."""


class DexcomLoader(BaseLoader):
    OAUTH_URL = "https://sandbox-api.dexcom.com/v2/oauth2/login"
    API_URL = "https://sandbox-api.dexcom.com"
    TIMEOUT = 4.5
    SCOPE = "offline_access"

    def __init__(
        self,
        client_id: str,
        client_secret: str,
        redirect_uri: str,
    ):
        super().__init__(client_id, client_secret)
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri

    async def _generate_headers(self) -> str:
        """Generate headers for the request."""
        credentials = f"{self.client_id}:{self.client_secret}"
        return base64_url_encode(credentials.encode("utf-8"))

    async def _generate_unique_state(self) -> str:
        """Generate a unique 16-digit number from UUID."""
        return str(uuid4().int)[:16]

    async def get_auth_page_url(self):
        unique_state = await self._generate_unique_state()
        result = {
            "auth_url": "",
            "error": "",
            "state": unique_state,
        }
        params = {
            "client_id": self.client_id,
            "redirect_uri": self.redirect_uri.split("?")[0],
            "response_type": "code",
            "scope": self.SCOPE,
            "state": unique_state,
        }
        headers = await self._generate_headers()

        response = await self.call_api(
            url=self.OAUTH_URL,
            method="GET",
            headers={
                "Authorization": f"Basic {headers}",
                "Content-Type": "application/x-www-form-urlencoded",
            },
            querystring=params,
        )
        result.update(response)

        return result
